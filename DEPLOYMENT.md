# Dalti Admin Panel Deployment Guide

This guide covers the deployment of the Dalti Admin Panel to Kubernetes.

## Prerequisites

- Docker installed and configured
- kubectl configured with access to your Kubernetes cluster
- Access to the `kotoubm7` Docker Hub registry
- cert-manager installed in your cluster (for TLS certificates)
- nginx-ingress-controller installed in your cluster

## Quick Deployment

### 1. Deploy with Default Version

```bash
./deploy.sh
```

### 2. Deploy with Custom Version

```bash
./deploy.sh -v "v1.2.0"
```

### 3. Verify Deployment

```bash
./verify-deployment.sh
```

## Manual Deployment Steps

If you prefer to deploy manually or need to troubleshoot:

### 1. Build and Push Docker Image

```bash
# Set your version
VERSION="beta-1.0.0"

# Copy production environment
cp .env.production .env

# Install dependencies and build
npm ci --only=production
NODE_ENV=production npm run build

# Build and push Docker image
docker build -t kotoubm7/dalti-frontend:$VERSION -f Dockerfile.frontend .
docker push kotoubm7/dalti-frontend:$VERSION
```

### 2. Update Kubernetes Manifests

```bash
# Update the image tag in the deployment
sed -i 's|image: kotoubm7/dalti-frontend:.*|image: kotoubm7/dalti-frontend:'$VERSION'|g' k8s/12-frontend-deployment.yaml
```

### 3. Deploy to Kubernetes

```bash
# Apply all manifests
kubectl apply -f k8s/

# Or apply individually
kubectl apply -f k8s/11-frontend-nginx-conf.yaml
kubectl apply -f k8s/12-frontend-deployment.yaml
kubectl apply -f k8s/13-frontend-service.yaml
kubectl apply -f k8s/14-frontend-ingress.yaml
```

## Configuration

### Environment Variables

The application uses the following environment variables (configured in `.env.production`):

- `VITE_API_BASE_URL_PROD`: Production API URL (https://dapi.adscloud.org)
- `VITE_NODE_ENV`: Environment (production)
- `VITE_JWT_STORAGE_KEY`: JWT storage key
- `VITE_ENABLE_ANALYTICS`: Enable analytics
- `VITE_ENABLE_AUDIT_LOGS`: Enable audit logging

### Kubernetes Resources

- **Namespace**: `dalti`
- **ConfigMap**: `dalti-frontend-nginx-conf` (Nginx configuration)
- **Deployment**: `dalti-frontend` (1 replica)
- **Service**: `dalti-frontend-service` (ClusterIP)
- **Ingress**: `dalti-frontend-ingress` (with TLS)

### Domain and TLS

- **Domain**: dalti-admin.adscloud.org
- **TLS**: Managed by cert-manager with Let's Encrypt
- **Certificate Secret**: dalti-frontend-tls-secret

## Monitoring and Troubleshooting

### Check Deployment Status

```bash
kubectl get all -n dalti -l app=dalti-frontend
```

### View Logs

```bash
kubectl logs -n dalti -l app=dalti-frontend -f
```

### Check Events

```bash
kubectl get events -n dalti --sort-by='.lastTimestamp'
```

### Health Check

The application provides a health endpoint at `/health` that returns a 200 status.

```bash
curl https://dalti-admin.adscloud.org/health
```

### Common Issues

1. **Image Pull Errors**: Ensure Docker Hub credentials are configured
2. **TLS Certificate Issues**: Check cert-manager logs and certificate status
3. **Ingress Not Working**: Verify nginx-ingress-controller is running
4. **Pod Not Starting**: Check resource limits and node capacity

## Security Features

- TLS encryption with automatic certificate management
- Security headers (X-Frame-Options, X-Content-Type-Options, etc.)
- Content Security Policy configured
- No source maps in production builds
- Nginx serving static files with proper caching headers

## Rollback

To rollback to a previous version:

```bash
# Set the previous version
PREVIOUS_VERSION="beta-1.0.0"

# Update deployment
kubectl set image deployment/dalti-frontend dalti-frontend-nginx=kotoubm7/dalti-frontend:$PREVIOUS_VERSION -n dalti

# Check rollout status
kubectl rollout status deployment/dalti-frontend -n dalti
```

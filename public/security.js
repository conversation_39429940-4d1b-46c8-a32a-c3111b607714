/**
 * Security Configuration Script
 * Sets up Content Security Policy and other security headers
 * This script should be loaded before any other scripts
 */

(function() {
  'use strict';

  // Security configuration
  const SECURITY_CONFIG = {
    // Content Security Policy directives
    csp: {
      'default-src': ["'self'"],
      'script-src': [
        "'self'",
        "'unsafe-inline'", // Required for React development
        "'unsafe-eval'", // Required for React development
        'https://www.google-analytics.com',
        'https://www.googletagmanager.com'
      ],
      'style-src': [
        "'self'",
        "'unsafe-inline'", // Required for styled-components and CSS-in-JS
        'https://fonts.googleapis.com'
      ],
      'font-src': [
        "'self'",
        'https://fonts.gstatic.com',
        'data:'
      ],
      'img-src': [
        "'self'",
        'data:',
        'https:',
        'blob:'
      ],
      'connect-src': [
        "'self'",
        'https://dapi-test.adscloud.org:8443',
        'https://dapi.adscloud.org',
        'https://www.google-analytics.com',
        'wss://localhost:*', // WebSocket for development
        'ws://localhost:*'   // WebSocket for development
      ],
      'frame-src': [
        "'none'"
      ],
      'frame-ancestors': [
        "'none'"
      ],
      'base-uri': [
        "'self'"
      ],
      'form-action': [
        "'self'"
      ],
      'object-src': [
        "'none'"
      ],
      'media-src': [
        "'self'",
        'data:'
      ],
      'worker-src': [
        "'self'",
        'blob:'
      ],
      'manifest-src': [
        "'self'"
      ]
    },

    // Additional security headers
    headers: {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()'
    },

    // Feature policy restrictions
    featurePolicy: {
      'camera': "'none'",
      'microphone': "'none'",
      'geolocation': "'none'",
      'payment': "'none'",
      'usb': "'none'",
      'magnetometer': "'none'",
      'gyroscope': "'none'",
      'accelerometer': "'none'"
    }
  };

  // Apply Content Security Policy
  function applyCSP() {
    // Skip CSP in development if it causes issues
    if (window.location.hostname === 'localhost' && window.DISABLE_CSP) {
      console.warn('CSP disabled for development');
      return;
    }

    const cspString = Object.entries(SECURITY_CONFIG.csp)
      .map(([directive, sources]) => {
        if (sources.length === 0) return directive;
        return `${directive} ${sources.join(' ')}`;
      })
      .join('; ');

    // Create CSP meta tag
    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = cspString;
    
    // Insert at the beginning of head
    const head = document.head || document.getElementsByTagName('head')[0];
    head.insertBefore(meta, head.firstChild);

    console.log('CSP applied:', cspString);
  }

  // Apply additional security headers via meta tags
  function applySecurityHeaders() {
    Object.entries(SECURITY_CONFIG.headers).forEach(([header, value]) => {
      const meta = document.createElement('meta');
      meta.httpEquiv = header;
      meta.content = value;
      document.head.appendChild(meta);
    });
  }

  // Apply feature policy
  function applyFeaturePolicy() {
    const policyString = Object.entries(SECURITY_CONFIG.featurePolicy)
      .map(([feature, allowlist]) => `${feature}=${allowlist}`)
      .join(', ');

    const meta = document.createElement('meta');
    meta.httpEquiv = 'Permissions-Policy';
    meta.content = policyString;
    document.head.appendChild(meta);
  }

  // Security event reporting
  function setupSecurityReporting() {
    // CSP violation reporting
    document.addEventListener('securitypolicyviolation', function(event) {
      console.warn('CSP Violation:', {
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        originalPolicy: event.originalPolicy,
        documentURI: event.documentURI
      });

      // Report to security monitoring endpoint
      if (window.SECURITY_REPORT_ENDPOINT) {
        fetch(window.SECURITY_REPORT_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: 'csp_violation',
            blockedURI: event.blockedURI,
            violatedDirective: event.violatedDirective,
            documentURI: event.documentURI,
            timestamp: Date.now(),
            userAgent: navigator.userAgent
          })
        }).catch(function(error) {
          console.error('Failed to report CSP violation:', error);
        });
      }
    });

    // Report JS errors
    window.addEventListener('error', function(event) {
      console.error('JavaScript Error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });

      // Report critical errors
      if (window.SECURITY_REPORT_ENDPOINT && event.error) {
        fetch(window.SECURITY_REPORT_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: 'javascript_error',
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error.stack,
            timestamp: Date.now(),
            userAgent: navigator.userAgent
          })
        }).catch(function(error) {
          console.error('Failed to report JS error:', error);
        });
      }
    });

    // Report unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
      console.error('Unhandled Promise Rejection:', event.reason);

      if (window.SECURITY_REPORT_ENDPOINT) {
        fetch(window.SECURITY_REPORT_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: 'unhandled_rejection',
            reason: event.reason ? event.reason.toString() : 'Unknown',
            timestamp: Date.now(),
            userAgent: navigator.userAgent
          })
        }).catch(function(error) {
          console.error('Failed to report promise rejection:', error);
        });
      }
    });
  }

  // Prevent common attacks
  function setupAttackPrevention() {
    // Prevent clickjacking
    if (window.self !== window.top) {
      console.warn('Potential clickjacking attempt detected');
      window.top.location = window.self.location;
    }

    // Disable right-click in production
    if (window.location.hostname !== 'localhost' && !window.ENABLE_RIGHT_CLICK) {
      document.addEventListener('contextmenu', function(event) {
        event.preventDefault();
        return false;
      });
    }

    // Disable F12 and other developer shortcuts in production
    if (window.location.hostname !== 'localhost' && !window.ENABLE_DEV_TOOLS) {
      document.addEventListener('keydown', function(event) {
        // F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
        if (event.keyCode === 123 || 
            (event.ctrlKey && event.shiftKey && (event.keyCode === 73 || event.keyCode === 74)) ||
            (event.ctrlKey && event.keyCode === 85)) {
          event.preventDefault();
          return false;
        }
      });
    }

    // Detect developer tools
    let devtools = {
      open: false,
      orientation: null
    };

    const threshold = 160;

    setInterval(function() {
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        if (!devtools.open) {
          devtools.open = true;
          console.warn('Developer tools opened');
          
          if (window.SECURITY_REPORT_ENDPOINT && window.location.hostname !== 'localhost') {
            fetch(window.SECURITY_REPORT_ENDPOINT, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                type: 'devtools_opened',
                timestamp: Date.now(),
                userAgent: navigator.userAgent
              })
            }).catch(function() {
              // Ignore errors
            });
          }
        }
      } else {
        devtools.open = false;
      }
    }, 500);
  }

  // Initialize security measures
  function initializeSecurity() {
    try {
      applyCSP();
      applySecurityHeaders();
      applyFeaturePolicy();
      setupSecurityReporting();
      setupAttackPrevention();
      
      console.log('Security measures initialized');
    } catch (error) {
      console.error('Failed to initialize security measures:', error);
    }
  }

  // Run security initialization
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSecurity);
  } else {
    initializeSecurity();
  }

  // Expose security config for debugging in development
  if (window.location.hostname === 'localhost') {
    window.SECURITY_CONFIG = SECURITY_CONFIG;
  }

  // Security health check
  window.SECURITY_HEALTH_CHECK = function() {
    return {
      csp: !!document.querySelector('meta[http-equiv="Content-Security-Policy"]'),
      headers: Object.keys(SECURITY_CONFIG.headers).every(header => 
        !!document.querySelector(`meta[http-equiv="${header}"]`)
      ),
      featurePolicy: !!document.querySelector('meta[http-equiv="Permissions-Policy"]'),
      timestamp: Date.now()
    };
  };

})();

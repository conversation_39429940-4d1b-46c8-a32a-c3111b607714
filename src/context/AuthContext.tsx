import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { tokenManager } from '../utils/tokenManager';
import { AdminUser, AuthState } from '../types';
import { adminApi } from '../services/adminApi';
import toast from 'react-hot-toast';

// Auth Actions
type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: { admin: AdminUser; token: string } }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'CLEAR_ERROR' }
  | { type: 'RESTORE_SESSION'; payload: AdminUser }
  | { type: 'FETCH_USER_SUCCESS'; payload: AdminUser };

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  admin: null,
  token: null,
  refreshToken: null,
  isLoading: true, // Start with loading true to check existing session
  error: null,
};

// Auth reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'LOGIN_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        admin: action.payload.admin,
        token: action.payload.token,
        refreshToken: null,
        isLoading: false,
        error: null,
      };

    case 'LOGIN_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        admin: null,
        token: null,
        refreshToken: null,
        isLoading: false,
        error: action.payload,
      };

    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        admin: null,
        token: null,
        refreshToken: null,
        isLoading: false,
        error: null,
      };

    case 'FETCH_USER_SUCCESS':
      return {
        ...state,
        admin: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    case 'RESTORE_SESSION':
      return {
        ...state,
        isAuthenticated: true,
        admin: action.payload,
        token: tokenManager.getToken(),
        refreshToken: null,
        isLoading: false,
        error: null,
      };

    default:
      return state;
  }
}

// Auth context interface
interface AuthContextType {
  state: AuthState;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  fetchUserInfo: () => Promise<void>;
  clearError: () => void;
  checkAuthStatus: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // Check if user is authenticated and restore session
  const checkAuthStatus = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      if (tokenManager.isAuthenticated()) {
        // Fetch user info from /auth/me endpoint
        await fetchUserInfo();
        return;
      }

      // No valid session found
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      console.error('Error checking auth status:', error);
      // If fetching user info fails, clear tokens and set loading to false
      tokenManager.clearTokens();
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Fetch user info from /auth/me endpoint
  const fetchUserInfo = async () => {
    try {
      const response = await adminApi.auth.getProfile();

      if (response.success && response.data) {
        // The actual user data is in response.data.json based on the API response structure
        const userData = response.data.json || response.data;

        const admin: AdminUser = {
          id: userData.id || 'admin-1',
          email: userData.email,
          name: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || userData.username || userData.email.split('@')[0],
          role: userData.role || 'ADMIN',
          isAdmin: userData.isAdmin || true,
          createdAt: userData.createdAt || new Date().toISOString(),
          lastLoginAt: new Date().toISOString(), // Set current time as last login
        };

        dispatch({ type: 'FETCH_USER_SUCCESS', payload: admin });
      } else {
        throw new Error(response.message || 'Failed to fetch user info');
      }
    } catch (error) {
      console.error('Error fetching user info:', error);
      throw error;
    }
  };

  // Login function
  const login = async (email: string, password: string) => {
    try {
      dispatch({ type: 'LOGIN_START' });

      const baseUrl = import.meta.env.VITE_NODE_ENV === 'production' 
        ? import.meta.env.VITE_API_BASE_URL_PROD 
        : import.meta.env.VITE_API_BASE_URL_DEV;

      const response = await fetch(`${baseUrl}/api/auth/admin/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Login failed');
      }

      const res = await response.json();

      if (res.sessionId) {
        // Store token using token manager (no refresh token needed)
        tokenManager.setTokens({
          token: res.sessionId,
        });
        const result = {data: { admin: res.user }}
        // Create admin user object
        const admin: AdminUser = {
          id: result.data.admin?.id || 'admin-1',
          email: result.data.admin?.email || email,
          name: result.data.admin?.firstName+' '+result.data.admin?.lastName || email.split('@')[0],
          role: 'ADMIN',
          isAdmin: true,
          createdAt: result.data.admin?.createdAt || new Date().toISOString(),
          lastLoginAt: new Date().toISOString(),
        };

        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: {
            admin,
            token: res.sessionId,
          },
        });

        toast.success(`Welcome back, ${admin.name}!`);
      } else {
        throw new Error(res?.message || 'Login failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Logout function
  const logout = () => {
    tokenManager.clearTokens();
    dispatch({ type: 'LOGOUT' });
    toast.success('Logged out successfully');
  };



  // Clear error function
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const contextValue: AuthContextType = {
    state,
    login,
    logout,
    fetchUserInfo,
    clearError,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Helper hooks for common auth operations
export function useAuthState() {
  const { state } = useAuth();
  return state;
}

export function useAuthActions() {
  const { login, logout, fetchUserInfo, clearError, checkAuthStatus } = useAuth();
  return { login, logout, fetchUserInfo, clearError, checkAuthStatus };
}

export function useAdminUser() {
  const { state } = useAuth();
  return state.admin;
}

export function useIsAuthenticated() {
  const { state } = useAuth();
  return state.isAuthenticated;
}

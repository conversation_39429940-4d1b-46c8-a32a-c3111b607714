import { useCallback, useEffect, useRef, useState } from "react";
import { Link, useLocation } from "react-router";
import <PERSON><PERSON><PERSON><PERSON> from "../components/branding/DaltiLogo";

// Dalti-specific icons for admin panel
import {
  DashboardIcon,
  ProvidersIcon,
  CustomersIcon,
  CategoriesIcon,
  BookingsIcon,
  AnalyticsIcon,
  SystemAdminIcon,
  ReportsIcon,
  AdvertisementIcon,
  ChevronDownIcon,
  MenuDotsIcon,
} from "../components/icons/DaltiIcons";
import { useSidebar } from "../context/SidebarContext";
import SidebarWidget from "./SidebarWidget";

type NavItem = {
  name: string;
  icon: React.ReactNode;
  path?: string;
  subItems?: { name: string; path: string; pro?: boolean; new?: boolean }[];
};

const navItems: NavItem[] = [
  {
    icon: <DashboardIcon />,
    name: "Dashboard",
    path: "/",
  },
  {
    icon: <ProvidersIcon />,
    name: "Providers",
    subItems: [
      { name: "All Providers", path: "/providers", pro: false },
      { name: "Pending Approval", path: "/providers/pending", pro: false },
      { name: "Verified Providers", path: "/providers/verified", pro: false },
      { name: "Provider Analytics", path: "/providers/analytics", pro: false },
    ],
  },
  {
    icon: <CustomersIcon />,
    name: "Customers",
    subItems: [
      { name: "All Customers", path: "/customers", pro: false },
      { name: "Active Customers", path: "/customers/active", pro: false },
      { name: "Customer Analytics", path: "/customers/analytics", pro: false },
      { name: "Support Tools", path: "/customers/support", pro: false },
    ],
  },
  {
    icon: <CategoriesIcon />,
    name: "Categories",
    subItems: [
      { name: "Category Tree", path: "/categories", pro: false },
      { name: "Category Analytics", path: "/categories/analytics", pro: false },
    ],
  },
  {
    icon: <AdvertisementIcon />,
    name: "Advertisements",
    subItems: [
      { name: "All Advertisements", path: "/advertisements", pro: false },
      { name: "Create Advertisement", path: "/advertisements/create", pro: false },
    ],
  },
  {
    icon: <BookingsIcon />,
    name: "Bookings",
    subItems: [
      { name: "All Bookings", path: "/bookings", pro: false },
      { name: "Recent Bookings", path: "/bookings/recent", pro: false },
      { name: "Booking Analytics", path: "/bookings/analytics", pro: false },
    ],
  },
];

const othersItems: NavItem[] = [
  {
    icon: <AnalyticsIcon />,
    name: "Analytics",
    subItems: [
      { name: "Platform Overview", path: "/analytics", pro: false },
      { name: "Revenue Reports", path: "/analytics/revenue", pro: false },
      { name: "Growth Metrics", path: "/analytics/growth", pro: false },
      { name: "Performance Reports", path: "/analytics/performance", pro: false },
    ],
  },
  {
    icon: <SystemAdminIcon />,
    name: "System Admin",
    subItems: [
      { name: "Platform Settings", path: "/admin/settings", pro: false },
      { name: "Admin Users", path: "/admin/users", pro: false },
      { name: "Audit Logs", path: "/admin/audit", pro: false },
      { name: "System Monitoring", path: "/admin/monitoring", pro: false },
      { name: "Maintenance", path: "/admin/maintenance", pro: false },
    ],
  },
  {
    icon: <ReportsIcon />,
    name: "Reports",
    subItems: [
      { name: "Provider Reports", path: "/reports/providers", pro: false },
      { name: "Customer Reports", path: "/reports/customers", pro: false },
      { name: "Financial Reports", path: "/reports/financial", pro: false },
      { name: "System Reports", path: "/reports/system", pro: false },
    ],
  },
];

const AppSidebar: React.FC = () => {
  const { isExpanded, isMobileOpen, isHovered, setIsHovered } = useSidebar();
  const location = useLocation();

  const [openSubmenu, setOpenSubmenu] = useState<{
    type: "main" | "others";
    index: number;
  } | null>(null);
  const [subMenuHeight, setSubMenuHeight] = useState<Record<string, number>>(
    {}
  );
  const subMenuRefs = useRef<Record<string, HTMLDivElement | null>>({});

  // const isActive = (path: string) => location.pathname === path;
  const isActive = useCallback(
    (path: string) => location.pathname === path,
    [location.pathname]
  );

  useEffect(() => {
    let submenuMatched = false;
    ["main", "others"].forEach((menuType) => {
      const items = menuType === "main" ? navItems : othersItems;
      items.forEach((nav, index) => {
        if (nav.subItems) {
          nav.subItems.forEach((subItem) => {
            if (isActive(subItem.path)) {
              setOpenSubmenu({
                type: menuType as "main" | "others",
                index,
              });
              submenuMatched = true;
            }
          });
        }
      });
    });

    if (!submenuMatched) {
      setOpenSubmenu(null);
    }
  }, [location, isActive]);

  useEffect(() => {
    if (openSubmenu !== null) {
      const key = `${openSubmenu.type}-${openSubmenu.index}`;
      if (subMenuRefs.current[key]) {
        setSubMenuHeight((prevHeights) => ({
          ...prevHeights,
          [key]: subMenuRefs.current[key]?.scrollHeight || 0,
        }));
      }
    }
  }, [openSubmenu]);

  const handleSubmenuToggle = (index: number, menuType: "main" | "others") => {
    setOpenSubmenu((prevOpenSubmenu) => {
      if (
        prevOpenSubmenu &&
        prevOpenSubmenu.type === menuType &&
        prevOpenSubmenu.index === index
      ) {
        return null;
      }
      return { type: menuType, index };
    });
  };

  const renderMenuItems = (items: NavItem[], menuType: "main" | "others") => (
    <ul className="flex flex-col gap-4">
      {items.map((nav, index) => (
        <li key={nav.name}>
          {nav.subItems ? (
            <button
              onClick={() => handleSubmenuToggle(index, menuType)}
              className={`menu-item group ${
                openSubmenu?.type === menuType && openSubmenu?.index === index
                  ? "menu-item-active"
                  : "menu-item-inactive"
              } cursor-pointer ${
                !isExpanded && !isHovered
                  ? "lg:justify-center"
                  : "lg:justify-start"
              }`}
            >
              <span
                className={`menu-item-icon-size  ${
                  openSubmenu?.type === menuType && openSubmenu?.index === index
                    ? "menu-item-icon-active"
                    : "menu-item-icon-inactive"
                }`}
              >
                {nav.icon}
              </span>
              {(isExpanded || isHovered || isMobileOpen) && (
                <span className="menu-item-text">{nav.name}</span>
              )}
              {(isExpanded || isHovered || isMobileOpen) && (
                <ChevronDownIcon
                  className={`ml-auto w-5 h-5 transition-transform duration-200 ${
                    openSubmenu?.type === menuType &&
                    openSubmenu?.index === index
                      ? "rotate-180 text-brand-500"
                      : ""
                  }`}
                />
              )}
            </button>
          ) : (
            nav.path && (
              <Link
                to={nav.path}
                className={`menu-item group ${
                  isActive(nav.path) ? "menu-item-active" : "menu-item-inactive"
                }`}
              >
                <span
                  className={`menu-item-icon-size ${
                    isActive(nav.path)
                      ? "menu-item-icon-active"
                      : "menu-item-icon-inactive"
                  }`}
                >
                  {nav.icon}
                </span>
                {(isExpanded || isHovered || isMobileOpen) && (
                  <span className="menu-item-text">{nav.name}</span>
                )}
              </Link>
            )
          )}
          {nav.subItems && (isExpanded || isHovered || isMobileOpen) && (
            <div
              ref={(el) => {
                subMenuRefs.current[`${menuType}-${index}`] = el;
              }}
              className="overflow-hidden transition-all duration-300"
              style={{
                height:
                  openSubmenu?.type === menuType && openSubmenu?.index === index
                    ? `${subMenuHeight[`${menuType}-${index}`]}px`
                    : "0px",
              }}
            >
              <ul className="mt-2 space-y-1 ml-9">
                {nav.subItems.map((subItem) => (
                  <li key={subItem.name}>
                    <Link
                      to={subItem.path}
                      className={`menu-dropdown-item ${
                        isActive(subItem.path)
                          ? "menu-dropdown-item-active"
                          : "menu-dropdown-item-inactive"
                      }`}
                    >
                      {subItem.name}
                      <span className="flex items-center gap-1 ml-auto">
                        {subItem.new && (
                          <span
                            className={`ml-auto ${
                              isActive(subItem.path)
                                ? "menu-dropdown-badge-active"
                                : "menu-dropdown-badge-inactive"
                            } menu-dropdown-badge`}
                          >
                            new
                          </span>
                        )}
                        {subItem.pro && (
                          <span
                            className={`ml-auto ${
                              isActive(subItem.path)
                                ? "menu-dropdown-badge-active"
                                : "menu-dropdown-badge-inactive"
                            } menu-dropdown-badge`}
                          >
                            pro
                          </span>
                        )}
                      </span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </li>
      ))}
    </ul>
  );

  return (
    <aside
      className={`fixed mt-16 flex flex-col lg:mt-0 top-0 px-5 left-0 bg-white dark:bg-gray-900 dark:border-gray-800 text-gray-900 h-screen transition-all duration-300 ease-in-out z-50 border-r border-gray-200 
        ${
          isExpanded || isMobileOpen
            ? "w-[290px]"
            : isHovered
            ? "w-[290px]"
            : "w-[90px]"
        }
        ${isMobileOpen ? "translate-x-0" : "-translate-x-full"}
        lg:translate-x-0`}
      onMouseEnter={() => !isExpanded && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={`py-8 flex ${
          !isExpanded && !isHovered ? "lg:justify-center" : "justify-start"
        }`}
      >
        <Link to="/">
          {isExpanded || isHovered || isMobileOpen ? (
            <DaltiLogo size="lg" showAdmin={true} />
          ) : (
            <DaltiLogo variant="icon" size="md" />
          )}
        </Link>
      </div>
      <div className="flex flex-col overflow-y-auto duration-300 ease-linear no-scrollbar">
        <nav className="mb-6">
          <div className="flex flex-col gap-4">
            <div>
              <h2
                className={`mb-4 text-xs uppercase flex leading-[20px] text-gray-400 ${
                  !isExpanded && !isHovered
                    ? "lg:justify-center"
                    : "justify-start"
                }`}
              >
                {isExpanded || isHovered || isMobileOpen ? (
                  "Menu"
                ) : (
                  <MenuDotsIcon className="size-6" />
                )}
              </h2>
              {renderMenuItems(navItems, "main")}
            </div>
            <div className="">
              <h2
                className={`mb-4 text-xs uppercase flex leading-[20px] text-gray-400 ${
                  !isExpanded && !isHovered
                    ? "lg:justify-center"
                    : "justify-start"
                }`}
              >
                {isExpanded || isHovered || isMobileOpen ? (
                  "Others"
                ) : (
                  <MenuDotsIcon />
                )}
              </h2>
              {renderMenuItems(othersItems, "others")}
            </div>
          </div>
        </nav>
        {/* {isExpanded || isHovered || isMobileOpen ? <SidebarWidget /> : null} */}
      </div>
    </aside>
  );
};

export default AppSidebar;

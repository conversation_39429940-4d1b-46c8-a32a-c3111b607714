/**
 * Mock Service Worker (MSW) Server Setup
 * Provides API mocking for tests
 */

import { setupServer } from 'msw/node';
import { rest } from 'msw';
import { factories } from '../utils/testUtils';

// API Base URL
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api';

// Mock handlers
export const handlers = [
  // Authentication endpoints
  rest.post(`${API_BASE_URL}/admin/login`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(factories.auth.createAuthResponse())
    );
  }),

  rest.post(`${API_BASE_URL}/admin/logout`, (req, res, ctx) => {
    return res(ctx.status(200), ctx.json({ message: 'Logged out successfully' }));
  }),

  rest.get(`${API_BASE_URL}/auth/me`, (req, res, ctx) => {
    const token = req.headers.get('Authorization');
    if (!token) {
      return res(ctx.status(401), ctx.json({ message: 'Unauthorized' }));
    }
    return res(ctx.status(200), ctx.json(factories.auth.createAuthUser()));
  }),

  // Provider endpoints
  rest.get(`${API_BASE_URL}/admin/providers`, (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 1;
    const limit = Number(req.url.searchParams.get('limit')) || 20;
    const search = req.url.searchParams.get('search') || '';
    const status = req.url.searchParams.get('status') || '';
    
    let providers = factories.provider.createProviderList(50);
    
    // Apply filters
    if (search) {
      providers = providers.filter(p => 
        p.name.toLowerCase().includes(search.toLowerCase()) ||
        p.email.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    if (status) {
      providers = providers.filter(p => p.status === status);
    }
    
    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProviders = providers.slice(startIndex, endIndex);
    
    return res(
      ctx.status(200),
      ctx.json(factories.api.paginated(paginatedProviders, page, limit))
    );
  }),

  rest.get(`${API_BASE_URL}/admin/providers/:id`, (req, res, ctx) => {
    const { id } = req.params;
    const provider = factories.provider.createProvider({ id });
    return res(ctx.status(200), ctx.json(provider));
  }),

  rest.put(`${API_BASE_URL}/admin/providers/:id/approve`, (req, res, ctx) => {
    const { id } = req.params;
    const provider = factories.provider.createProvider({ 
      id, 
      status: 'approved',
      verified: true 
    });
    return res(ctx.status(200), ctx.json(provider));
  }),

  rest.put(`${API_BASE_URL}/admin/providers/:id/reject`, (req, res, ctx) => {
    const { id } = req.params;
    const provider = factories.provider.createProvider({ 
      id, 
      status: 'rejected' 
    });
    return res(ctx.status(200), ctx.json(provider));
  }),

  // Customer endpoints
  rest.get(`${API_BASE_URL}/admin/customers`, (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 1;
    const limit = Number(req.url.searchParams.get('limit')) || 20;
    const search = req.url.searchParams.get('search') || '';
    
    let customers = factories.customer.createCustomerList(30);
    
    // Apply search filter
    if (search) {
      customers = customers.filter(c => 
        c.firstName.toLowerCase().includes(search.toLowerCase()) ||
        c.lastName.toLowerCase().includes(search.toLowerCase()) ||
        c.email.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCustomers = customers.slice(startIndex, endIndex);
    
    return res(
      ctx.status(200),
      ctx.json(factories.api.paginated(paginatedCustomers, page, limit))
    );
  }),

  rest.get(`${API_BASE_URL}/admin/customers/:id`, (req, res, ctx) => {
    const { id } = req.params;
    const customer = factories.customer.createCustomer({ id });
    return res(ctx.status(200), ctx.json(customer));
  }),

  // Category endpoints
  rest.get(`${API_BASE_URL}/admin/categories`, (req, res, ctx) => {
    const categories = factories.category.createCategoryTree();
    return res(ctx.status(200), ctx.json(categories));
  }),

  rest.get(`${API_BASE_URL}/admin/categories/:id`, (req, res, ctx) => {
    const { id } = req.params;
    const category = factories.category.createCategory({ id });
    return res(ctx.status(200), ctx.json(category));
  }),

  rest.post(`${API_BASE_URL}/admin/categories`, (req, res, ctx) => {
    const newCategory = factories.category.createCategory({
      id: 'new-category-id',
      ...req.body,
    });
    return res(ctx.status(201), ctx.json(newCategory));
  }),

  rest.put(`${API_BASE_URL}/admin/categories/:id`, (req, res, ctx) => {
    const { id } = req.params;
    const updatedCategory = factories.category.createCategory({
      id,
      ...req.body,
    });
    return res(ctx.status(200), ctx.json(updatedCategory));
  }),

  rest.delete(`${API_BASE_URL}/admin/categories/:id`, (req, res, ctx) => {
    return res(ctx.status(200), ctx.json({ message: 'Category deleted successfully' }));
  }),

  // Dashboard/Analytics endpoints
  rest.get(`${API_BASE_URL}/admin/dashboard/stats`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        totalProviders: 2847,
        activeCustomers: 18392,
        totalBookings: 45621,
        revenue: 284750,
        pendingApprovals: 23,
        systemHealth: 'healthy',
      })
    );
  }),

  rest.get(`${API_BASE_URL}/admin/dashboard/activity`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          id: '1',
          type: 'provider_registration',
          title: 'New Provider Registration',
          description: 'Dr. Sarah Johnson registered as a healthcare provider',
          timestamp: new Date().toISOString(),
          status: 'pending',
        },
        {
          id: '2',
          type: 'booking_completed',
          title: 'Booking Completed',
          description: 'Customer John Doe completed appointment with Dr. Smith',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          status: 'completed',
        },
      ])
    );
  }),

  // Admin user endpoints
  rest.get(`${API_BASE_URL}/admin/users`, (req, res, ctx) => {
    const adminUsers = [
      factories.auth.createAuthUser({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'super_admin',
      }),
      factories.auth.createAuthUser({
        id: 'admin-2',
        email: '<EMAIL>',
        role: 'admin',
      }),
    ];
    return res(ctx.status(200), ctx.json(adminUsers));
  }),

  // System settings endpoints
  rest.get(`${API_BASE_URL}/admin/settings`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        siteName: 'Dalti Admin Panel',
        maintenanceMode: false,
        registrationEnabled: true,
        emailNotifications: true,
        smsNotifications: false,
        maxFileSize: 10485760, // 10MB
        allowedFileTypes: ['jpg', 'jpeg', 'png', 'pdf'],
      })
    );
  }),

  rest.put(`${API_BASE_URL}/admin/settings`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        message: 'Settings updated successfully',
        settings: req.body,
      })
    );
  }),

  // Audit log endpoints
  rest.get(`${API_BASE_URL}/admin/audit-logs`, (req, res, ctx) => {
    const logs = [
      {
        id: 'log-1',
        action: 'provider_approved',
        adminId: 'admin-1',
        adminEmail: '<EMAIL>',
        targetId: 'provider-1',
        targetType: 'provider',
        details: 'Approved provider registration',
        timestamp: new Date().toISOString(),
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0...',
      },
    ];
    
    return res(
      ctx.status(200),
      ctx.json(factories.api.paginated(logs, 1, 20))
    );
  }),

  // Error handlers for testing error states
  rest.get(`${API_BASE_URL}/admin/error-test`, (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ message: 'Internal server error' }));
  }),

  rest.get(`${API_BASE_URL}/admin/not-found-test`, (req, res, ctx) => {
    return res(ctx.status(404), ctx.json({ message: 'Resource not found' }));
  }),

  rest.get(`${API_BASE_URL}/admin/unauthorized-test`, (req, res, ctx) => {
    return res(ctx.status(401), ctx.json({ message: 'Unauthorized' }));
  }),
];

// Create server instance
export const server = setupServer(...handlers);

// Export individual handlers for test customization
export { rest };

// Helper functions for test-specific mocking
export const mockHandlers = {
  // Mock successful login
  mockSuccessfulLogin: (user = factories.auth.createAuthUser()) => {
    server.use(
      rest.post(`${API_BASE_URL}/admin/login`, (req, res, ctx) => {
        return res(
          ctx.status(200),
          ctx.json(factories.auth.createAuthResponse({ user }))
        );
      })
    );
  },

  // Mock failed login
  mockFailedLogin: (message = 'Invalid credentials') => {
    server.use(
      rest.post(`${API_BASE_URL}/admin/login`, (req, res, ctx) => {
        return res(
          ctx.status(401),
          ctx.json({ message })
        );
      })
    );
  },

  // Mock network error
  mockNetworkError: (endpoint: string) => {
    server.use(
      rest.get(`${API_BASE_URL}${endpoint}`, (req, res, ctx) => {
        return res.networkError('Network error');
      })
    );
  },

  // Mock slow response
  mockSlowResponse: (endpoint: string, delay = 2000) => {
    server.use(
      rest.get(`${API_BASE_URL}${endpoint}`, (req, res, ctx) => {
        return res(
          ctx.delay(delay),
          ctx.status(200),
          ctx.json({ message: 'Slow response' })
        );
      })
    );
  },

  // Reset to default handlers
  resetHandlers: () => {
    server.resetHandlers(...handlers);
  },
};

import React, { createContext, useContext, useEffect, useState } from 'react';

export type Theme = 'light' | 'dark' | 'system';
export type ResolvedTheme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  resolvedTheme: ResolvedTheme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isDark: boolean;
  isLight: boolean;
  systemPreference: ResolvedTheme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'dalti-admin-theme',
}: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [systemPreference, setSystemPreference] = useState<ResolvedTheme>('light');
  const [mounted, setMounted] = useState(false);

  // Get system preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemPreference(mediaQuery.matches ? 'dark' : 'light');

    const handleChange = (e: MediaQueryListEvent) => {
      setSystemPreference(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Load theme from storage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored && ['light', 'dark', 'system'].includes(stored)) {
        setThemeState(stored as Theme);
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error);
    }
    setMounted(true);
  }, [storageKey]);

  // Calculate resolved theme
  const resolvedTheme: ResolvedTheme = theme === 'system' ? systemPreference : theme;

  // Apply theme to document
  useEffect(() => {
    if (!mounted) return;

    const root = document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('light', 'dark');
    
    // Add current theme class
    root.classList.add(resolvedTheme);
    
    // Set data attribute for CSS selectors
    root.setAttribute('data-theme', resolvedTheme);
    
    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        'content',
        resolvedTheme === 'dark' ? '#1f2937' : '#ffffff'
      );
    }
  }, [resolvedTheme, mounted]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    try {
      localStorage.setItem(storageKey, newTheme);
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
    }
  };

  const toggleTheme = () => {
    if (theme === 'system') {
      setTheme(systemPreference === 'dark' ? 'light' : 'dark');
    } else {
      setTheme(theme === 'dark' ? 'light' : 'dark');
    }
  };

  const value: ThemeContextType = {
    theme,
    resolvedTheme,
    setTheme,
    toggleTheme,
    isDark: resolvedTheme === 'dark',
    isLight: resolvedTheme === 'light',
    systemPreference,
  };

  // Prevent flash of wrong theme
  if (!mounted) {
    return null;
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Theme-aware component wrapper
interface ThemeAwareProps {
  children: React.ReactNode;
  lightClass?: string;
  darkClass?: string;
  className?: string;
}

export function ThemeAware({
  children,
  lightClass = '',
  darkClass = '',
  className = '',
}: ThemeAwareProps) {
  const { isDark } = useTheme();
  
  const themeClass = isDark ? darkClass : lightClass;
  const combinedClass = [className, themeClass].filter(Boolean).join(' ');

  return (
    <div className={combinedClass}>
      {children}
    </div>
  );
}

// Theme transition component
interface ThemeTransitionProps {
  children: React.ReactNode;
  duration?: number;
}

export function ThemeTransition({
  children,
  duration = 200,
}: ThemeTransitionProps) {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const { resolvedTheme } = useTheme();

  useEffect(() => {
    setIsTransitioning(true);
    const timer = setTimeout(() => setIsTransitioning(false), duration);
    return () => clearTimeout(timer);
  }, [resolvedTheme, duration]);

  return (
    <div
      className={`transition-colors duration-${duration} ${
        isTransitioning ? 'transition-all' : ''
      }`}
      style={{
        transitionDuration: `${duration}ms`,
      }}
    >
      {children}
    </div>
  );
}

// Hook for theme-aware styles
export function useThemeStyles() {
  const { isDark, isLight } = useTheme();

  const getThemeClass = (lightClass: string, darkClass: string) => {
    return isDark ? darkClass : lightClass;
  };

  const getThemeValue = <T,>(lightValue: T, darkValue: T): T => {
    return isDark ? darkValue : lightValue;
  };

  const themeClasses = {
    // Background classes
    bg: {
      primary: getThemeClass('bg-white', 'bg-gray-900'),
      secondary: getThemeClass('bg-gray-50', 'bg-gray-800'),
      tertiary: getThemeClass('bg-gray-100', 'bg-gray-700'),
      card: getThemeClass('bg-white', 'bg-gray-800'),
      overlay: getThemeClass('bg-white/80', 'bg-gray-900/80'),
    },
    
    // Text classes
    text: {
      primary: getThemeClass('text-gray-900', 'text-white'),
      secondary: getThemeClass('text-gray-600', 'text-gray-300'),
      tertiary: getThemeClass('text-gray-500', 'text-gray-400'),
      muted: getThemeClass('text-gray-400', 'text-gray-500'),
      inverse: getThemeClass('text-white', 'text-gray-900'),
    },
    
    // Border classes
    border: {
      primary: getThemeClass('border-gray-200', 'border-gray-700'),
      secondary: getThemeClass('border-gray-300', 'border-gray-600'),
      light: getThemeClass('border-gray-100', 'border-gray-800'),
    },
    
    // Interactive classes
    interactive: {
      hover: getThemeClass('hover:bg-gray-50', 'hover:bg-gray-700'),
      focus: 'focus:ring-2 focus:ring-brand-500 focus:ring-offset-2',
      active: getThemeClass('active:bg-gray-100', 'active:bg-gray-600'),
    },
  };

  return {
    isDark,
    isLight,
    getThemeClass,
    getThemeValue,
    themeClasses,
  };
}

// Theme-aware CSS variables
export const themeVariables = {
  light: {
    '--theme-bg-primary': '#ffffff',
    '--theme-bg-secondary': '#f9fafb',
    '--theme-bg-tertiary': '#f3f4f6',
    '--theme-text-primary': '#111827',
    '--theme-text-secondary': '#6b7280',
    '--theme-text-tertiary': '#9ca3af',
    '--theme-border-primary': '#e5e7eb',
    '--theme-border-secondary': '#d1d5db',
    '--theme-shadow': '0 1px 3px 0 rgb(0 0 0 / 0.1)',
  },
  dark: {
    '--theme-bg-primary': '#111827',
    '--theme-bg-secondary': '#1f2937',
    '--theme-bg-tertiary': '#374151',
    '--theme-text-primary': '#f9fafb',
    '--theme-text-secondary': '#d1d5db',
    '--theme-text-tertiary': '#9ca3af',
    '--theme-border-primary': '#374151',
    '--theme-border-secondary': '#4b5563',
    '--theme-shadow': '0 1px 3px 0 rgb(0 0 0 / 0.3)',
  },
} as const;

// Apply theme variables to document
export function applyThemeVariables(theme: ResolvedTheme) {
  const variables = themeVariables[theme];
  const root = document.documentElement;
  
  Object.entries(variables).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
}

// Theme persistence utilities
export const themeUtils = {
  getStoredTheme: (storageKey: string = 'dalti-admin-theme'): Theme | null => {
    try {
      const stored = localStorage.getItem(storageKey);
      return stored && ['light', 'dark', 'system'].includes(stored) ? (stored as Theme) : null;
    } catch {
      return null;
    }
  },
  
  setStoredTheme: (theme: Theme, storageKey: string = 'dalti-admin-theme'): void => {
    try {
      localStorage.setItem(storageKey, theme);
    } catch (error) {
      console.warn('Failed to store theme:', error);
    }
  },
  
  getSystemPreference: (): ResolvedTheme => {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  },
  
  watchSystemPreference: (callback: (theme: ResolvedTheme) => void): (() => void) => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handler = (e: MediaQueryListEvent) => callback(e.matches ? 'dark' : 'light');
    
    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  },
};

export default ThemeContext;

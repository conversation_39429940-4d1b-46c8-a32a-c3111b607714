import React from 'react';

/**
 * Responsive Design Utilities for Dalti Admin Panel
 * Provides utilities for responsive design, breakpoint management, and device detection
 */

// Tailwind CSS Breakpoints
export const breakpoints = {
  sm: 640,   // Small devices (landscape phones, 640px and up)
  md: 768,   // Medium devices (tablets, 768px and up)
  lg: 1024,  // Large devices (desktops, 1024px and up)
  xl: 1280,  // Extra large devices (large desktops, 1280px and up)
  '2xl': 1536, // 2X Extra large devices (larger desktops, 1536px and up)
} as const;

export type Breakpoint = keyof typeof breakpoints;

/**
 * Hook to get current screen size and breakpoint
 */
export const useScreenSize = () => {
  const [screenSize, setScreenSize] = React.useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
  });

  React.useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const getCurrentBreakpoint = (): Breakpoint => {
    const width = screenSize.width;
    if (width >= breakpoints['2xl']) return '2xl';
    if (width >= breakpoints.xl) return 'xl';
    if (width >= breakpoints.lg) return 'lg';
    if (width >= breakpoints.md) return 'md';
    return 'sm';
  };

  const isBreakpoint = (breakpoint: Breakpoint): boolean => {
    return screenSize.width >= breakpoints[breakpoint];
  };

  const isMobile = screenSize.width < breakpoints.md;
  const isTablet = screenSize.width >= breakpoints.md && screenSize.width < breakpoints.lg;
  const isDesktop = screenSize.width >= breakpoints.lg;

  return {
    ...screenSize,
    breakpoint: getCurrentBreakpoint(),
    isBreakpoint,
    isMobile,
    isTablet,
    isDesktop,
  };
};

/**
 * Responsive grid configurations
 */
export const responsiveGrids = {
  // Dashboard cards
  dashboardCards: {
    sm: 'grid-cols-1',
    md: 'grid-cols-2',
    lg: 'grid-cols-3',
    xl: 'grid-cols-4',
  },
  
  // Data tables
  tableColumns: {
    sm: 'grid-cols-1',
    md: 'grid-cols-2',
    lg: 'grid-cols-3',
    xl: 'grid-cols-4',
  },
  
  // Form layouts
  formFields: {
    sm: 'grid-cols-1',
    md: 'grid-cols-2',
    lg: 'grid-cols-3',
  },
  
  // Analytics charts
  analyticsCharts: {
    sm: 'grid-cols-1',
    md: 'grid-cols-1',
    lg: 'grid-cols-2',
    xl: 'grid-cols-2',
  },
  
  // Provider/Customer cards
  entityCards: {
    sm: 'grid-cols-1',
    md: 'grid-cols-2',
    lg: 'grid-cols-3',
    xl: 'grid-cols-4',
  },
} as const;

/**
 * Responsive spacing configurations
 */
export const responsiveSpacing = {
  container: {
    sm: 'px-4',
    md: 'px-6',
    lg: 'px-8',
  },
  
  section: {
    sm: 'py-4',
    md: 'py-6',
    lg: 'py-8',
  },
  
  card: {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  },
} as const;

/**
 * Responsive text sizes
 */
export const responsiveText = {
  heading1: {
    sm: 'text-2xl',
    md: 'text-3xl',
    lg: 'text-4xl',
  },
  
  heading2: {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl',
  },
  
  heading3: {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
  },
  
  body: {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-base',
  },
  
  caption: {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-sm',
  },
} as const;

/**
 * Generate responsive class string
 */
export const getResponsiveClasses = (
  config: Record<string, string>,
  prefix: string = ''
): string => {
  return Object.entries(config)
    .map(([breakpoint, className]) => {
      if (breakpoint === 'sm') {
        return className;
      }
      return `${breakpoint}:${className}`;
    })
    .join(' ');
};

/**
 * Mobile-first responsive utilities
 */
export const responsive = {
  // Hide on mobile, show on desktop
  hideOnMobile: 'hidden md:block',
  
  // Show on mobile, hide on desktop
  showOnMobile: 'block md:hidden',
  
  // Stack on mobile, row on desktop
  stackOnMobile: 'flex flex-col md:flex-row',
  
  // Full width on mobile, auto on desktop
  fullWidthOnMobile: 'w-full md:w-auto',
  
  // Center on mobile, left align on desktop
  centerOnMobile: 'text-center md:text-left',
  
  // Smaller padding on mobile
  paddingResponsive: 'p-4 md:p-6 lg:p-8',
  
  // Responsive margins
  marginResponsive: 'm-4 md:m-6 lg:m-8',
  
  // Responsive gaps
  gapResponsive: 'gap-4 md:gap-6 lg:gap-8',
} as const;

/**
 * Table responsive configurations
 */
export const tableResponsive = {
  // Hide columns on smaller screens
  hideOnMobile: 'hidden md:table-cell',
  hideOnTablet: 'hidden lg:table-cell',
  
  // Show only on mobile
  showOnMobile: 'table-cell md:hidden',
  
  // Responsive table container
  container: 'overflow-x-auto',
  
  // Mobile card layout for tables
  mobileCard: 'block md:hidden bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-4',
  
  // Desktop table layout
  desktopTable: 'hidden md:table',
} as const;

/**
 * Form responsive configurations
 */
export const formResponsive = {
  // Form container
  container: 'space-y-4 md:space-y-6',
  
  // Form grid
  grid: getResponsiveClasses(responsiveGrids.formFields),
  
  // Form buttons
  buttons: 'flex flex-col sm:flex-row gap-3 sm:gap-4',
  
  // Input groups
  inputGroup: 'space-y-2',
  
  // Label positioning
  label: 'block text-sm font-medium mb-1',
  
  // Input sizing
  input: 'w-full px-3 py-2 text-sm md:text-base',
} as const;

/**
 * Navigation responsive configurations
 */
export const navigationResponsive = {
  // Mobile menu
  mobileMenu: 'block lg:hidden',
  
  // Desktop menu
  desktopMenu: 'hidden lg:block',
  
  // Mobile overlay
  mobileOverlay: 'fixed inset-0 z-50 lg:hidden',
  
  // Sidebar responsive
  sidebar: {
    mobile: 'fixed inset-y-0 left-0 z-50 w-64 transform transition-transform lg:translate-x-0',
    desktop: 'hidden lg:flex lg:flex-col lg:w-64',
  },
  
  // Header responsive
  header: {
    mobile: 'flex items-center justify-between p-4 lg:hidden',
    desktop: 'hidden lg:flex lg:items-center lg:justify-between lg:p-6',
  },
} as const;

/**
 * Card responsive configurations
 */
export const cardResponsive = {
  // Standard card
  card: 'bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700',
  
  // Card padding
  padding: getResponsiveClasses(responsiveSpacing.card),
  
  // Card grid
  grid: getResponsiveClasses(responsiveGrids.entityCards),
  
  // Card header
  header: 'flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4',
  
  // Card content
  content: 'space-y-4',
  
  // Card actions
  actions: 'flex flex-col sm:flex-row gap-2 sm:gap-3 mt-4',
} as const;

/**
 * Modal responsive configurations
 */
export const modalResponsive = {
  // Modal container
  container: 'fixed inset-0 z-50 overflow-y-auto',
  
  // Modal backdrop
  backdrop: 'fixed inset-0 bg-black bg-opacity-50',
  
  // Modal content
  content: 'relative bg-white dark:bg-gray-800 rounded-lg shadow-xl mx-4 my-8 md:mx-auto md:my-16 max-w-lg md:max-w-2xl lg:max-w-4xl',
  
  // Modal header
  header: 'flex items-center justify-between p-4 md:p-6 border-b border-gray-200 dark:border-gray-700',
  
  // Modal body
  body: 'p-4 md:p-6',
  
  // Modal footer
  footer: 'flex flex-col sm:flex-row gap-3 sm:gap-4 sm:justify-end p-4 md:p-6 border-t border-gray-200 dark:border-gray-700',
} as const;

/**
 * Utility function to combine responsive classes
 */
export const combineResponsiveClasses = (...classes: (string | undefined)[]): string => {
  return classes.filter(Boolean).join(' ');
};

/**
 * Device detection utilities
 */
export const deviceUtils = {
  isTouchDevice: (): boolean => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  },
  
  isIOS: (): boolean => {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  },
  
  isAndroid: (): boolean => {
    return /Android/.test(navigator.userAgent);
  },
  
  isMobile: (): boolean => {
    return /Mobi|Android/i.test(navigator.userAgent);
  },
  
  isTablet: (): boolean => {
    return /Tablet|iPad/i.test(navigator.userAgent);
  },
};

export default {
  breakpoints,
  useScreenSize,
  responsiveGrids,
  responsiveSpacing,
  responsiveText,
  getResponsiveClasses,
  responsive,
  tableResponsive,
  formResponsive,
  navigationResponsive,
  cardResponsive,
  modalResponsive,
  combineResponsiveClasses,
  deviceUtils,
};

// API Response Caching System for Dalti Admin Panel

import { CacheEntry, CacheConfig } from '../types';

// Cache configuration
const DEFAULT_CONFIG: CacheConfig = {
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxSize: 100, // Maximum 100 entries
  enablePersistence: true, // Store in localStorage
};

// Cache key prefixes for different data types
export const CACHE_KEYS = {
  PROVIDERS: 'providers',
  CUSTOMERS: 'customers',
  CATEGORIES: 'categories',
  DASHBOARD_METRICS: 'dashboard_metrics',
  RECENT_ACTIVITIES: 'recent_activities',
  ANALYTICS: 'analytics',
  AUDIT_LOGS: 'audit_logs',
  SYSTEM_SETTINGS: 'system_settings',
} as const;

// Cache TTL configurations for different data types
const CACHE_TTL_CONFIG = {
  [CACHE_KEYS.PROVIDERS]: 2 * 60 * 1000, // 2 minutes
  [CACHE_KEYS.CUSTOMERS]: 2 * 60 * 1000, // 2 minutes
  [CACHE_KEYS.CATEGORIES]: 10 * 60 * 1000, // 10 minutes (categories change less frequently)
  [CACHE_KEYS.DASHBOARD_METRICS]: 1 * 60 * 1000, // 1 minute
  [CACHE_KEYS.RECENT_ACTIVITIES]: 30 * 1000, // 30 seconds
  [CACHE_KEYS.ANALYTICS]: 5 * 60 * 1000, // 5 minutes
  [CACHE_KEYS.AUDIT_LOGS]: 5 * 60 * 1000, // 5 minutes
  [CACHE_KEYS.SYSTEM_SETTINGS]: 30 * 60 * 1000, // 30 minutes
};

class CacheManager {
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig;
  private storageKey = 'dalti_admin_cache';

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.loadFromStorage();
    this.startCleanupInterval();
  }

  /**
   * Store data in cache
   */
  set<T>(key: string, data: T, customTTL?: number): void {
    const ttl = customTTL || this.getTTLForKey(key) || this.config.defaultTTL;
    const now = Date.now();
    
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + ttl,
      key,
    };

    // Check cache size limit
    if (this.cache.size >= this.config.maxSize) {
      this.evictOldest();
    }

    this.cache.set(key, entry);
    this.saveToStorage();
  }

  /**
   * Retrieve data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      this.saveToStorage();
      return null;
    }

    return entry.data;
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      this.saveToStorage();
      return false;
    }

    return true;
  }

  /**
   * Remove specific key from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.saveToStorage();
    }
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.saveToStorage();
  }

  /**
   * Clear cache entries by prefix
   */
  clearByPrefix(prefix: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => 
      key.startsWith(prefix)
    );
    
    keysToDelete.forEach(key => this.cache.delete(key));
    this.saveToStorage();
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    entries: Array<{ key: string; size: number; expiresIn: number }>;
  } {
    const now = Date.now();
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      size: this.estimateSize(entry.data),
      expiresIn: Math.max(0, entry.expiresAt - now),
    }));

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: this.calculateHitRate(),
      entries,
    };
  }

  /**
   * Invalidate cache entries based on patterns
   */
  invalidate(patterns: string[]): void {
    patterns.forEach(pattern => {
      if (pattern.includes('*')) {
        // Wildcard pattern
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        const keysToDelete = Array.from(this.cache.keys()).filter(key => 
          regex.test(key)
        );
        keysToDelete.forEach(key => this.cache.delete(key));
      } else {
        // Exact match
        this.cache.delete(pattern);
      }
    });
    
    this.saveToStorage();
  }

  /**
   * Get or set with automatic caching
   */
  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    customTTL?: number
  ): Promise<T> {
    // Try to get from cache first
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Fetch fresh data
    try {
      const data = await fetcher();
      this.set(key, data, customTTL);
      return data;
    } catch (error) {
      // If fetch fails, try to return stale data if available
      const staleEntry = this.cache.get(key) as CacheEntry<T> | undefined;
      if (staleEntry) {
        console.warn(`Using stale cache data for key: ${key}`);
        return staleEntry.data;
      }
      throw error;
    }
  }

  /**
   * Preload cache with data
   */
  preload<T>(key: string, data: T, customTTL?: number): void {
    this.set(key, data, customTTL);
  }

  /**
   * Get TTL for specific cache key
   */
  private getTTLForKey(key: string): number | undefined {
    for (const [prefix, ttl] of Object.entries(CACHE_TTL_CONFIG)) {
      if (key.startsWith(prefix)) {
        return ttl;
      }
    }
    return undefined;
  }

  /**
   * Evict oldest entry when cache is full
   */
  private evictOldest(): void {
    let oldestKey = '';
    let oldestTimestamp = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
    
    if (expiredKeys.length > 0) {
      this.saveToStorage();
    }
  }

  /**
   * Start periodic cleanup
   */
  private startCleanupInterval(): void {
    setInterval(() => {
      this.cleanup();
    }, 60000); // Clean up every minute
  }

  /**
   * Load cache from localStorage
   */
  private loadFromStorage(): void {
    if (!this.config.enablePersistence || typeof localStorage === 'undefined') {
      return;
    }

    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const entries: Array<[string, CacheEntry]> = JSON.parse(stored);
        const now = Date.now();

        entries.forEach(([key, entry]) => {
          // Only load non-expired entries
          if (now <= entry.expiresAt) {
            this.cache.set(key, entry);
          }
        });
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error);
    }
  }

  /**
   * Save cache to localStorage
   */
  private saveToStorage(): void {
    if (!this.config.enablePersistence || typeof localStorage === 'undefined') {
      return;
    }

    try {
      const entries = Array.from(this.cache.entries());
      localStorage.setItem(this.storageKey, JSON.stringify(entries));
    } catch (error) {
      console.warn('Failed to save cache to storage:', error);
    }
  }

  /**
   * Estimate data size (rough approximation)
   */
  private estimateSize(data: any): number {
    try {
      return JSON.stringify(data).length;
    } catch {
      return 0;
    }
  }

  /**
   * Calculate cache hit rate (placeholder for future implementation)
   */
  private calculateHitRate(): number {
    // TODO: Implement hit rate tracking
    return 0;
  }
}

// Export singleton instance
export const cacheManager = new CacheManager();

// Utility functions for common cache operations
export const cacheUtils = {
  /**
   * Generate cache key for paginated data
   */
  generatePaginatedKey(baseKey: string, page: number, limit: number, filters?: Record<string, any>): string {
    const filterString = filters ? JSON.stringify(filters) : '';
    return `${baseKey}_page_${page}_limit_${limit}_${filterString}`;
  },

  /**
   * Generate cache key for filtered data
   */
  generateFilteredKey(baseKey: string, filters: Record<string, any>): string {
    const filterString = JSON.stringify(filters);
    return `${baseKey}_filtered_${filterString}`;
  },

  /**
   * Invalidate related cache entries
   */
  invalidateRelated(entityType: string, entityId?: string): void {
    const patterns = [`${entityType}*`];
    
    if (entityId) {
      patterns.push(`*${entityId}*`);
    }

    // Also invalidate dashboard metrics when entities change
    patterns.push(CACHE_KEYS.DASHBOARD_METRICS);
    patterns.push(CACHE_KEYS.RECENT_ACTIVITIES);

    cacheManager.invalidate(patterns);
  },

  /**
   * Preload common data
   */
  async preloadCommonData(): Promise<void> {
    // This would be called on app initialization to preload frequently accessed data
    // Implementation would depend on available API endpoints
  },
};

export default cacheManager;

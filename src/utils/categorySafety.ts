import { ProviderCategory } from '../types';

export interface CategorySafetyResult {
  canDelete: boolean;
  canMove: boolean;
  canDeactivate: boolean;
  issues: CategorySafetyIssue[];
  warnings: CategorySafetyWarning[];
  recommendations: string[];
}

export interface CategorySafetyIssue {
  type: 'circular_reference' | 'orphan_children' | 'provider_dependency' | 'booking_history' | 'revenue_impact';
  severity: 'critical' | 'warning' | 'info';
  message: string;
  affectedItems: string[];
  autoFixable: boolean;
}

export interface CategorySafetyWarning {
  type: 'performance' | 'user_experience' | 'data_integrity' | 'business_impact';
  message: string;
  impact: string;
  suggestion: string;
}

/**
 * Comprehensive category safety analysis
 */
export class CategorySafetyAnalyzer {
  private categories: ProviderCategory[];
  
  constructor(categories: ProviderCategory[]) {
    this.categories = categories;
  }

  /**
   * Analyze safety for category deletion
   */
  analyzeDeletion(categoryId: string): CategorySafetyResult {
    const category = this.categories.find(c => c.id === categoryId);
    if (!category) {
      throw new Error('Category not found');
    }

    const issues: CategorySafetyIssue[] = [];
    const warnings: CategorySafetyWarning[] = [];
    const recommendations: string[] = [];

    // Check for child categories
    const childCategories = this.getChildCategories(categoryId);
    if (childCategories.length > 0) {
      issues.push({
        type: 'orphan_children',
        severity: 'critical',
        message: `Category has ${childCategories.length} child categories`,
        affectedItems: childCategories.map(c => c.name),
        autoFixable: true,
      });
      recommendations.push('Reassign child categories to another parent or make them root categories');
    }

    // Check for provider dependencies
    if (category.providerCount && category.providerCount > 0) {
      const severity = category.providerCount > 10 ? 'critical' : 'warning';
      issues.push({
        type: 'provider_dependency',
        severity,
        message: `Category has ${category.providerCount} assigned providers`,
        affectedItems: [`${category.providerCount} providers`],
        autoFixable: true,
      });
      recommendations.push('Reassign providers to similar categories before deletion');
    }

    // Check for circular references in the hierarchy
    if (this.hasCircularReference(categoryId)) {
      issues.push({
        type: 'circular_reference',
        severity: 'critical',
        message: 'Circular reference detected in category hierarchy',
        affectedItems: this.getCircularReferencePath(categoryId),
        autoFixable: true,
      });
      recommendations.push('Fix circular reference before proceeding');
    }

    // Business impact warnings
    if (category.providerCount && category.providerCount > 5) {
      warnings.push({
        type: 'business_impact',
        message: 'High provider count may impact business operations',
        impact: 'Customer discovery and booking patterns may be affected',
        suggestion: 'Consider archiving instead of deleting',
      });
    }

    const canDelete = !issues.some(issue => issue.severity === 'critical');

    return {
      canDelete,
      canMove: true,
      canDeactivate: true,
      issues,
      warnings,
      recommendations,
    };
  }

  /**
   * Analyze safety for category movement
   */
  analyzeMovement(categoryId: string, newParentId: string | null): CategorySafetyResult {
    const issues: CategorySafetyIssue[] = [];
    const warnings: CategorySafetyWarning[] = [];
    const recommendations: string[] = [];

    // Check for circular reference after move
    if (newParentId && this.wouldCreateCircularReference(categoryId, newParentId)) {
      issues.push({
        type: 'circular_reference',
        severity: 'critical',
        message: 'Moving category would create circular reference',
        affectedItems: this.getCircularReferencePath(categoryId, newParentId),
        autoFixable: false,
      });
      recommendations.push('Choose a different parent category');
    }

    // Check hierarchy depth
    const newDepth = this.calculateDepthAfterMove(categoryId, newParentId);
    if (newDepth > 3) {
      warnings.push({
        type: 'performance',
        message: `Category hierarchy would become too deep (${newDepth} levels)`,
        impact: 'May impact navigation performance and user experience',
        suggestion: 'Consider flattening the hierarchy structure',
      });
    }

    const canMove = !issues.some(issue => issue.severity === 'critical');

    return {
      canDelete: true,
      canMove,
      canDeactivate: true,
      issues,
      warnings,
      recommendations,
    };
  }

  /**
   * Analyze overall category structure health
   */
  analyzeStructureHealth(): CategorySafetyResult {
    const issues: CategorySafetyIssue[] = [];
    const warnings: CategorySafetyWarning[] = [];
    const recommendations: string[] = [];

    // Check for orphaned categories
    const orphanedCategories = this.findOrphanedCategories();
    if (orphanedCategories.length > 0) {
      issues.push({
        type: 'orphan_children',
        severity: 'warning',
        message: `${orphanedCategories.length} categories reference non-existent parents`,
        affectedItems: orphanedCategories.map(c => c.name),
        autoFixable: true,
      });
    }

    // Check for circular references
    const circularReferences = this.findAllCircularReferences();
    circularReferences.forEach(ref => {
      issues.push({
        type: 'circular_reference',
        severity: 'critical',
        message: 'Circular reference in category hierarchy',
        affectedItems: ref,
        autoFixable: true,
      });
    });

    // Check for duplicate names
    const duplicateNames = this.findDuplicateNames();
    if (duplicateNames.length > 0) {
      warnings.push({
        type: 'user_experience',
        message: `${duplicateNames.length} categories have duplicate names`,
        impact: 'May confuse users and affect category selection',
        suggestion: 'Rename categories to make them unique',
      });
    }

    // Check for inactive categories with providers
    const inactiveWithProviders = this.categories.filter(c => 
      !c.isActive && c.providerCount && c.providerCount > 0
    );
    if (inactiveWithProviders.length > 0) {
      warnings.push({
        type: 'business_impact',
        message: `${inactiveWithProviders.length} inactive categories have assigned providers`,
        impact: 'Providers may not be discoverable by customers',
        suggestion: 'Activate categories or reassign providers',
      });
    }

    return {
      canDelete: true,
      canMove: true,
      canDeactivate: true,
      issues,
      warnings,
      recommendations,
    };
  }

  /**
   * Get child categories of a given category
   */
  private getChildCategories(categoryId: string): ProviderCategory[] {
    return this.categories.filter(c => c.parentId === categoryId);
  }

  /**
   * Check if a category has circular reference
   */
  private hasCircularReference(categoryId: string): boolean {
    const visited = new Set<string>();
    let current = this.categories.find(c => c.id === categoryId);

    while (current?.parentId) {
      if (visited.has(current.id)) {
        return true;
      }
      visited.add(current.id);
      current = this.categories.find(c => c.id === current!.parentId);
    }

    return false;
  }

  /**
   * Get the path of circular reference
   */
  private getCircularReferencePath(categoryId: string, newParentId?: string): string[] {
    const path: string[] = [];
    const visited = new Set<string>();
    let current = this.categories.find(c => c.id === categoryId);

    // If checking for potential circular reference after move
    if (newParentId) {
      path.push(current?.name || categoryId);
      current = this.categories.find(c => c.id === newParentId);
    }

    while (current) {
      if (visited.has(current.id)) {
        const circularStart = path.indexOf(current.name);
        return path.slice(circularStart);
      }
      
      visited.add(current.id);
      path.push(current.name);
      
      if (current.parentId) {
        current = this.categories.find(c => c.id === current!.parentId);
      } else {
        break;
      }
    }

    return path;
  }

  /**
   * Check if moving a category would create circular reference
   */
  private wouldCreateCircularReference(categoryId: string, newParentId: string): boolean {
    // Check if newParentId is a descendant of categoryId
    const isDescendant = (checkId: string, ancestorId: string): boolean => {
      const category = this.categories.find(c => c.id === checkId);
      if (!category?.parentId) return false;
      if (category.parentId === ancestorId) return true;
      return isDescendant(category.parentId, ancestorId);
    };

    return isDescendant(newParentId, categoryId);
  }

  /**
   * Calculate hierarchy depth after move
   */
  private calculateDepthAfterMove(categoryId: string, newParentId: string | null): number {
    if (!newParentId) return 0;

    let depth = 1;
    let current = this.categories.find(c => c.id === newParentId);

    while (current?.parentId) {
      depth++;
      current = this.categories.find(c => c.id === current!.parentId);
      
      // Prevent infinite loop
      if (depth > 10) break;
    }

    return depth;
  }

  /**
   * Find categories that reference non-existent parents
   */
  private findOrphanedCategories(): ProviderCategory[] {
    const categoryIds = new Set(this.categories.map(c => c.id));
    return this.categories.filter(c => 
      c.parentId && !categoryIds.has(c.parentId)
    );
  }

  /**
   * Find all circular references in the category structure
   */
  private findAllCircularReferences(): string[][] {
    const circularRefs: string[][] = [];
    const visited = new Set<string>();

    this.categories.forEach(category => {
      if (!visited.has(category.id) && this.hasCircularReference(category.id)) {
        const path = this.getCircularReferencePath(category.id);
        circularRefs.push(path);
        path.forEach(name => {
          const cat = this.categories.find(c => c.name === name);
          if (cat) visited.add(cat.id);
        });
      }
    });

    return circularRefs;
  }

  /**
   * Find categories with duplicate names
   */
  private findDuplicateNames(): string[] {
    const nameCount = new Map<string, number>();
    
    this.categories.forEach(category => {
      const name = category.title.toLowerCase().trim();
      nameCount.set(name, (nameCount.get(name) || 0) + 1);
    });

    return Array.from(nameCount.entries())
      .filter(([_, count]) => count > 1)
      .map(([name, _]) => name);
  }
}

/**
 * Quick safety check for category operations
 */
export function quickSafetyCheck(
  categories: ProviderCategory[],
  operation: 'delete' | 'move' | 'deactivate',
  categoryId: string,
  newParentId?: string
): { safe: boolean; issues: string[] } {
  const analyzer = new CategorySafetyAnalyzer(categories);
  
  let result: CategorySafetyResult;
  
  switch (operation) {
    case 'delete':
      result = analyzer.analyzeDeletion(categoryId);
      return {
        safe: result.canDelete,
        issues: result.issues.filter(i => i.severity === 'critical').map(i => i.message),
      };
    case 'move':
      result = analyzer.analyzeMovement(categoryId, newParentId || null);
      return {
        safe: result.canMove,
        issues: result.issues.filter(i => i.severity === 'critical').map(i => i.message),
      };
    case 'deactivate':
      // Deactivation is generally safe, just check for provider impact
      const category = categories.find(c => c.id === categoryId);
      const hasProviders = category?.providerCount && category.providerCount > 0;
      return {
        safe: true,
        issues: hasProviders ? ['Category has assigned providers that will become undiscoverable'] : [],
      };
    default:
      return { safe: false, issues: ['Unknown operation'] };
  }
}

/**
 * Auto-fix category safety issues
 */
export function generateAutoFixSuggestions(
  categories: ProviderCategory[],
  issues: CategorySafetyIssue[]
): { action: string; description: string; categoryId: string }[] {
  const suggestions: { action: string; description: string; categoryId: string }[] = [];

  issues.forEach(issue => {
    if (!issue.autoFixable) return;

    switch (issue.type) {
      case 'circular_reference':
        suggestions.push({
          action: 'remove_parent',
          description: 'Remove parent assignment to break circular reference',
          categoryId: issue.affectedItems[0] || '',
        });
        break;
      case 'orphan_children':
        suggestions.push({
          action: 'reassign_children',
          description: 'Reassign child categories to valid parents',
          categoryId: issue.affectedItems[0] || '',
        });
        break;
      case 'provider_dependency':
        suggestions.push({
          action: 'reassign_providers',
          description: 'Move providers to similar categories',
          categoryId: issue.affectedItems[0] || '',
        });
        break;
    }
  });

  return suggestions;
}

/**
 * Accessibility Utilities for Dalti Admin Panel
 * Provides utilities for WCAG compliance, keyboard navigation, and screen reader support
 */

import React from 'react';

// ARIA Live Region Types
export type AriaLiveType = 'off' | 'polite' | 'assertive';

// Focus Management
export class FocusManager {
  private static focusStack: HTMLElement[] = [];

  /**
   * Save current focus and set new focus
   */
  static pushFocus(element: HTMLElement): void {
    const currentFocus = document.activeElement as HTMLElement;
    if (currentFocus) {
      this.focusStack.push(currentFocus);
    }
    element.focus();
  }

  /**
   * Restore previous focus
   */
  static popFocus(): void {
    const previousFocus = this.focusStack.pop();
    if (previousFocus) {
      previousFocus.focus();
    }
  }

  /**
   * Trap focus within a container
   */
  static trapFocus(container: HTMLElement): () => void {
    const focusableElements = this.getFocusableElements(container);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);

    // Focus first element
    if (firstElement) {
      firstElement.focus();
    }

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }

  /**
   * Get all focusable elements within a container
   */
  static getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ].join(', ');

    return Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[];
  }

  /**
   * Check if element is focusable
   */
  static isFocusable(element: HTMLElement): boolean {
    const focusableElements = this.getFocusableElements(document.body);
    return focusableElements.includes(element);
  }
}

// Keyboard Navigation Utilities
export class KeyboardNavigation {
  /**
   * Handle arrow key navigation in a list
   */
  static handleArrowNavigation(
    event: React.KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    onIndexChange: (index: number) => void,
    orientation: 'horizontal' | 'vertical' = 'vertical'
  ): void {
    const { key } = event;
    let newIndex = currentIndex;

    if (orientation === 'vertical') {
      if (key === 'ArrowDown') {
        newIndex = (currentIndex + 1) % items.length;
      } else if (key === 'ArrowUp') {
        newIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
      }
    } else {
      if (key === 'ArrowRight') {
        newIndex = (currentIndex + 1) % items.length;
      } else if (key === 'ArrowLeft') {
        newIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
      }
    }

    if (newIndex !== currentIndex) {
      event.preventDefault();
      onIndexChange(newIndex);
      items[newIndex]?.focus();
    }
  }

  /**
   * Handle Enter and Space key activation
   */
  static handleActivation(
    event: React.KeyboardEvent,
    callback: () => void
  ): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      callback();
    }
  }

  /**
   * Handle Escape key
   */
  static handleEscape(
    event: React.KeyboardEvent,
    callback: () => void
  ): void {
    if (event.key === 'Escape') {
      event.preventDefault();
      callback();
    }
  }
}

// Screen Reader Utilities
export class ScreenReader {
  private static liveRegion: HTMLElement | null = null;

  /**
   * Initialize live region for announcements
   */
  static initializeLiveRegion(): void {
    if (this.liveRegion) return;

    this.liveRegion = document.createElement('div');
    this.liveRegion.setAttribute('aria-live', 'polite');
    this.liveRegion.setAttribute('aria-atomic', 'true');
    this.liveRegion.className = 'sr-only';
    this.liveRegion.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `;
    document.body.appendChild(this.liveRegion);
  }

  /**
   * Announce message to screen readers
   */
  static announce(message: string, priority: AriaLiveType = 'polite'): void {
    this.initializeLiveRegion();
    
    if (this.liveRegion) {
      this.liveRegion.setAttribute('aria-live', priority);
      this.liveRegion.textContent = message;
      
      // Clear after announcement
      setTimeout(() => {
        if (this.liveRegion) {
          this.liveRegion.textContent = '';
        }
      }, 1000);
    }
  }

  /**
   * Generate accessible description for complex UI elements
   */
  static generateDescription(element: {
    type: string;
    status?: string;
    count?: number;
    position?: { current: number; total: number };
  }): string {
    let description = element.type;

    if (element.status) {
      description += `, status: ${element.status}`;
    }

    if (element.count !== undefined) {
      description += `, ${element.count} items`;
    }

    if (element.position) {
      description += `, ${element.position.current} of ${element.position.total}`;
    }

    return description;
  }
}

// Color Contrast Utilities
export class ColorContrast {
  /**
   * Calculate relative luminance of a color
   */
  static getLuminance(r: number, g: number, b: number): number {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  /**
   * Calculate contrast ratio between two colors
   */
  static getContrastRatio(color1: [number, number, number], color2: [number, number, number]): number {
    const lum1 = this.getLuminance(...color1);
    const lum2 = this.getLuminance(...color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  }

  /**
   * Check if color combination meets WCAG AA standards
   */
  static meetsWCAGAA(foreground: [number, number, number], background: [number, number, number]): boolean {
    const ratio = this.getContrastRatio(foreground, background);
    return ratio >= 4.5; // WCAG AA standard for normal text
  }

  /**
   * Check if color combination meets WCAG AAA standards
   */
  static meetsWCAGAAA(foreground: [number, number, number], background: [number, number, number]): boolean {
    const ratio = this.getContrastRatio(foreground, background);
    return ratio >= 7; // WCAG AAA standard for normal text
  }
}

// ARIA Utilities
export class AriaUtils {
  /**
   * Generate unique ID for ARIA relationships
   */
  static generateId(prefix: string = 'aria'): string {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create ARIA label for form fields
   */
  static createFieldLabel(label: string, required: boolean = false, error?: string): string {
    let ariaLabel = label;
    if (required) {
      ariaLabel += ', required';
    }
    if (error) {
      ariaLabel += `, error: ${error}`;
    }
    return ariaLabel;
  }

  /**
   * Create ARIA description for complex components
   */
  static createDescription(parts: string[]): string {
    return parts.filter(Boolean).join(', ');
  }

  /**
   * Get appropriate ARIA role for interactive elements
   */
  static getRole(element: string): string {
    const roleMap: Record<string, string> = {
      'clickable-div': 'button',
      'custom-select': 'combobox',
      'tab-panel': 'tabpanel',
      'menu-item': 'menuitem',
      'tree-item': 'treeitem',
    };
    return roleMap[element] || '';
  }
}

// Accessibility Testing Utilities
export class AccessibilityTesting {
  /**
   * Check for common accessibility issues
   */
  static auditElement(element: HTMLElement): string[] {
    const issues: string[] = [];

    // Check for missing alt text on images
    const images = element.querySelectorAll('img');
    images.forEach(img => {
      if (!img.alt && !img.getAttribute('aria-label')) {
        issues.push('Image missing alt text');
      }
    });

    // Check for missing form labels
    const inputs = element.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      const id = input.id;
      const hasLabel = id && element.querySelector(`label[for="${id}"]`);
      const hasAriaLabel = input.getAttribute('aria-label') || input.getAttribute('aria-labelledby');
      
      if (!hasLabel && !hasAriaLabel) {
        issues.push('Form control missing label');
      }
    });

    // Check for missing heading hierarchy
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let lastLevel = 0;
    headings.forEach(heading => {
      const level = parseInt(heading.tagName.charAt(1));
      if (level > lastLevel + 1) {
        issues.push('Heading hierarchy skipped');
      }
      lastLevel = level;
    });

    // Check for interactive elements without proper roles
    const clickableElements = element.querySelectorAll('[onclick], .cursor-pointer');
    clickableElements.forEach(el => {
      if (!el.getAttribute('role') && el.tagName !== 'BUTTON' && el.tagName !== 'A') {
        issues.push('Interactive element missing role');
      }
    });

    return issues;
  }

  /**
   * Test keyboard navigation
   */
  static testKeyboardNavigation(container: HTMLElement): boolean {
    const focusableElements = FocusManager.getFocusableElements(container);
    return focusableElements.length > 0;
  }
}

// React Hooks for Accessibility
export const useAccessibility = () => {
  React.useEffect(() => {
    ScreenReader.initializeLiveRegion();
  }, []);

  const announce = React.useCallback((message: string, priority: AriaLiveType = 'polite') => {
    ScreenReader.announce(message, priority);
  }, []);

  const generateId = React.useCallback((prefix?: string) => {
    return AriaUtils.generateId(prefix);
  }, []);

  return {
    announce,
    generateId,
    FocusManager,
    KeyboardNavigation,
    AriaUtils,
  };
};

// Skip Link Component
export const SkipLink: React.FC<{ href: string; children: React.ReactNode }> = ({ href, children }) => (
  <a
    href={href}
    className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded-lg focus:shadow-lg"
  >
    {children}
  </a>
);

export default {
  FocusManager,
  KeyboardNavigation,
  ScreenReader,
  ColorContrast,
  AriaUtils,
  AccessibilityTesting,
  useAccessibility,
  SkipLink,
};

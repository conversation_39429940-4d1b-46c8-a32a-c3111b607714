/**
 * Lazy Loading and Code Splitting Utilities
 * Provides intelligent lazy loading with error boundaries and loading states
 */

import React, { Suspense, ComponentType, LazyExoticComponent } from 'react';
import { ErrorBoundary } from '../components/error/ErrorBoundary';
import { ThemeSpinner } from '../components/theme/ThemeAwareComponents';

// Lazy loading options
interface LazyLoadOptions {
  fallback?: React.ComponentType;
  errorFallback?: React.ComponentType<any>;
  retryCount?: number;
  preload?: boolean;
  chunkName?: string;
  timeout?: number;
}

// Enhanced lazy loading with retry logic
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): LazyExoticComponent<T> {
  const {
    fallback: CustomFallback,
    errorFallback: CustomErrorFallback,
    retryCount = 3,
    timeout = 10000,
  } = options;

  // Create retry wrapper for import function
  const retryImport = async (attempt = 1): Promise<{ default: T }> => {
    try {
      // Add timeout to import
      const importPromise = importFn();
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Import timeout')), timeout);
      });

      return await Promise.race([importPromise, timeoutPromise]);
    } catch (error) {
      console.warn(`Lazy import attempt ${attempt} failed:`, error);
      
      if (attempt < retryCount) {
        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));
        return retryImport(attempt + 1);
      }
      
      throw error;
    }
  };

  return React.lazy(retryImport);
}

// Lazy component wrapper with loading and error states
interface LazyWrapperProps {
  children: React.ReactNode;
  fallback?: React.ComponentType;
  errorFallback?: React.ComponentType<any>;
  name?: string;
}

export function LazyWrapper({
  children,
  fallback: CustomFallback,
  errorFallback: CustomErrorFallback,
  name = 'Component',
}: LazyWrapperProps) {
  const LoadingFallback = CustomFallback || (() => (
    <div className="flex items-center justify-center min-h-64">
      <div className="text-center">
        <ThemeSpinner size="lg" />
        <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
          Loading {name}...
        </p>
      </div>
    </div>
  ));

  return (
    <ErrorBoundary
      fallback={CustomErrorFallback}
      context={`LazyLoad: ${name}`}
      level="section"
    >
      <Suspense fallback={<LoadingFallback />}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
}

// Preloader for lazy components
export class LazyPreloader {
  private static preloadedComponents = new Set<string>();
  private static preloadPromises = new Map<string, Promise<any>>();

  static preload(
    importFn: () => Promise<any>,
    componentName: string
  ): Promise<any> {
    if (this.preloadedComponents.has(componentName)) {
      return Promise.resolve();
    }

    if (this.preloadPromises.has(componentName)) {
      return this.preloadPromises.get(componentName)!;
    }

    const preloadPromise = importFn()
      .then(() => {
        this.preloadedComponents.add(componentName);
        this.preloadPromises.delete(componentName);
      })
      .catch(error => {
        console.warn(`Failed to preload ${componentName}:`, error);
        this.preloadPromises.delete(componentName);
        throw error;
      });

    this.preloadPromises.set(componentName, preloadPromise);
    return preloadPromise;
  }

  static preloadOnHover(
    element: HTMLElement,
    importFn: () => Promise<any>,
    componentName: string
  ): () => void {
    let timeoutId: NodeJS.Timeout;

    const handleMouseEnter = () => {
      timeoutId = setTimeout(() => {
        this.preload(importFn, componentName);
      }, 100); // Small delay to avoid unnecessary preloads
    };

    const handleMouseLeave = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }

  static preloadOnIntersection(
    importFn: () => Promise<any>,
    componentName: string,
    options: IntersectionObserverInit = {}
  ): (element: HTMLElement) => () => void {
    return (element: HTMLElement) => {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.preload(importFn, componentName);
              observer.unobserve(entry.target);
            }
          });
        },
        {
          rootMargin: '50px',
          ...options,
        }
      );

      observer.observe(element);

      return () => {
        observer.unobserve(element);
        observer.disconnect();
      };
    };
  }
}

// Hook for lazy loading with preloading
export function useLazyPreload(
  importFn: () => Promise<any>,
  componentName: string
) {
  const [isPreloaded, setIsPreloaded] = React.useState(false);
  const [isPreloading, setIsPreloading] = React.useState(false);

  const preload = React.useCallback(async () => {
    if (isPreloaded || isPreloading) return;

    setIsPreloading(true);
    try {
      await LazyPreloader.preload(importFn, componentName);
      setIsPreloaded(true);
    } catch (error) {
      console.warn(`Failed to preload ${componentName}:`, error);
    } finally {
      setIsPreloading(false);
    }
  }, [importFn, componentName, isPreloaded, isPreloading]);

  const preloadOnHover = React.useCallback((element: HTMLElement) => {
    return LazyPreloader.preloadOnHover(element, importFn, componentName);
  }, [importFn, componentName]);

  const preloadOnIntersection = React.useCallback((options?: IntersectionObserverInit) => {
    return LazyPreloader.preloadOnIntersection(importFn, componentName, options);
  }, [importFn, componentName]);

  return {
    preload,
    preloadOnHover,
    preloadOnIntersection,
    isPreloaded,
    isPreloading,
  };
}

// Image lazy loading component
interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  placeholder?: string;
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
  threshold?: number;
  rootMargin?: string;
}

export function LazyImage({
  src,
  alt,
  placeholder,
  blurDataURL,
  onLoad,
  onError,
  threshold = 0.1,
  rootMargin = '50px',
  className = '',
  ...props
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [isInView, setIsInView] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);
  const imgRef = React.useRef<HTMLImageElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold, rootMargin }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [threshold, rootMargin]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  const shouldShowPlaceholder = !isLoaded && !hasError;
  const shouldShowBlur = blurDataURL && shouldShowPlaceholder;

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Placeholder */}
      {shouldShowPlaceholder && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse">
          {placeholder && (
            <div className="flex items-center justify-center h-full text-gray-400 text-sm">
              {placeholder}
            </div>
          )}
        </div>
      )}

      {/* Blur placeholder */}
      {shouldShowBlur && (
        <img
          src={blurDataURL}
          alt=""
          className="absolute inset-0 w-full h-full object-cover filter blur-sm scale-110"
          aria-hidden="true"
        />
      )}

      {/* Main image */}
      <img
        ref={imgRef}
        src={isInView ? src : undefined}
        alt={alt}
        onLoad={handleLoad}
        onError={handleError}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        {...props}
      />

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-xs">Failed to load image</p>
          </div>
        </div>
      )}
    </div>
  );
}

// Lazy route component
interface LazyRouteProps {
  component: LazyExoticComponent<any>;
  fallback?: React.ComponentType;
  errorFallback?: React.ComponentType<any>;
  preload?: boolean;
  name?: string;
}

export function LazyRoute({
  component: Component,
  fallback,
  errorFallback,
  name = 'Page',
  ...props
}: LazyRouteProps) {
  return (
    <LazyWrapper
      fallback={fallback}
      errorFallback={errorFallback}
      name={name}
    >
      <Component {...props} />
    </LazyWrapper>
  );
}

// Bundle analyzer utilities
export class BundleAnalyzer {
  static logChunkInfo(): void {
    if (process.env.NODE_ENV === 'development') {
      // Log webpack chunk information
      if (typeof __webpack_require__ !== 'undefined') {
        console.group('📦 Bundle Information');
        console.log('Chunks loaded:', Object.keys(__webpack_require__.cache || {}));
        console.groupEnd();
      }
    }
  }

  static measureChunkLoadTime(chunkName: string): (endTime?: number) => void {
    const startTime = performance.now();
    
    return (endTime?: number) => {
      const loadTime = (endTime || performance.now()) - startTime;
      console.log(`📦 Chunk "${chunkName}" loaded in ${loadTime.toFixed(2)}ms`);
      
      // Report to analytics
      if (process.env.REACT_APP_ANALYTICS_URL) {
        fetch(process.env.REACT_APP_ANALYTICS_URL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'chunk-load',
            chunkName,
            loadTime,
            timestamp: Date.now(),
          }),
        }).catch(() => {
          // Ignore analytics errors
        });
      }
    };
  }
}

// Resource hints utilities
export class ResourceHints {
  static preloadResource(href: string, as: string, crossorigin?: string): void {
    if (typeof document === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (crossorigin) link.crossOrigin = crossorigin;
    
    document.head.appendChild(link);
  }

  static prefetchResource(href: string): void {
    if (typeof document === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    
    document.head.appendChild(link);
  }

  static preconnect(href: string, crossorigin?: boolean): void {
    if (typeof document === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = href;
    if (crossorigin) link.crossOrigin = 'anonymous';
    
    document.head.appendChild(link);
  }

  static dnsPrefetch(href: string): void {
    if (typeof document === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = href;
    
    document.head.appendChild(link);
  }
}

export default {
  createLazyComponent,
  LazyWrapper,
  LazyPreloader,
  LazyImage,
  LazyRoute,
  BundleAnalyzer,
  ResourceHints,
};

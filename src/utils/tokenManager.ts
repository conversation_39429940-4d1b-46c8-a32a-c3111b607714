// JWT Token Management for Dalti Admin Panel

interface TokenData {
  token: string;
  expiresAt?: number;
}

interface DecodedToken {
  exp: number;
  iat: number;
  adminId: string;
  email: string;
  role: string;
  isAdmin: boolean;
}

class TokenManager {
  private readonly TOKEN_KEY = import.meta.env.VITE_JWT_STORAGE_KEY || 'dalti_admin_token';
  private readonly EXPIRES_AT_KEY = 'dalti_admin_token_expires_at';

  /**
   * Store authentication token securely
   */
  setTokens(tokenData: TokenData): void {
    try {
      localStorage.setItem(this.TOKEN_KEY, tokenData.token);

      // Calculate and store expiration time
      const decoded = this.decodeToken(tokenData.token);
      if (decoded) {
        const expiresAt = decoded.exp * 1000; // Convert to milliseconds
        localStorage.setItem(this.EXPIRES_AT_KEY, expiresAt.toString());
      }
    } catch (error) {
      console.error('Error storing token:', error);
      throw new Error('Failed to store authentication token');
    }
  }

  /**
   * Get the current access token
   */
  getToken(): string | null {
    try {
      return localStorage.getItem(this.TOKEN_KEY);
    } catch (error) {
      console.error('Error retrieving token:', error);
      return null;
    }
  }



  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;

    return true
  }

  /**
   * Check if token is valid (not expired)
   */
  isTokenValid(token: string): boolean {
    try {
      const decoded = this.decodeToken(token);
      if (!decoded) return false;

      const currentTime = Date.now() / 1000;
      return decoded.exp > currentTime;
    } catch (error) {
      console.error('Error validating token:', error);
      return false;
    }
  }



  /**
   * Decode JWT token payload
   */
  decodeToken(token: string): DecodedToken | null {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return null;

      const payload = parts[1];
      const decoded = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')));
      
      return decoded as DecodedToken;
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  /**
   * Get admin user data from token
   */
  getAdminData(): DecodedToken | null {
    const token = this.getToken();
    if (!token) return null;

    return this.decodeToken(token);
  }

  /**
   * Get a valid token (since tokens are auto-refreshed, just return current token if valid)
   */
  async getValidToken(): Promise<string | null> {
    const currentToken = this.getToken();

    if (!currentToken) {
      return null;
    }

    if (this.isTokenValid(currentToken)) {
      return currentToken;
    }

    // Token is expired, clear it
    this.clearTokens();
    return null;
  }

  /**
   * Clear all stored tokens
   */
  clearTokens(): void {
    try {
      localStorage.removeItem(this.TOKEN_KEY);
      localStorage.removeItem(this.EXPIRES_AT_KEY);
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  }

  /**
   * Get token expiration time
   */
  getTokenExpiration(): Date | null {
    try {
      const expiresAt = localStorage.getItem(this.EXPIRES_AT_KEY);
      if (!expiresAt) return null;
      
      return new Date(parseInt(expiresAt));
    } catch (error) {
      console.error('Error getting token expiration:', error);
      return null;
    }
  }

  /**
   * Check if admin has specific permissions (for future use)
   */
  hasPermission(permission: string): boolean {
    const adminData = this.getAdminData();
    if (!adminData || !adminData.isAdmin) return false;
    
    // For now, all admins have all permissions
    // This can be extended later for role-based permissions
    return true;
  }


}

// Export singleton instance
export const tokenManager = new TokenManager();

// Export utility functions
export const isAuthenticated = () => tokenManager.isAuthenticated();
export const getToken = () => tokenManager.getToken();
export const getAdminData = () => tokenManager.getAdminData();
export const clearTokens = () => tokenManager.clearTokens();
export const getValidToken = () => tokenManager.getValidToken();

import { adminApi } from '../services/adminApi';
import { handleError } from './errorHandler';

export interface AuditLogEntry {
  action: string;
  entityType: 'provider' | 'customer' | 'category' | 'system';
  entityId?: string;
  details: Record<string, any>;
  metadata?: {
    userAgent?: string;
    ipAddress?: string;
    sessionId?: string;
    timestamp?: string;
  };
}

export interface AuditContext {
  adminId?: string;
  adminEmail?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
}

/**
 * Audit Logger for tracking administrative actions
 */
export class AuditLogger {
  private static instance: AuditLogger;
  private context: AuditContext = {};
  private pendingLogs: AuditLogEntry[] = [];
  private isOnline = true;

  private constructor() {
    // Set up online/offline detection
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushPendingLogs();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  /**
   * Set audit context (admin info, session details)
   */
  public setContext(context: AuditContext): void {
    this.context = { ...this.context, ...context };
  }

  /**
   * Log an administrative action
   */
  public async logAction(entry: AuditLogEntry): Promise<void> {
    const enrichedEntry = this.enrichLogEntry(entry);

    if (this.isOnline) {
      try {
        await this.sendLogToServer(enrichedEntry);
      } catch (error) {
        console.warn('Failed to send audit log to server, queuing for later:', error);
        this.pendingLogs.push(enrichedEntry);
      }
    } else {
      this.pendingLogs.push(enrichedEntry);
    }

    // Also log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Audit Log:', enrichedEntry);
    }
  }

  /**
   * Log provider-related actions
   */
  public async logProviderAction(
    action: 'approved' | 'rejected' | 'suspended' | 'activated' | 'updated' | 'deleted',
    providerId: string,
    details: Record<string, any>
  ): Promise<void> {
    await this.logAction({
      action: `provider_${action}`,
      entityType: 'provider',
      entityId: providerId,
      details: {
        providerId,
        ...details,
      },
    });
  }

  /**
   * Log customer-related actions
   */
  public async logCustomerAction(
    action: 'credits_updated' | 'suspended' | 'activated' | 'updated' | 'deleted',
    customerId: string,
    details: Record<string, any>
  ): Promise<void> {
    await this.logAction({
      action: `customer_${action}`,
      entityType: 'customer',
      entityId: customerId,
      details: {
        customerId,
        ...details,
      },
    });
  }

  /**
   * Log category-related actions
   */
  public async logCategoryAction(
    action: 'created' | 'updated' | 'deleted' | 'reordered' | 'activated' | 'deactivated',
    categoryId: string,
    details: Record<string, any>
  ): Promise<void> {
    await this.logAction({
      action: `category_${action}`,
      entityType: 'category',
      entityId: categoryId,
      details: {
        categoryId,
        ...details,
      },
    });
  }

  /**
   * Log system-related actions
   */
  public async logSystemAction(
    action: 'settings_updated' | 'backup_created' | 'maintenance_mode' | 'user_session' | 'security_event',
    details: Record<string, any>,
    entityId?: string
  ): Promise<void> {
    await this.logAction({
      action: `system_${action}`,
      entityType: 'system',
      entityId: entityId || 'system',
      details,
    });
  }

  /**
   * Log authentication and authorization events
   */
  public async logAuthAction(
    action: 'login' | 'logout' | 'failed_login' | 'password_reset' | 'permission_denied',
    details: Record<string, any>
  ): Promise<void> {
    await this.logAction({
      action: `auth_${action}`,
      entityType: 'system',
      entityId: 'auth',
      details,
    });
  }

  /**
   * Log bulk operations
   */
  public async logBulkAction(
    action: string,
    entityType: 'provider' | 'customer' | 'category' | 'system',
    entityIds: string[],
    details: Record<string, any>
  ): Promise<void> {
    await this.logAction({
      action: `bulk_${action}`,
      entityType,
      details: {
        entityIds,
        count: entityIds.length,
        ...details,
      },
    });
  }

  /**
   * Enrich log entry with context and metadata
   */
  private enrichLogEntry(entry: AuditLogEntry): AuditLogEntry {
    return {
      ...entry,
      details: {
        ...entry.details,
        adminId: this.context.adminId,
        adminEmail: this.context.adminEmail,
      },
      metadata: {
        userAgent: this.context.userAgent || navigator.userAgent,
        ipAddress: this.context.ipAddress,
        sessionId: this.context.sessionId,
        timestamp: new Date().toISOString(),
        ...entry.metadata,
      },
    };
  }

  /**
   * Send log entry to server
   */
  private async sendLogToServer(entry: AuditLogEntry): Promise<void> {
    try {
      await adminApi.auditLogs.createAuditLog({
        action: entry.action,
        entityType: entry.entityType,
        entityId: entry.entityId,
        details: entry.details,
      });
    } catch (error) {
      handleError(error, { action: 'send_audit_log', silent: true });
      throw error;
    }
  }

  /**
   * Flush pending logs when connection is restored
   */
  private async flushPendingLogs(): Promise<void> {
    if (this.pendingLogs.length === 0) return;

    const logsToSend = [...this.pendingLogs];
    this.pendingLogs = [];

    for (const log of logsToSend) {
      try {
        await this.sendLogToServer(log);
      } catch (error) {
        // If sending fails, put it back in the queue
        this.pendingLogs.push(log);
      }
    }
  }

  /**
   * Get pending logs count
   */
  public getPendingLogsCount(): number {
    return this.pendingLogs.length;
  }

  /**
   * Clear pending logs (use with caution)
   */
  public clearPendingLogs(): void {
    this.pendingLogs = [];
  }
}

// Export singleton instance
export const auditLogger = AuditLogger.getInstance();

/**
 * Decorator for automatically logging method calls
 */
export function LogAuditAction(
  action: string,
  entityType: 'provider' | 'customer' | 'category' | 'system',
  getEntityId?: (args: any[]) => string,
  getDetails?: (args: any[], result?: any) => Record<string, any>
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      let result: any;
      let error: any;

      try {
        result = await method.apply(this, args);
        return result;
      } catch (err) {
        error = err;
        throw err;
      } finally {
        // Log the action
        const entityId = getEntityId ? getEntityId(args) : undefined;
        const details = getDetails ? getDetails(args, result) : { args };
        
        await auditLogger.logAction({
          action: error ? `${action}_failed` : action,
          entityType,
          entityId,
          details: {
            ...details,
            duration: Date.now() - startTime,
            success: !error,
            error: error ? error.message : undefined,
          },
        });
      }
    };

    return descriptor;
  };
}

/**
 * Hook for React components to use audit logging
 */
export function useAuditLogger() {
  return {
    logAction: auditLogger.logAction.bind(auditLogger),
    logProviderAction: auditLogger.logProviderAction.bind(auditLogger),
    logCustomerAction: auditLogger.logCustomerAction.bind(auditLogger),
    logCategoryAction: auditLogger.logCategoryAction.bind(auditLogger),
    logSystemAction: auditLogger.logSystemAction.bind(auditLogger),
    logAuthAction: auditLogger.logAuthAction.bind(auditLogger),
    logBulkAction: auditLogger.logBulkAction.bind(auditLogger),
    setContext: auditLogger.setContext.bind(auditLogger),
    getPendingLogsCount: auditLogger.getPendingLogsCount.bind(auditLogger),
  };
}

/**
 * Initialize audit logger with admin context
 */
export function initializeAuditLogger(context: AuditContext): void {
  auditLogger.setContext(context);
}

/**
 * Quick logging functions for common actions
 */
export const quickLog = {
  providerApproved: (providerId: string, details: Record<string, any>) =>
    auditLogger.logProviderAction('approved', providerId, details),
  
  providerRejected: (providerId: string, details: Record<string, any>) =>
    auditLogger.logProviderAction('rejected', providerId, details),
  
  customerCreditsUpdated: (customerId: string, details: Record<string, any>) =>
    auditLogger.logCustomerAction('credits_updated', customerId, details),
  
  categoryCreated: (categoryId: string, details: Record<string, any>) =>
    auditLogger.logCategoryAction('created', categoryId, details),
  
  categoryDeleted: (categoryId: string, details: Record<string, any>) =>
    auditLogger.logCategoryAction('deleted', categoryId, details),
  
  systemSettingsUpdated: (details: Record<string, any>) =>
    auditLogger.logSystemAction('settings_updated', details),
  
  adminLogin: (details: Record<string, any>) =>
    auditLogger.logAuthAction('login', details),
  
  adminLogout: (details: Record<string, any>) =>
    auditLogger.logAuthAction('logout', details),
};

/**
 * Test Utilities for Dalti Admin Panel
 * Provides comprehensive testing utilities and helpers
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '../contexts/AuthContext';
import { ThemeProvider } from '../contexts/ThemeContext';
import { LoadingProvider } from '../contexts/LoadingContext';

// Mock data generators
export const mockProviders = {
  createProvider: (overrides = {}) => ({
    id: 'provider-1',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    businessTitle: 'Family Medicine Clinic',
    status: 'active',
    verified: true,
    rating: 4.5,
    totalRatings: 120,
    locationCount: 2,
    customerCount: 450,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    ...overrides,
  }),

  createProviderList: (count = 5) => 
    Array.from({ length: count }, (_, index) => 
      mockProviders.createProvider({
        id: `provider-${index + 1}`,
        name: `Provider ${index + 1}`,
        email: `provider${index + 1}@example.com`,
      })
    ),
};

export const mockCustomers = {
  createCustomer: (overrides = {}) => ({
    id: 'customer-1',
    firstName: 'Jane',
    lastName: 'Doe',
    email: '<EMAIL>',
    phoneNumber: '+**********',
    verified: true,
    credits: 100,
    totalBookings: 15,
    createdAt: '2024-01-10T08:00:00Z',
    updatedAt: '2024-01-10T08:00:00Z',
    ...overrides,
  }),

  createCustomerList: (count = 5) =>
    Array.from({ length: count }, (_, index) =>
      mockCustomers.createCustomer({
        id: `customer-${index + 1}`,
        firstName: `Customer`,
        lastName: `${index + 1}`,
        email: `customer${index + 1}@example.com`,
      })
    ),
};

export const mockCategories = {
  createCategory: (overrides = {}) => ({
    id: 'category-1',
    title: 'Healthcare',
    description: 'Medical and healthcare services',
    parentId: null,
    level: 0,
    providerCount: 25,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    ...overrides,
  }),

  createCategoryTree: () => [
    mockCategories.createCategory({
      id: 'cat-1',
      title: 'Healthcare',
      children: [
        mockCategories.createCategory({
          id: 'cat-1-1',
          title: 'General Medicine',
          parentId: 'cat-1',
          level: 1,
        }),
        mockCategories.createCategory({
          id: 'cat-1-2',
          title: 'Dentistry',
          parentId: 'cat-1',
          level: 1,
        }),
      ],
    }),
    mockCategories.createCategory({
      id: 'cat-2',
      title: 'Beauty & Wellness',
      children: [
        mockCategories.createCategory({
          id: 'cat-2-1',
          title: 'Hair Salon',
          parentId: 'cat-2',
          level: 1,
        }),
      ],
    }),
  ],
};

export const mockAuth = {
  createAuthUser: (overrides = {}) => ({
    id: 'admin-1',
    email: '<EMAIL>',
    username: 'admin',
    firstName: 'Admin',
    lastName: 'User',
    isAdmin: true,
    role: 'super_admin',
    credits: 0,
    preferedLanguage: 'en',
    identities: {
      email: {
        verified: true,
      },
    },
    ...overrides,
  }),

  createAuthResponse: (overrides = {}) => ({
    user: mockAuth.createAuthUser(),
    token: 'mock-jwt-token',
    refreshToken: 'mock-refresh-token',
    expiresIn: 3600,
    ...overrides,
  }),
};

// API Mock Utilities
export const mockApiResponse = {
  success: <T,>(data: T) => ({
    data,
    status: 200,
    statusText: 'OK',
    headers: {},
    config: {},
  }),

  error: (message = 'API Error', status = 500) => ({
    response: {
      data: { message },
      status,
      statusText: status === 404 ? 'Not Found' : 'Internal Server Error',
      headers: {},
      config: {},
    },
    message,
    isAxiosError: true,
  }),

  paginated: <T,>(data: T[], page = 1, limit = 20) => ({
    data,
    pagination: {
      page,
      limit,
      total: data.length,
      totalPages: Math.ceil(data.length / limit),
    },
  }),
};

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  authUser?: any;
  theme?: 'light' | 'dark' | 'system';
  queryClient?: QueryClient;
}

function createWrapper({
  initialEntries = ['/'],
  authUser = null,
  theme = 'light',
  queryClient,
}: CustomRenderOptions = {}) {
  const testQueryClient = queryClient || new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  });

  return function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <BrowserRouter>
        <QueryClientProvider client={testQueryClient}>
          <ThemeProvider defaultTheme={theme}>
            <AuthProvider initialUser={authUser}>
              <LoadingProvider>
                {children}
              </LoadingProvider>
            </AuthProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </BrowserRouter>
    );
  };
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult {
  const { initialEntries, authUser, theme, queryClient, ...renderOptions } = options;
  
  const Wrapper = createWrapper({ initialEntries, authUser, theme, queryClient });
  
  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Test helpers for common scenarios
export const testHelpers = {
  // Wait for loading to complete
  waitForLoadingToFinish: async () => {
    const { waitForElementToBeRemoved, queryByTestId } = await import('@testing-library/react');
    const loadingElement = queryByTestId(document.body, 'loading-spinner');
    if (loadingElement) {
      await waitForElementToBeRemoved(loadingElement);
    }
  },

  // Simulate user authentication
  authenticateUser: (user = mockAuth.createAuthUser()) => {
    localStorage.setItem('dalti_admin_token', 'mock-token');
    localStorage.setItem('dalti_admin_user', JSON.stringify(user));
    return user;
  },

  // Clear authentication
  clearAuth: () => {
    localStorage.removeItem('dalti_admin_token');
    localStorage.removeItem('dalti_admin_user');
  },

  // Mock API calls
  mockApiCall: (mockImplementation: any) => {
    const originalFetch = global.fetch;
    global.fetch = jest.fn().mockImplementation(mockImplementation);
    return () => {
      global.fetch = originalFetch;
    };
  },

  // Create mock intersection observer
  mockIntersectionObserver: () => {
    const mockIntersectionObserver = jest.fn();
    mockIntersectionObserver.mockReturnValue({
      observe: () => null,
      unobserve: () => null,
      disconnect: () => null,
    });
    window.IntersectionObserver = mockIntersectionObserver;
    return mockIntersectionObserver;
  },

  // Mock window.matchMedia for responsive tests
  mockMatchMedia: (matches = false) => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });
  },

  // Mock ResizeObserver
  mockResizeObserver: () => {
    global.ResizeObserver = jest.fn().mockImplementation(() => ({
      observe: jest.fn(),
      unobserve: jest.fn(),
      disconnect: jest.fn(),
    }));
  },
};

// Custom matchers
export const customMatchers = {
  toBeAccessible: (element: HTMLElement) => {
    const hasAriaLabel = element.hasAttribute('aria-label') || element.hasAttribute('aria-labelledby');
    const hasRole = element.hasAttribute('role');
    const isFocusable = element.tabIndex >= 0 || ['button', 'input', 'select', 'textarea', 'a'].includes(element.tagName.toLowerCase());
    
    return {
      pass: hasAriaLabel || hasRole || !isFocusable,
      message: () => `Expected element to be accessible`,
    };
  },

  toHaveValidContrast: (element: HTMLElement) => {
    // Simplified contrast check - in real implementation, you'd use a proper contrast checker
    const styles = window.getComputedStyle(element);
    const backgroundColor = styles.backgroundColor;
    const color = styles.color;
    
    return {
      pass: backgroundColor !== color, // Simplified check
      message: () => `Expected element to have valid color contrast`,
    };
  },
};

// Test data factories
export const factories = {
  provider: mockProviders,
  customer: mockCustomers,
  category: mockCategories,
  auth: mockAuth,
  api: mockApiResponse,
};

// Common test scenarios
export const scenarios = {
  // Test component with loading state
  withLoading: (Component: React.ComponentType<any>, props = {}) => {
    return renderWithProviders(<Component {...props} loading={true} />);
  },

  // Test component with error state
  withError: (Component: React.ComponentType<any>, props = {}) => {
    return renderWithProviders(<Component {...props} error="Test error message" />);
  },

  // Test component with empty state
  withEmptyData: (Component: React.ComponentType<any>, props = {}) => {
    return renderWithProviders(<Component {...props} data={[]} />);
  },

  // Test component with authenticated user
  withAuth: (Component: React.ComponentType<any>, props = {}, user = mockAuth.createAuthUser()) => {
    return renderWithProviders(<Component {...props} />, { authUser: user });
  },

  // Test component in dark theme
  withDarkTheme: (Component: React.ComponentType<any>, props = {}) => {
    return renderWithProviders(<Component {...props} />, { theme: 'dark' });
  },
};

// Performance testing utilities
export const performanceHelpers = {
  measureRenderTime: async (renderFn: () => void) => {
    const start = performance.now();
    renderFn();
    const end = performance.now();
    return end - start;
  },

  measureMemoryUsage: () => {
    if ('memory' in performance) {
      return (performance as any).memory;
    }
    return null;
  },
};

// Accessibility testing utilities
export const a11yHelpers = {
  checkKeyboardNavigation: async (element: HTMLElement) => {
    const { fireEvent } = await import('@testing-library/react');
    
    // Test Tab navigation
    fireEvent.keyDown(element, { key: 'Tab' });
    expect(document.activeElement).toBe(element);
    
    // Test Enter activation
    fireEvent.keyDown(element, { key: 'Enter' });
    
    // Test Escape
    fireEvent.keyDown(element, { key: 'Escape' });
  },

  checkAriaAttributes: (element: HTMLElement) => {
    const ariaAttributes = Array.from(element.attributes)
      .filter(attr => attr.name.startsWith('aria-'))
      .map(attr => ({ name: attr.name, value: attr.value }));
    
    return ariaAttributes;
  },

  checkFocusManagement: (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    return Array.from(focusableElements);
  },
};

export default {
  renderWithProviders,
  testHelpers,
  customMatchers,
  factories,
  scenarios,
  performanceHelpers,
  a11yHelpers,
};

/**
 * Theme Configuration for Dalti Admin Panel
 * Provides comprehensive theme configuration and utilities
 */

export interface ThemeColors {
  primary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
  gray: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
  success: {
    50: string;
    100: string;
    500: string;
    600: string;
    700: string;
    900: string;
  };
  warning: {
    50: string;
    100: string;
    500: string;
    600: string;
    700: string;
    900: string;
  };
  error: {
    50: string;
    100: string;
    500: string;
    600: string;
    700: string;
    900: string;
  };
}

export interface ThemeConfig {
  colors: {
    light: ThemeColors;
    dark: ThemeColors;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  shadows: {
    light: {
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    dark: {
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
  };
  typography: {
    fontFamily: {
      sans: string[];
      mono: string[];
    };
    fontSize: {
      xs: [string, string];
      sm: [string, string];
      base: [string, string];
      lg: [string, string];
      xl: [string, string];
      '2xl': [string, string];
      '3xl': [string, string];
    };
    fontWeight: {
      normal: string;
      medium: string;
      semibold: string;
      bold: string;
    };
  };
  animation: {
    duration: {
      fast: string;
      normal: string;
      slow: string;
    };
    easing: {
      ease: string;
      easeIn: string;
      easeOut: string;
      easeInOut: string;
    };
  };
}

// Dalti Brand Colors
export const daltiThemeConfig: ThemeConfig = {
  colors: {
    light: {
      primary: {
        50: '#f0f9fa',
        100: '#ccecef',
        200: '#99d8de',
        300: '#66c5cd',
        400: '#33b1bc',
        500: '#1a8a96',
        600: '#19727F',
        700: '#155b68',
        800: '#114451',
        900: '#0d2d3a',
      },
      gray: {
        50: '#f9fafb',
        100: '#f3f4f6',
        200: '#e5e7eb',
        300: '#d1d5db',
        400: '#9ca3af',
        500: '#6b7280',
        600: '#4b5563',
        700: '#374151',
        800: '#1f2937',
        900: '#111827',
      },
      success: {
        50: '#ecfdf5',
        100: '#d1fae5',
        500: '#10b981',
        600: '#059669',
        700: '#047857',
        900: '#064e3b',
      },
      warning: {
        50: '#fffbeb',
        100: '#fef3c7',
        500: '#f59e0b',
        600: '#d97706',
        700: '#b45309',
        900: '#78350f',
      },
      error: {
        50: '#fef2f2',
        100: '#fee2e2',
        500: '#ef4444',
        600: '#dc2626',
        700: '#b91c1c',
        900: '#7f1d1d',
      },
    },
    dark: {
      primary: {
        50: '#0d2d3a',
        100: '#114451',
        200: '#155b68',
        300: '#19727F',
        400: '#1a8a96',
        500: '#33b1bc',
        600: '#66c5cd',
        700: '#99d8de',
        800: '#ccecef',
        900: '#f0f9fa',
      },
      gray: {
        50: '#111827',
        100: '#1f2937',
        200: '#374151',
        300: '#4b5563',
        400: '#6b7280',
        500: '#9ca3af',
        600: '#d1d5db',
        700: '#e5e7eb',
        800: '#f3f4f6',
        900: '#f9fafb',
      },
      success: {
        50: '#064e3b',
        100: '#047857',
        500: '#10b981',
        600: '#059669',
        700: '#34d399',
        900: '#d1fae5',
      },
      warning: {
        50: '#78350f',
        100: '#b45309',
        500: '#f59e0b',
        600: '#d97706',
        700: '#fbbf24',
        900: '#fef3c7',
      },
      error: {
        50: '#7f1d1d',
        100: '#b91c1c',
        500: '#ef4444',
        600: '#dc2626',
        700: '#f87171',
        900: '#fee2e2',
      },
    },
  },
  spacing: {
    xs: '0.5rem',
    sm: '1rem',
    md: '1.5rem',
    lg: '2rem',
    xl: '3rem',
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
  },
  shadows: {
    light: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    },
    dark: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.3)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3)',
      xl: '0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3)',
    },
  },
  typography: {
    fontFamily: {
      sans: [
        'Inter',
        '-apple-system',
        'BlinkMacSystemFont',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        'sans-serif',
      ],
      mono: [
        'JetBrains Mono',
        'Fira Code',
        'Monaco',
        'Consolas',
        'Liberation Mono',
        'Courier New',
        'monospace',
      ],
    },
    fontSize: {
      xs: ['0.75rem', '1rem'],
      sm: ['0.875rem', '1.25rem'],
      base: ['1rem', '1.5rem'],
      lg: ['1.125rem', '1.75rem'],
      xl: ['1.25rem', '1.75rem'],
      '2xl': ['1.5rem', '2rem'],
      '3xl': ['1.875rem', '2.25rem'],
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },
  animation: {
    duration: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
    },
    easing: {
      ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
};

// Theme utility functions
export class ThemeUtils {
  static getColor(
    theme: 'light' | 'dark',
    colorKey: keyof ThemeColors,
    shade: keyof ThemeColors['primary']
  ): string {
    return (daltiThemeConfig.colors[theme][colorKey] as any)[shade];
  }

  static getShadow(theme: 'light' | 'dark', size: keyof ThemeConfig['shadows']['light']): string {
    return daltiThemeConfig.shadows[theme][size];
  }

  static generateCSSVariables(theme: 'light' | 'dark'): Record<string, string> {
    const colors = daltiThemeConfig.colors[theme];
    const shadows = daltiThemeConfig.shadows[theme];
    const variables: Record<string, string> = {};

    // Color variables
    Object.entries(colors).forEach(([colorName, colorShades]) => {
      Object.entries(colorShades as Record<string, string>).forEach(([shade, value]) => {
        variables[`--color-${colorName}-${shade}`] = value;
      });
    });

    // Shadow variables
    Object.entries(shadows).forEach(([size, value]) => {
      variables[`--shadow-${size}`] = value;
    });

    // Spacing variables
    Object.entries(daltiThemeConfig.spacing).forEach(([size, value]) => {
      variables[`--spacing-${size}`] = value;
    });

    // Border radius variables
    Object.entries(daltiThemeConfig.borderRadius).forEach(([size, value]) => {
      variables[`--radius-${size}`] = value;
    });

    return variables;
  }

  static applyCSSVariables(theme: 'light' | 'dark'): void {
    const variables = this.generateCSSVariables(theme);
    const root = document.documentElement;

    Object.entries(variables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }

  static getContrastColor(backgroundColor: string): string {
    // Simple contrast calculation
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }

  static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  }

  static rgbToHex(r: number, g: number, b: number): string {
    return '#' + [r, g, b].map(x => x.toString(16).padStart(2, '0')).join('');
  }

  static adjustBrightness(hex: string, percent: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const adjust = (value: number) => {
      const adjusted = Math.round(value * (1 + percent / 100));
      return Math.max(0, Math.min(255, adjusted));
    };

    return this.rgbToHex(adjust(rgb.r), adjust(rgb.g), adjust(rgb.b));
  }

  static generateColorPalette(baseColor: string): ThemeColors['primary'] {
    return {
      50: this.adjustBrightness(baseColor, 80),
      100: this.adjustBrightness(baseColor, 60),
      200: this.adjustBrightness(baseColor, 40),
      300: this.adjustBrightness(baseColor, 20),
      400: this.adjustBrightness(baseColor, 10),
      500: baseColor,
      600: this.adjustBrightness(baseColor, -10),
      700: this.adjustBrightness(baseColor, -20),
      800: this.adjustBrightness(baseColor, -30),
      900: this.adjustBrightness(baseColor, -40),
    };
  }
}

// Theme presets
export const themePresets = {
  dalti: daltiThemeConfig,
  teal: daltiThemeConfig, // Alias for the main Dalti theme
  blue: {
    ...daltiThemeConfig,
    colors: {
      ...daltiThemeConfig.colors,
      light: {
        ...daltiThemeConfig.colors.light,
        primary: ThemeUtils.generateColorPalette('#3b82f6'),
      },
      dark: {
        ...daltiThemeConfig.colors.dark,
        primary: ThemeUtils.generateColorPalette('#60a5fa'),
      },
    },
  },
  green: {
    ...daltiThemeConfig,
    colors: {
      ...daltiThemeConfig.colors,
      light: {
        ...daltiThemeConfig.colors.light,
        primary: ThemeUtils.generateColorPalette('#10b981'),
      },
      dark: {
        ...daltiThemeConfig.colors.dark,
        primary: ThemeUtils.generateColorPalette('#34d399'),
      },
    },
  },
  purple: {
    ...daltiThemeConfig,
    colors: {
      ...daltiThemeConfig.colors,
      light: {
        ...daltiThemeConfig.colors.light,
        primary: ThemeUtils.generateColorPalette('#8b5cf6'),
      },
      dark: {
        ...daltiThemeConfig.colors.dark,
        primary: ThemeUtils.generateColorPalette('#a78bfa'),
      },
    },
  },
} as const;

// Export default theme
export default daltiThemeConfig;

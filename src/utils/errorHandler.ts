// Centralized Error Handling System for Dalti Admin Panel

import toast from 'react-hot-toast';
import { ApiError } from '../types';

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Error categories
export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  SERVER = 'server',
  CLIENT = 'client',
  UNKNOWN = 'unknown',
}

// Enhanced error interface
export interface EnhancedError extends ApiError {
  severity: ErrorSeverity;
  category: ErrorCategory;
  userMessage: string;
  technicalMessage: string;
  retryable: boolean;
  retryCount?: number;
  maxRetries?: number;
  context?: Record<string, any>;
}

// Error handler configuration
interface ErrorHandlerConfig {
  showToast: boolean;
  logToConsole: boolean;
  logToServer: boolean;
  retryEnabled: boolean;
  maxRetries: number;
  retryDelay: number;
}

class ErrorHandler {
  private config: ErrorHandlerConfig = {
    showToast: true,
    logToConsole: true,
    logToServer: false, // Enable when logging endpoint is available
    retryEnabled: true,
    maxRetries: 3,
    retryDelay: 1000,
  };

  private errorCounts = new Map<string, number>();

  /**
   * Main error handling method
   */
  handle(error: any, context?: Record<string, any>): EnhancedError {
    const enhancedError = this.enhanceError(error, context);
    
    // Log error
    if (this.config.logToConsole) {
      this.logToConsole(enhancedError);
    }

    if (this.config.logToServer) {
      this.logToServer(enhancedError);
    }

    // Show user notification
    if (this.config.showToast) {
      this.showUserNotification(enhancedError);
    }

    // Track error frequency
    this.trackError(enhancedError);

    return enhancedError;
  }

  /**
   * Enhance basic error with additional metadata
   */
  private enhanceError(error: any, context?: Record<string, any>): EnhancedError {
    let baseError: ApiError;

    // Handle different error types
    if (this.isApiError(error)) {
      baseError = error;
    } else if (error instanceof Error) {
      baseError = {
        code: 'CLIENT_ERROR',
        message: error.message,
        details: { stack: error.stack },
        timestamp: new Date().toISOString(),
      };
    } else if (typeof error === 'string') {
      baseError = {
        code: 'GENERIC_ERROR',
        message: error,
        details: null,
        timestamp: new Date().toISOString(),
      };
    } else {
      baseError = {
        code: 'UNKNOWN_ERROR',
        message: 'An unknown error occurred',
        details: error,
        timestamp: new Date().toISOString(),
      };
    }

    // Categorize and enhance error
    const category = this.categorizeError(baseError);
    const severity = this.determineSeverity(baseError, category);
    const userMessage = this.generateUserMessage(baseError, category);
    const retryable = this.isRetryable(baseError, category);

    return {
      ...baseError,
      severity,
      category,
      userMessage,
      technicalMessage: baseError.message,
      retryable,
      maxRetries: this.config.maxRetries,
      context,
    };
  }

  /**
   * Check if error is an API error
   */
  private isApiError(error: any): error is ApiError {
    return error && typeof error === 'object' && 'code' in error && 'message' in error;
  }

  /**
   * Categorize error based on code and message
   */
  private categorizeError(error: ApiError): ErrorCategory {
    const code = error.code.toLowerCase();
    const message = error.message.toLowerCase();

    if (code.includes('network') || message.includes('network')) {
      return ErrorCategory.NETWORK;
    }

    if (code.includes('401') || code.includes('unauthorized') || message.includes('authentication')) {
      return ErrorCategory.AUTHENTICATION;
    }

    if (code.includes('403') || code.includes('forbidden') || message.includes('access')) {
      return ErrorCategory.AUTHORIZATION;
    }

    if (code.includes('400') || code.includes('422') || message.includes('validation')) {
      return ErrorCategory.VALIDATION;
    }

    if (code.includes('5') || message.includes('server')) {
      return ErrorCategory.SERVER;
    }

    if (code.includes('4')) {
      return ErrorCategory.CLIENT;
    }

    return ErrorCategory.UNKNOWN;
  }

  /**
   * Determine error severity
   */
  private determineSeverity(error: ApiError, category: ErrorCategory): ErrorSeverity {
    // Critical errors
    if (category === ErrorCategory.AUTHENTICATION && error.code.includes('401')) {
      return ErrorSeverity.CRITICAL;
    }

    if (error.code.includes('500') || error.code.includes('503')) {
      return ErrorSeverity.HIGH;
    }

    // High severity errors
    if (category === ErrorCategory.AUTHORIZATION) {
      return ErrorSeverity.HIGH;
    }

    if (category === ErrorCategory.SERVER) {
      return ErrorSeverity.HIGH;
    }

    // Medium severity errors
    if (category === ErrorCategory.NETWORK) {
      return ErrorSeverity.MEDIUM;
    }

    if (category === ErrorCategory.VALIDATION) {
      return ErrorSeverity.MEDIUM;
    }

    // Low severity errors
    return ErrorSeverity.LOW;
  }

  /**
   * Generate user-friendly error message
   */
  private generateUserMessage(error: ApiError, category: ErrorCategory): string {
    switch (category) {
      case ErrorCategory.NETWORK:
        return 'Connection problem. Please check your internet connection and try again.';
      
      case ErrorCategory.AUTHENTICATION:
        return 'Your session has expired. Please log in again.';
      
      case ErrorCategory.AUTHORIZATION:
        return 'You don\'t have permission to perform this action.';
      
      case ErrorCategory.VALIDATION:
        return error.message || 'Please check your input and try again.';
      
      case ErrorCategory.SERVER:
        return 'Server is temporarily unavailable. Please try again in a few moments.';
      
      case ErrorCategory.CLIENT:
        return error.message || 'Something went wrong. Please try again.';
      
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /**
   * Determine if error is retryable
   */
  private isRetryable(error: ApiError, category: ErrorCategory): boolean {
    // Never retry authentication or authorization errors
    if (category === ErrorCategory.AUTHENTICATION || category === ErrorCategory.AUTHORIZATION) {
      return false;
    }

    // Never retry validation errors
    if (category === ErrorCategory.VALIDATION) {
      return false;
    }

    // Retry network and server errors
    if (category === ErrorCategory.NETWORK || category === ErrorCategory.SERVER) {
      return true;
    }

    // Retry specific HTTP status codes
    const retryableCodes = ['408', '429', '500', '502', '503', '504'];
    return retryableCodes.some(code => error.code.includes(code));
  }

  /**
   * Show user notification
   */
  private showUserNotification(error: EnhancedError): void {
    const options = {
      duration: this.getToastDuration(error.severity),
      style: this.getToastStyle(error.severity),
    };

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        toast.error(error.userMessage, options);
        break;
      
      case ErrorSeverity.MEDIUM:
        toast.error(error.userMessage, options);
        break;
      
      case ErrorSeverity.LOW:
        toast(error.userMessage, options);
        break;
    }
  }

  /**
   * Get toast duration based on severity
   */
  private getToastDuration(severity: ErrorSeverity): number {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return 8000;
      case ErrorSeverity.HIGH:
        return 6000;
      case ErrorSeverity.MEDIUM:
        return 4000;
      case ErrorSeverity.LOW:
        return 3000;
      default:
        return 4000;
    }
  }

  /**
   * Get toast style based on severity
   */
  private getToastStyle(severity: ErrorSeverity): Record<string, string> {
    const baseStyle = {
      background: '#363636',
      color: '#fff',
      borderRadius: '8px',
    };

    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return { ...baseStyle, background: '#dc2626', fontWeight: 'bold' };
      case ErrorSeverity.HIGH:
        return { ...baseStyle, background: '#ea580c' };
      case ErrorSeverity.MEDIUM:
        return { ...baseStyle, background: '#d97706' };
      case ErrorSeverity.LOW:
        return baseStyle;
      default:
        return baseStyle;
    }
  }

  /**
   * Log error to console
   */
  private logToConsole(error: EnhancedError): void {
    const logMethod = this.getConsoleLogMethod(error.severity);
    
    console.group(`🚨 ${error.severity.toUpperCase()} ERROR - ${error.category.toUpperCase()}`);
    console[logMethod]('User Message:', error.userMessage);
    console[logMethod]('Technical Message:', error.technicalMessage);
    console[logMethod]('Error Code:', error.code);
    console[logMethod]('Timestamp:', error.timestamp);
    
    if (error.details) {
      console[logMethod]('Details:', error.details);
    }
    
    if (error.context) {
      console[logMethod]('Context:', error.context);
    }
    
    console.groupEnd();
  }

  /**
   * Get appropriate console log method
   */
  private getConsoleLogMethod(severity: ErrorSeverity): 'error' | 'warn' | 'log' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.LOW:
        return 'log';
      default:
        return 'error';
    }
  }

  /**
   * Log error to server (placeholder for future implementation)
   */
  private async logToServer(error: EnhancedError): Promise<void> {
    try {
      // TODO: Implement server logging when endpoint is available
      // await httpClient.post('/api/admin/logs/errors', {
      //   error: {
      //     code: error.code,
      //     message: error.technicalMessage,
      //     severity: error.severity,
      //     category: error.category,
      //     timestamp: error.timestamp,
      //     details: error.details,
      //     context: error.context,
      //   },
      //   userAgent: navigator.userAgent,
      //   url: window.location.href,
      // });
    } catch (loggingError) {
      console.warn('Failed to log error to server:', loggingError);
    }
  }

  /**
   * Track error frequency
   */
  private trackError(error: EnhancedError): void {
    const key = `${error.category}-${error.code}`;
    const count = this.errorCounts.get(key) || 0;
    this.errorCounts.set(key, count + 1);

    // Alert if error occurs too frequently
    if (count > 5) {
      console.warn(`High frequency error detected: ${key} (${count} occurrences)`);
    }
  }

  /**
   * Retry operation with exponential backoff
   */
  async retry<T>(
    operation: () => Promise<T>,
    error: EnhancedError,
    retryCount = 0
  ): Promise<T> {
    if (!error.retryable || retryCount >= (error.maxRetries || this.config.maxRetries)) {
      throw error;
    }

    const delay = this.config.retryDelay * Math.pow(2, retryCount);
    
    console.log(`Retrying operation in ${delay}ms (attempt ${retryCount + 1}/${error.maxRetries})`);
    
    await new Promise(resolve => setTimeout(resolve, delay));

    try {
      return await operation();
    } catch (retryError) {
      const enhancedRetryError = this.enhanceError(retryError);
      enhancedRetryError.retryCount = retryCount + 1;
      
      return this.retry(operation, enhancedRetryError, retryCount + 1);
    }
  }

  /**
   * Clear error tracking data
   */
  clearErrorTracking(): void {
    this.errorCounts.clear();
  }

  /**
   * Get error statistics
   */
  getErrorStats(): Record<string, number> {
    return Object.fromEntries(this.errorCounts);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Export singleton instance
export const errorHandler = new ErrorHandler();

// Convenience functions
export const handleError = (error: any, context?: Record<string, any>) => 
  errorHandler.handle(error, context);

export const retryOperation = <T>(operation: () => Promise<T>, error: EnhancedError) =>
  errorHandler.retry(operation, error);

export default errorHandler;

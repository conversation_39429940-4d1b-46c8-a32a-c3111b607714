/**
 * Security Audit and Testing Utilities
 * Provides comprehensive security auditing and vulnerability testing
 */

// Security audit result interfaces
export interface SecurityAuditResult {
  score: number; // 0-100
  grade: 'A+' | 'A' | 'B' | 'C' | 'D' | 'F';
  vulnerabilities: SecurityVulnerability[];
  recommendations: SecurityRecommendation[];
  compliance: ComplianceCheck[];
  summary: AuditSummary;
}

export interface SecurityVulnerability {
  id: string;
  type: VulnerabilityType;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  title: string;
  description: string;
  impact: string;
  remediation: string;
  cwe?: string; // Common Weakness Enumeration
  cvss?: number; // Common Vulnerability Scoring System
  evidence?: any;
}

export interface SecurityRecommendation {
  id: string;
  category: 'authentication' | 'authorization' | 'data-protection' | 'network' | 'configuration';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  implementation: string;
  estimatedEffort: 'low' | 'medium' | 'high';
}

export interface ComplianceCheck {
  standard: 'OWASP' | 'GDPR' | 'SOC2' | 'ISO27001' | 'NIST';
  requirement: string;
  status: 'compliant' | 'non-compliant' | 'partial' | 'not-applicable';
  details: string;
}

export interface AuditSummary {
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;
  complianceScore: number;
}

export enum VulnerabilityType {
  XSS = 'XSS',
  CSRF = 'CSRF',
  INJECTION = 'INJECTION',
  BROKEN_AUTH = 'BROKEN_AUTH',
  SENSITIVE_DATA = 'SENSITIVE_DATA',
  XML_EXTERNAL_ENTITIES = 'XML_EXTERNAL_ENTITIES',
  BROKEN_ACCESS_CONTROL = 'BROKEN_ACCESS_CONTROL',
  SECURITY_MISCONFIGURATION = 'SECURITY_MISCONFIGURATION',
  INSECURE_DESERIALIZATION = 'INSECURE_DESERIALIZATION',
  VULNERABLE_COMPONENTS = 'VULNERABLE_COMPONENTS',
  INSUFFICIENT_LOGGING = 'INSUFFICIENT_LOGGING',
}

// Security audit engine
export class SecurityAuditor {
  private vulnerabilities: SecurityVulnerability[] = [];
  private recommendations: SecurityRecommendation[] = [];
  private complianceChecks: ComplianceCheck[] = [];

  async performAudit(): Promise<SecurityAuditResult> {
    console.log('🔒 Starting security audit...');

    // Reset previous results
    this.vulnerabilities = [];
    this.recommendations = [];
    this.complianceChecks = [];

    // Perform various security checks
    await this.checkCSP();
    await this.checkSecurityHeaders();
    await this.checkAuthentication();
    await this.checkDataProtection();
    await this.checkInputValidation();
    await this.checkSessionManagement();
    await this.checkErrorHandling();
    await this.checkLogging();
    await this.checkDependencies();
    await this.checkConfiguration();

    // Calculate score and grade
    const summary = this.calculateSummary();
    const score = this.calculateScore(summary);
    const grade = this.calculateGrade(score);

    return {
      score,
      grade,
      vulnerabilities: this.vulnerabilities,
      recommendations: this.recommendations,
      compliance: this.complianceChecks,
      summary,
    };
  }

  private async checkCSP(): Promise<void> {
    const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    
    if (!cspMeta) {
      this.addVulnerability({
        type: VulnerabilityType.SECURITY_MISCONFIGURATION,
        severity: 'high',
        title: 'Missing Content Security Policy',
        description: 'No Content Security Policy (CSP) header found',
        impact: 'Increased risk of XSS attacks',
        remediation: 'Implement a strict CSP header',
        cwe: 'CWE-693',
      });
    } else {
      const cspContent = cspMeta.getAttribute('content') || '';
      
      // Check for unsafe directives
      if (cspContent.includes("'unsafe-eval'")) {
        this.addVulnerability({
          type: VulnerabilityType.XSS,
          severity: 'medium',
          title: 'Unsafe CSP directive: unsafe-eval',
          description: 'CSP allows unsafe-eval which can enable XSS attacks',
          impact: 'Potential code injection vulnerabilities',
          remediation: 'Remove unsafe-eval and use safer alternatives',
          cwe: 'CWE-79',
        });
      }

      if (cspContent.includes("'unsafe-inline'")) {
        this.addRecommendation({
          category: 'configuration',
          priority: 'medium',
          title: 'Reduce unsafe-inline usage',
          description: 'CSP contains unsafe-inline which reduces security',
          implementation: 'Use nonces or hashes for inline scripts and styles',
          estimatedEffort: 'medium',
        });
      }
    }

    this.addComplianceCheck({
      standard: 'OWASP',
      requirement: 'A05:2021 – Security Misconfiguration',
      status: cspMeta ? 'compliant' : 'non-compliant',
      details: 'Content Security Policy implementation',
    });
  }

  private async checkSecurityHeaders(): Promise<void> {
    const requiredHeaders = [
      'X-Content-Type-Options',
      'X-Frame-Options',
      'X-XSS-Protection',
      'Referrer-Policy',
    ];

    const missingHeaders = requiredHeaders.filter(header => 
      !document.querySelector(`meta[http-equiv="${header}"]`)
    );

    if (missingHeaders.length > 0) {
      this.addVulnerability({
        type: VulnerabilityType.SECURITY_MISCONFIGURATION,
        severity: 'medium',
        title: 'Missing Security Headers',
        description: `Missing security headers: ${missingHeaders.join(', ')}`,
        impact: 'Increased attack surface',
        remediation: 'Implement all recommended security headers',
        cwe: 'CWE-693',
        evidence: { missingHeaders },
      });
    }

    // Check HTTPS
    if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
      this.addVulnerability({
        type: VulnerabilityType.SENSITIVE_DATA,
        severity: 'critical',
        title: 'Insecure HTTP Connection',
        description: 'Application is not served over HTTPS',
        impact: 'Data transmission is not encrypted',
        remediation: 'Enforce HTTPS for all connections',
        cwe: 'CWE-319',
      });
    }
  }

  private async checkAuthentication(): Promise<void> {
    const token = localStorage.getItem('dalti_admin_token');
    
    if (token) {
      // Check token structure
      const parts = token.split('.');
      if (parts.length !== 3) {
        this.addVulnerability({
          type: VulnerabilityType.BROKEN_AUTH,
          severity: 'high',
          title: 'Invalid Token Format',
          description: 'Authentication token does not follow JWT format',
          impact: 'Potential authentication bypass',
          remediation: 'Use properly formatted JWT tokens',
          cwe: 'CWE-287',
        });
      } else {
        try {
          const payload = JSON.parse(atob(parts[1]));
          const now = Math.floor(Date.now() / 1000);
          
          // Check token expiration
          if (!payload.exp) {
            this.addVulnerability({
              type: VulnerabilityType.BROKEN_AUTH,
              severity: 'medium',
              title: 'Token Without Expiration',
              description: 'JWT token does not have expiration claim',
              impact: 'Tokens may remain valid indefinitely',
              remediation: 'Add expiration claim to JWT tokens',
              cwe: 'CWE-613',
            });
          } else if (payload.exp - now > 86400) { // More than 24 hours
            this.addRecommendation({
              category: 'authentication',
              priority: 'medium',
              title: 'Long Token Expiration',
              description: 'JWT token has very long expiration time',
              implementation: 'Reduce token expiration time and implement refresh tokens',
              estimatedEffort: 'medium',
            });
          }
        } catch (error) {
          this.addVulnerability({
            type: VulnerabilityType.BROKEN_AUTH,
            severity: 'high',
            title: 'Malformed Token Payload',
            description: 'Cannot decode JWT token payload',
            impact: 'Authentication system may be compromised',
            remediation: 'Validate and fix token generation',
            cwe: 'CWE-287',
          });
        }
      }
    }

    this.addComplianceCheck({
      standard: 'OWASP',
      requirement: 'A07:2021 – Identification and Authentication Failures',
      status: token ? 'compliant' : 'partial',
      details: 'JWT token authentication implementation',
    });
  }

  private async checkDataProtection(): Promise<void> {
    // Check for sensitive data in localStorage
    const sensitiveKeys = ['password', 'secret', 'key', 'token'];
    const localStorageKeys = Object.keys(localStorage);
    
    const exposedSensitiveData = localStorageKeys.filter(key =>
      sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))
    );

    if (exposedSensitiveData.length > 0) {
      this.addVulnerability({
        type: VulnerabilityType.SENSITIVE_DATA,
        severity: 'medium',
        title: 'Sensitive Data in Local Storage',
        description: 'Potentially sensitive data stored in localStorage',
        impact: 'Data may be accessible to malicious scripts',
        remediation: 'Encrypt sensitive data or use secure storage',
        cwe: 'CWE-312',
        evidence: { exposedKeys: exposedSensitiveData },
      });
    }

    // Check for console.log statements that might leak data
    const scripts = Array.from(document.scripts);
    let hasConsoleLogging = false;
    
    scripts.forEach(script => {
      if (script.textContent && script.textContent.includes('console.log')) {
        hasConsoleLogging = true;
      }
    });

    if (hasConsoleLogging && window.location.hostname !== 'localhost') {
      this.addRecommendation({
        category: 'data-protection',
        priority: 'low',
        title: 'Remove Console Logging',
        description: 'Console logging detected in production',
        implementation: 'Remove or disable console.log statements in production',
        estimatedEffort: 'low',
      });
    }

    this.addComplianceCheck({
      standard: 'GDPR',
      requirement: 'Article 32 - Security of processing',
      status: exposedSensitiveData.length === 0 ? 'compliant' : 'non-compliant',
      details: 'Data protection and encryption measures',
    });
  }

  private async checkInputValidation(): Promise<void> {
    // Check for forms without proper validation
    const forms = document.querySelectorAll('form');
    let formsWithoutValidation = 0;

    forms.forEach(form => {
      const inputs = form.querySelectorAll('input, textarea');
      let hasValidation = false;

      inputs.forEach(input => {
        if (input.hasAttribute('required') || 
            input.hasAttribute('pattern') || 
            input.hasAttribute('minlength') ||
            input.hasAttribute('maxlength')) {
          hasValidation = true;
        }
      });

      if (!hasValidation) {
        formsWithoutValidation++;
      }
    });

    if (formsWithoutValidation > 0) {
      this.addRecommendation({
        category: 'data-protection',
        priority: 'medium',
        title: 'Implement Input Validation',
        description: `${formsWithoutValidation} forms lack proper input validation`,
        implementation: 'Add client-side and server-side input validation',
        estimatedEffort: 'medium',
      });
    }

    this.addComplianceCheck({
      standard: 'OWASP',
      requirement: 'A03:2021 – Injection',
      status: formsWithoutValidation === 0 ? 'compliant' : 'partial',
      details: 'Input validation and sanitization',
    });
  }

  private async checkSessionManagement(): Promise<void> {
    // Check for secure session management
    const hasSecureStorage = !!sessionStorage.getItem('csrf_token');
    
    if (!hasSecureStorage) {
      this.addRecommendation({
        category: 'authentication',
        priority: 'medium',
        title: 'Implement CSRF Protection',
        description: 'No CSRF token found in session storage',
        implementation: 'Implement CSRF token generation and validation',
        estimatedEffort: 'medium',
      });
    }

    // Check session timeout
    const lastActivity = localStorage.getItem('last_activity');
    if (!lastActivity) {
      this.addRecommendation({
        category: 'authentication',
        priority: 'low',
        title: 'Implement Session Timeout',
        description: 'No session timeout mechanism detected',
        implementation: 'Add automatic session timeout based on inactivity',
        estimatedEffort: 'low',
      });
    }
  }

  private async checkErrorHandling(): Promise<void> {
    // Check for error boundaries
    const hasErrorBoundary = !!document.querySelector('[data-error-boundary]');
    
    if (!hasErrorBoundary) {
      this.addRecommendation({
        category: 'configuration',
        priority: 'medium',
        title: 'Implement Error Boundaries',
        description: 'No React error boundaries detected',
        implementation: 'Add error boundaries to prevent information disclosure',
        estimatedEffort: 'medium',
      });
    }
  }

  private async checkLogging(): Promise<void> {
    // Check for security event logging
    const hasSecurityLogging = !!window.SecurityMonitor;
    
    this.addComplianceCheck({
      standard: 'OWASP',
      requirement: 'A09:2021 – Security Logging and Monitoring Failures',
      status: hasSecurityLogging ? 'compliant' : 'non-compliant',
      details: 'Security event logging and monitoring',
    });

    if (!hasSecurityLogging) {
      this.addVulnerability({
        type: VulnerabilityType.INSUFFICIENT_LOGGING,
        severity: 'medium',
        title: 'Insufficient Security Logging',
        description: 'No security event logging system detected',
        impact: 'Security incidents may go undetected',
        remediation: 'Implement comprehensive security logging',
        cwe: 'CWE-778',
      });
    }
  }

  private async checkDependencies(): Promise<void> {
    // This would typically require build-time analysis
    // For now, check for known vulnerable patterns
    
    this.addRecommendation({
      category: 'configuration',
      priority: 'high',
      title: 'Regular Dependency Audits',
      description: 'Perform regular security audits of dependencies',
      implementation: 'Use npm audit or similar tools regularly',
      estimatedEffort: 'low',
    });

    this.addComplianceCheck({
      standard: 'OWASP',
      requirement: 'A06:2021 – Vulnerable and Outdated Components',
      status: 'partial',
      details: 'Dependency vulnerability management',
    });
  }

  private async checkConfiguration(): Promise<void> {
    // Check for development mode in production
    if (process.env.NODE_ENV !== 'production' && window.location.hostname !== 'localhost') {
      this.addVulnerability({
        type: VulnerabilityType.SECURITY_MISCONFIGURATION,
        severity: 'high',
        title: 'Development Mode in Production',
        description: 'Application running in development mode in production',
        impact: 'Increased attack surface and information disclosure',
        remediation: 'Ensure production builds use NODE_ENV=production',
        cwe: 'CWE-489',
      });
    }

    // Check for exposed debug information
    if (window.location.hostname !== 'localhost' && (window.console.log.toString().includes('native') === false)) {
      this.addRecommendation({
        category: 'configuration',
        priority: 'low',
        title: 'Disable Debug Console',
        description: 'Console debugging may be enabled in production',
        implementation: 'Disable or override console methods in production',
        estimatedEffort: 'low',
      });
    }
  }

  private addVulnerability(vuln: Omit<SecurityVulnerability, 'id'>): void {
    this.vulnerabilities.push({
      id: `vuln_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...vuln,
    });
  }

  private addRecommendation(rec: Omit<SecurityRecommendation, 'id'>): void {
    this.recommendations.push({
      id: `rec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...rec,
    });
  }

  private addComplianceCheck(check: ComplianceCheck): void {
    this.complianceChecks.push(check);
  }

  private calculateSummary(): AuditSummary {
    const totalChecks = this.vulnerabilities.length + this.recommendations.length;
    const passedChecks = this.complianceChecks.filter(c => c.status === 'compliant').length;
    const failedChecks = this.vulnerabilities.length;

    return {
      totalChecks,
      passedChecks,
      failedChecks,
      criticalIssues: this.vulnerabilities.filter(v => v.severity === 'critical').length,
      highIssues: this.vulnerabilities.filter(v => v.severity === 'high').length,
      mediumIssues: this.vulnerabilities.filter(v => v.severity === 'medium').length,
      lowIssues: this.vulnerabilities.filter(v => v.severity === 'low').length,
      complianceScore: this.complianceChecks.length > 0 ? 
        (passedChecks / this.complianceChecks.length) * 100 : 100,
    };
  }

  private calculateScore(summary: AuditSummary): number {
    let score = 100;

    // Deduct points for vulnerabilities
    score -= summary.criticalIssues * 25;
    score -= summary.highIssues * 15;
    score -= summary.mediumIssues * 10;
    score -= summary.lowIssues * 5;

    // Factor in compliance score
    score = (score + summary.complianceScore) / 2;

    return Math.max(0, Math.min(100, score));
  }

  private calculateGrade(score: number): SecurityAuditResult['grade'] {
    if (score >= 95) return 'A+';
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }
}

// Security audit utilities
export class SecurityAuditUtils {
  static async runQuickAudit(): Promise<SecurityAuditResult> {
    const auditor = new SecurityAuditor();
    return await auditor.performAudit();
  }

  static generateAuditReport(result: SecurityAuditResult): string {
    let report = '🔒 Security Audit Report\n';
    report += '========================\n\n';
    report += `Overall Score: ${result.score}/100 (Grade: ${result.grade})\n\n`;

    if (result.vulnerabilities.length > 0) {
      report += 'Vulnerabilities:\n';
      result.vulnerabilities.forEach((vuln, index) => {
        report += `  ${index + 1}. [${vuln.severity.toUpperCase()}] ${vuln.title}\n`;
        report += `     ${vuln.description}\n`;
        report += `     Remediation: ${vuln.remediation}\n\n`;
      });
    }

    if (result.recommendations.length > 0) {
      report += 'Recommendations:\n';
      result.recommendations.forEach((rec, index) => {
        report += `  ${index + 1}. [${rec.priority.toUpperCase()}] ${rec.title}\n`;
        report += `     ${rec.description}\n`;
        report += `     Implementation: ${rec.implementation}\n\n`;
      });
    }

    report += `Compliance Score: ${result.summary.complianceScore.toFixed(1)}%\n`;
    report += `Total Issues: ${result.summary.failedChecks}\n`;
    report += `Critical: ${result.summary.criticalIssues}, High: ${result.summary.highIssues}, Medium: ${result.summary.mediumIssues}, Low: ${result.summary.lowIssues}\n`;

    return report;
  }

  static exportAuditResults(result: SecurityAuditResult): void {
    const report = this.generateAuditReport(result);
    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `security-audit-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

export default SecurityAuditor;

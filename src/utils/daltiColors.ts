/**
 * Dal<PERSON> Brand Colors and Utilities
 * Centralized color management for the Dalti admin panel
 */

export const daltiColors = {
  // Primary Brand Colors
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb', // Main brand color
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },

  // Secondary Brand Colors
  secondary: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981', // Secondary brand color
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
  },

  // Accent Colors
  accent: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // Accent color
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },

  // Status Colors
  status: {
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },

  // Neutral Colors
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
} as const;

/**
 * Get status color based on status type
 */
export const getStatusColor = (status: string): string => {
  const statusMap: Record<string, string> = {
    // Provider statuses
    verified: daltiColors.status.success,
    pending: daltiColors.status.warning,
    rejected: daltiColors.status.error,
    suspended: daltiColors.status.error,
    
    // Customer statuses
    active: daltiColors.status.success,
    inactive: daltiColors.gray[400],
    blocked: daltiColors.status.error,
    
    // General statuses
    approved: daltiColors.status.success,
    completed: daltiColors.status.success,
    success: daltiColors.status.success,
    warning: daltiColors.status.warning,
    error: daltiColors.status.error,
    failed: daltiColors.status.error,
    info: daltiColors.status.info,
    processing: daltiColors.status.info,
    
    // System statuses
    online: daltiColors.status.success,
    offline: daltiColors.gray[400],
    maintenance: daltiColors.status.warning,
  };

  return statusMap[status.toLowerCase()] || daltiColors.gray[400];
};

/**
 * Get status background color class for Tailwind
 */
export const getStatusBgClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    // Provider statuses
    verified: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    rejected: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    suspended: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    
    // Customer statuses
    active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
    blocked: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    
    // General statuses
    approved: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    success: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    info: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    processing: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    
    // System statuses
    online: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    offline: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
    maintenance: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  };

  return statusMap[status.toLowerCase()] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
};

/**
 * Get priority color based on priority level
 */
export const getPriorityColor = (priority: string): string => {
  const priorityMap: Record<string, string> = {
    critical: daltiColors.status.error,
    high: '#f97316', // orange-500
    medium: daltiColors.status.warning,
    low: daltiColors.status.success,
  };

  return priorityMap[priority.toLowerCase()] || daltiColors.gray[400];
};

/**
 * Get priority background class for Tailwind
 */
export const getPriorityBgClass = (priority: string): string => {
  const priorityMap: Record<string, string> = {
    critical: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  };

  return priorityMap[priority.toLowerCase()] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
};

/**
 * Generate gradient background styles
 */
export const getGradientStyle = (type: 'primary' | 'secondary' | 'accent' = 'primary'): React.CSSProperties => {
  const gradients = {
    primary: `linear-gradient(135deg, ${daltiColors.primary[600]} 0%, ${daltiColors.primary[700]} 100%)`,
    secondary: `linear-gradient(135deg, ${daltiColors.secondary[600]} 0%, ${daltiColors.secondary[700]} 100%)`,
    accent: `linear-gradient(135deg, ${daltiColors.accent[500]} 0%, ${daltiColors.accent[600]} 100%)`,
  };

  return {
    background: gradients[type],
  };
};

/**
 * Get chart colors for analytics
 */
export const getChartColors = () => ({
  primary: daltiColors.primary[600],
  secondary: daltiColors.secondary[500],
  accent: daltiColors.accent[500],
  success: daltiColors.status.success,
  warning: daltiColors.status.warning,
  error: daltiColors.status.error,
  info: daltiColors.status.info,
  gray: daltiColors.gray[400],
});

/**
 * Get theme-aware colors
 */
export const getThemeColors = (isDark: boolean = false) => ({
  background: {
    primary: isDark ? '#0f172a' : '#ffffff',
    secondary: isDark ? '#1e293b' : '#f8fafc',
    tertiary: isDark ? '#334155' : '#f1f5f9',
  },
  text: {
    primary: isDark ? '#f8fafc' : '#0f172a',
    secondary: isDark ? '#cbd5e1' : '#475569',
    tertiary: isDark ? '#94a3b8' : '#64748b',
  },
  border: {
    primary: isDark ? '#334155' : '#e2e8f0',
    secondary: isDark ? '#475569' : '#f1f5f9',
  },
});

/**
 * Dalti brand constants
 */
export const daltiBrand = {
  name: 'Dalti',
  tagline: 'Professional Appointment Booking Platform',
  adminTitle: 'Dalti Admin Panel',
  colors: daltiColors,
  fonts: {
    primary: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    mono: 'JetBrains Mono, "Fira Code", Consolas, monospace',
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
  },
  borderRadius: {
    sm: '0.375rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
  },
} as const;

export default daltiColors;

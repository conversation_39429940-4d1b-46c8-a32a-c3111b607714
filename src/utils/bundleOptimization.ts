/**
 * Bundle Optimization Utilities
 * Provides tools for analyzing and optimizing bundle size and performance
 */

// Bundle analysis interface
export interface BundleAnalysis {
  totalSize: number;
  gzippedSize: number;
  chunks: ChunkInfo[];
  dependencies: DependencyInfo[];
  duplicates: DuplicateInfo[];
  recommendations: OptimizationRecommendation[];
}

export interface ChunkInfo {
  name: string;
  size: number;
  gzippedSize: number;
  modules: string[];
  loadTime?: number;
  isAsync: boolean;
  isEntry: boolean;
}

export interface DependencyInfo {
  name: string;
  version: string;
  size: number;
  gzippedSize: number;
  treeshakeable: boolean;
  sideEffects: boolean;
  usage: 'critical' | 'important' | 'optional' | 'unused';
}

export interface DuplicateInfo {
  module: string;
  chunks: string[];
  totalSize: number;
  instances: number;
}

export interface OptimizationRecommendation {
  type: 'code-split' | 'tree-shake' | 'lazy-load' | 'compress' | 'dedupe' | 'upgrade';
  priority: 'high' | 'medium' | 'low';
  description: string;
  estimatedSavings: number;
  implementation: string;
}

// Bundle analyzer class
export class BundleAnalyzer {
  private static instance: BundleAnalyzer;
  private analysisData: BundleAnalysis | null = null;

  static getInstance(): BundleAnalyzer {
    if (!BundleAnalyzer.instance) {
      BundleAnalyzer.instance = new BundleAnalyzer();
    }
    return BundleAnalyzer.instance;
  }

  // Analyze current bundle
  async analyzeBundleSize(): Promise<BundleAnalysis> {
    if (this.analysisData) {
      return this.analysisData;
    }

    const analysis: BundleAnalysis = {
      totalSize: 0,
      gzippedSize: 0,
      chunks: [],
      dependencies: [],
      duplicates: [],
      recommendations: [],
    };

    // Analyze webpack chunks if available
    if (typeof __webpack_require__ !== 'undefined' && __webpack_require__.cache) {
      analysis.chunks = this.analyzeWebpackChunks();
    }

    // Analyze dependencies
    analysis.dependencies = await this.analyzeDependencies();

    // Find duplicates
    analysis.duplicates = this.findDuplicates(analysis.chunks);

    // Generate recommendations
    analysis.recommendations = this.generateRecommendations(analysis);

    // Calculate totals
    analysis.totalSize = analysis.chunks.reduce((sum, chunk) => sum + chunk.size, 0);
    analysis.gzippedSize = analysis.chunks.reduce((sum, chunk) => sum + chunk.gzippedSize, 0);

    this.analysisData = analysis;
    return analysis;
  }

  private analyzeWebpackChunks(): ChunkInfo[] {
    const chunks: ChunkInfo[] = [];
    
    if (typeof __webpack_require__ === 'undefined') {
      return chunks;
    }

    const cache = __webpack_require__.cache || {};
    const chunkGroups = new Map<string, string[]>();

    // Group modules by chunk
    Object.keys(cache).forEach(moduleId => {
      const module = cache[moduleId];
      if (module && module.exports) {
        const chunkName = this.getChunkName(moduleId);
        if (!chunkGroups.has(chunkName)) {
          chunkGroups.set(chunkName, []);
        }
        chunkGroups.get(chunkName)!.push(moduleId);
      }
    });

    // Create chunk info
    chunkGroups.forEach((modules, chunkName) => {
      const estimatedSize = modules.length * 1024; // Rough estimate
      const estimatedGzippedSize = estimatedSize * 0.3; // Rough compression ratio

      chunks.push({
        name: chunkName,
        size: estimatedSize,
        gzippedSize: estimatedGzippedSize,
        modules,
        isAsync: chunkName !== 'main',
        isEntry: chunkName === 'main',
      });
    });

    return chunks;
  }

  private getChunkName(moduleId: string): string {
    // Extract chunk name from module ID or path
    if (moduleId.includes('node_modules')) {
      return 'vendor';
    }
    if (moduleId.includes('pages/')) {
      const match = moduleId.match(/pages\/([^/]+)/);
      return match ? `page-${match[1]}` : 'pages';
    }
    if (moduleId.includes('components/')) {
      return 'components';
    }
    return 'main';
  }

  private async analyzeDependencies(): Promise<DependencyInfo[]> {
    const dependencies: DependencyInfo[] = [];

    // Common dependencies with known sizes (in KB)
    const knownDependencies = {
      'react': { size: 42, gzippedSize: 13, treeshakeable: false, sideEffects: false, usage: 'critical' as const },
      'react-dom': { size: 130, gzippedSize: 42, treeshakeable: false, sideEffects: false, usage: 'critical' as const },
      'react-router-dom': { size: 56, gzippedSize: 18, treeshakeable: true, sideEffects: false, usage: 'critical' as const },
      '@tanstack/react-query': { size: 85, gzippedSize: 25, treeshakeable: true, sideEffects: false, usage: 'important' as const },
      'axios': { size: 47, gzippedSize: 14, treeshakeable: false, sideEffects: false, usage: 'important' as const },
      'tailwindcss': { size: 200, gzippedSize: 25, treeshakeable: true, sideEffects: false, usage: 'critical' as const },
      'lodash': { size: 528, gzippedSize: 94, treeshakeable: true, sideEffects: false, usage: 'optional' as const },
      'moment': { size: 329, gzippedSize: 67, treeshakeable: false, sideEffects: false, usage: 'optional' as const },
      'date-fns': { size: 78, gzippedSize: 15, treeshakeable: true, sideEffects: false, usage: 'optional' as const },
    };

    Object.entries(knownDependencies).forEach(([name, info]) => {
      dependencies.push({
        name,
        version: '1.0.0', // Placeholder
        size: info.size * 1024,
        gzippedSize: info.gzippedSize * 1024,
        treeshakeable: info.treeshakeable,
        sideEffects: info.sideEffects,
        usage: info.usage,
      });
    });

    return dependencies;
  }

  private findDuplicates(chunks: ChunkInfo[]): DuplicateInfo[] {
    const duplicates: DuplicateInfo[] = [];
    const moduleChunkMap = new Map<string, string[]>();

    // Map modules to chunks
    chunks.forEach(chunk => {
      chunk.modules.forEach(module => {
        if (!moduleChunkMap.has(module)) {
          moduleChunkMap.set(module, []);
        }
        moduleChunkMap.get(module)!.push(chunk.name);
      });
    });

    // Find modules in multiple chunks
    moduleChunkMap.forEach((chunkNames, module) => {
      if (chunkNames.length > 1) {
        duplicates.push({
          module,
          chunks: chunkNames,
          totalSize: chunkNames.length * 1024, // Estimated
          instances: chunkNames.length,
        });
      }
    });

    return duplicates;
  }

  private generateRecommendations(analysis: BundleAnalysis): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = [];

    // Large chunks recommendation
    const largeChunks = analysis.chunks.filter(chunk => chunk.size > 250 * 1024); // > 250KB
    if (largeChunks.length > 0) {
      recommendations.push({
        type: 'code-split',
        priority: 'high',
        description: `Split large chunks: ${largeChunks.map(c => c.name).join(', ')}`,
        estimatedSavings: largeChunks.reduce((sum, chunk) => sum + chunk.size * 0.3, 0),
        implementation: 'Use React.lazy() and dynamic imports for route-based code splitting',
      });
    }

    // Unused dependencies
    const unusedDeps = analysis.dependencies.filter(dep => dep.usage === 'unused');
    if (unusedDeps.length > 0) {
      recommendations.push({
        type: 'tree-shake',
        priority: 'medium',
        description: `Remove unused dependencies: ${unusedDeps.map(d => d.name).join(', ')}`,
        estimatedSavings: unusedDeps.reduce((sum, dep) => sum + dep.size, 0),
        implementation: 'Remove unused imports and dependencies from package.json',
      });
    }

    // Heavy dependencies
    const heavyDeps = analysis.dependencies.filter(dep => dep.size > 100 * 1024 && dep.usage === 'optional');
    if (heavyDeps.length > 0) {
      recommendations.push({
        type: 'lazy-load',
        priority: 'medium',
        description: `Lazy load heavy optional dependencies: ${heavyDeps.map(d => d.name).join(', ')}`,
        estimatedSavings: heavyDeps.reduce((sum, dep) => sum + dep.size * 0.7, 0),
        implementation: 'Load these dependencies only when needed using dynamic imports',
      });
    }

    // Duplicates
    if (analysis.duplicates.length > 0) {
      const totalDuplicateSize = analysis.duplicates.reduce((sum, dup) => sum + dup.totalSize, 0);
      recommendations.push({
        type: 'dedupe',
        priority: 'high',
        description: `Deduplicate ${analysis.duplicates.length} modules across chunks`,
        estimatedSavings: totalDuplicateSize * 0.8,
        implementation: 'Configure webpack splitChunks to extract common modules',
      });
    }

    // Moment.js replacement
    const momentDep = analysis.dependencies.find(dep => dep.name === 'moment');
    if (momentDep) {
      recommendations.push({
        type: 'upgrade',
        priority: 'medium',
        description: 'Replace moment.js with date-fns for smaller bundle size',
        estimatedSavings: momentDep.size * 0.75,
        implementation: 'Replace moment.js imports with date-fns equivalents',
      });
    }

    // Lodash optimization
    const lodashDep = analysis.dependencies.find(dep => dep.name === 'lodash');
    if (lodashDep) {
      recommendations.push({
        type: 'tree-shake',
        priority: 'medium',
        description: 'Use individual lodash functions instead of full library',
        estimatedSavings: lodashDep.size * 0.8,
        implementation: 'Import specific functions: import debounce from "lodash/debounce"',
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  // Get bundle size report
  getBundleSizeReport(): string {
    if (!this.analysisData) {
      return 'Bundle analysis not available. Run analyzeBundleSize() first.';
    }

    const { totalSize, gzippedSize, chunks, recommendations } = this.analysisData;

    let report = '📦 Bundle Size Report\n';
    report += '===================\n\n';
    report += `Total Size: ${this.formatBytes(totalSize)}\n`;
    report += `Gzipped Size: ${this.formatBytes(gzippedSize)}\n\n`;

    report += 'Chunks:\n';
    chunks.forEach(chunk => {
      report += `  ${chunk.name}: ${this.formatBytes(chunk.size)} (${this.formatBytes(chunk.gzippedSize)} gzipped)\n`;
    });

    if (recommendations.length > 0) {
      report += '\n🚀 Optimization Recommendations:\n';
      recommendations.forEach((rec, index) => {
        report += `  ${index + 1}. [${rec.priority.toUpperCase()}] ${rec.description}\n`;
        report += `     Estimated savings: ${this.formatBytes(rec.estimatedSavings)}\n`;
        report += `     Implementation: ${rec.implementation}\n\n`;
      });
    }

    return report;
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Tree shaking utilities
export class TreeShakingOptimizer {
  // Analyze unused exports
  static analyzeUnusedExports(): string[] {
    const unusedExports: string[] = [];
    
    // This would require build-time analysis
    // For now, return common patterns that are often unused
    const commonUnusedPatterns = [
      'lodash default import',
      'moment default import',
      'entire icon libraries',
      'unused utility functions',
      'development-only code in production',
    ];

    return commonUnusedPatterns;
  }

  // Get tree shaking recommendations
  static getTreeShakingRecommendations(): OptimizationRecommendation[] {
    return [
      {
        type: 'tree-shake',
        priority: 'high',
        description: 'Use named imports instead of default imports for better tree shaking',
        estimatedSavings: 50 * 1024,
        implementation: 'Change "import _ from \'lodash\'" to "import { debounce } from \'lodash\'"',
      },
      {
        type: 'tree-shake',
        priority: 'medium',
        description: 'Configure sideEffects: false in package.json for better tree shaking',
        estimatedSavings: 30 * 1024,
        implementation: 'Add "sideEffects": false to package.json',
      },
      {
        type: 'tree-shake',
        priority: 'medium',
        description: 'Use ES modules instead of CommonJS for better tree shaking',
        estimatedSavings: 40 * 1024,
        implementation: 'Ensure all imports use ES module syntax',
      },
    ];
  }
}

// Performance budget utilities
export class PerformanceBudget {
  private static budgets = {
    totalSize: 500 * 1024, // 500KB
    gzippedSize: 150 * 1024, // 150KB
    chunkSize: 250 * 1024, // 250KB per chunk
    vendorSize: 200 * 1024, // 200KB for vendor chunks
  };

  static checkBudget(analysis: BundleAnalysis): { passed: boolean; violations: string[] } {
    const violations: string[] = [];

    if (analysis.totalSize > this.budgets.totalSize) {
      violations.push(`Total size exceeds budget: ${analysis.totalSize} > ${this.budgets.totalSize}`);
    }

    if (analysis.gzippedSize > this.budgets.gzippedSize) {
      violations.push(`Gzipped size exceeds budget: ${analysis.gzippedSize} > ${this.budgets.gzippedSize}`);
    }

    analysis.chunks.forEach(chunk => {
      if (chunk.size > this.budgets.chunkSize) {
        violations.push(`Chunk ${chunk.name} exceeds size budget: ${chunk.size} > ${this.budgets.chunkSize}`);
      }
    });

    const vendorChunks = analysis.chunks.filter(chunk => chunk.name.includes('vendor'));
    vendorChunks.forEach(chunk => {
      if (chunk.size > this.budgets.vendorSize) {
        violations.push(`Vendor chunk ${chunk.name} exceeds budget: ${chunk.size} > ${this.budgets.vendorSize}`);
      }
    });

    return {
      passed: violations.length === 0,
      violations,
    };
  }

  static setBudgets(budgets: Partial<typeof PerformanceBudget.budgets>): void {
    Object.assign(this.budgets, budgets);
  }
}

export default {
  BundleAnalyzer,
  TreeShakingOptimizer,
  PerformanceBudget,
};

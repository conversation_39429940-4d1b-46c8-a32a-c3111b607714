/**
 * Security Utilities and Monitoring
 * Provides comprehensive security measures for the Dalti admin panel
 */

// Security configuration interface
export interface SecurityConfig {
  csp: {
    enabled: boolean;
    reportOnly: boolean;
    directives: Record<string, string[]>;
  };
  headers: {
    hsts: boolean;
    noSniff: boolean;
    frameOptions: string;
    xssProtection: boolean;
  };
  authentication: {
    tokenExpiry: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
    requireMFA: boolean;
  };
  monitoring: {
    enabled: boolean;
    reportEndpoint?: string;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
  };
}

// Default security configuration
export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  csp: {
    enabled: true,
    reportOnly: false,
    directives: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      'style-src': ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      'font-src': ["'self'", 'https://fonts.gstatic.com'],
      'img-src': ["'self'", 'data:', 'https:'],
      'connect-src': ["'self'", 'https://dapi-test.adscloud.org:8443', 'https://dapi.adscloud.org'],
      'frame-ancestors': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
      'upgrade-insecure-requests': [],
    },
  },
  headers: {
    hsts: true,
    noSniff: true,
    frameOptions: 'DENY',
    xssProtection: true,
  },
  authentication: {
    tokenExpiry: 3600000, // 1 hour
    maxLoginAttempts: 5,
    lockoutDuration: 900000, // 15 minutes
    requireMFA: false,
  },
  monitoring: {
    enabled: true,
    logLevel: 'warn',
  },
};

// Security event types
export enum SecurityEventType {
  LOGIN_ATTEMPT = 'LOGIN_ATTEMPT',
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  TOKEN_REFRESH = 'TOKEN_REFRESH',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  CSP_VIOLATION = 'CSP_VIOLATION',
  XSS_ATTEMPT = 'XSS_ATTEMPT',
  CSRF_ATTEMPT = 'CSRF_ATTEMPT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  PRIVILEGE_ESCALATION = 'PRIVILEGE_ESCALATION',
}

// Security event interface
export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  timestamp: number;
  userId?: string;
  userAgent: string;
  ipAddress?: string;
  details: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  blocked: boolean;
}

// Security monitor class
export class SecurityMonitor {
  private static instance: SecurityMonitor;
  private config: SecurityConfig;
  private events: SecurityEvent[] = [];
  private loginAttempts: Map<string, { count: number; lastAttempt: number }> = new Map();

  private constructor(config: SecurityConfig = DEFAULT_SECURITY_CONFIG) {
    this.config = config;
    this.initializeCSP();
    this.initializeSecurityHeaders();
    this.initializeEventListeners();
  }

  static getInstance(config?: SecurityConfig): SecurityMonitor {
    if (!SecurityMonitor.instance) {
      SecurityMonitor.instance = new SecurityMonitor(config);
    }
    return SecurityMonitor.instance;
  }

  private initializeCSP(): void {
    if (!this.config.csp.enabled || typeof document === 'undefined') return;

    const cspString = Object.entries(this.config.csp.directives)
      .map(([directive, sources]) => {
        if (sources.length === 0) return directive;
        return `${directive} ${sources.join(' ')}`;
      })
      .join('; ');

    const meta = document.createElement('meta');
    meta.httpEquiv = this.config.csp.reportOnly 
      ? 'Content-Security-Policy-Report-Only' 
      : 'Content-Security-Policy';
    meta.content = cspString;
    document.head.appendChild(meta);
  }

  private initializeSecurityHeaders(): void {
    if (typeof document === 'undefined') return;

    // Note: These headers should ideally be set by the server
    // This is a client-side fallback for development
    if (this.config.headers.frameOptions) {
      const meta = document.createElement('meta');
      meta.httpEquiv = 'X-Frame-Options';
      meta.content = this.config.headers.frameOptions;
      document.head.appendChild(meta);
    }
  }

  private initializeEventListeners(): void {
    if (typeof window === 'undefined') return;

    // CSP violation reporting
    document.addEventListener('securitypolicyviolation', (event) => {
      this.logSecurityEvent({
        type: SecurityEventType.CSP_VIOLATION,
        details: {
          violatedDirective: event.violatedDirective,
          blockedURI: event.blockedURI,
          documentURI: event.documentURI,
          originalPolicy: event.originalPolicy,
        },
        severity: 'medium',
        blocked: true,
      });
    });

    // Detect potential XSS attempts
    this.detectXSSAttempts();

    // Monitor for suspicious activity
    this.monitorSuspiciousActivity();
  }

  private detectXSSAttempts(): void {
    const originalInnerHTML = Element.prototype.innerHTML;
    Element.prototype.innerHTML = function(value: string) {
      if (typeof value === 'string' && this.isXSSAttempt(value)) {
        SecurityMonitor.instance.logSecurityEvent({
          type: SecurityEventType.XSS_ATTEMPT,
          details: {
            element: this.tagName,
            content: value.substring(0, 100),
          },
          severity: 'high',
          blocked: true,
        });
        return;
      }
      return originalInnerHTML.call(this, value);
    };
  }

  private isXSSAttempt(content: string): boolean {
    const xssPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe[^>]*>/gi,
      /<object[^>]*>/gi,
      /<embed[^>]*>/gi,
      /eval\s*\(/gi,
      /expression\s*\(/gi,
    ];

    return xssPatterns.some(pattern => pattern.test(content));
  }

  private monitorSuspiciousActivity(): void {
    let rapidClickCount = 0;
    let lastClickTime = 0;

    document.addEventListener('click', () => {
      const now = Date.now();
      if (now - lastClickTime < 100) {
        rapidClickCount++;
        if (rapidClickCount > 10) {
          this.logSecurityEvent({
            type: SecurityEventType.SUSPICIOUS_ACTIVITY,
            details: {
              activity: 'rapid_clicking',
              count: rapidClickCount,
            },
            severity: 'low',
            blocked: false,
          });
          rapidClickCount = 0;
        }
      } else {
        rapidClickCount = 0;
      }
      lastClickTime = now;
    });
  }

  // Public methods
  public logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp' | 'userAgent'>): void {
    const fullEvent: SecurityEvent = {
      id: this.generateEventId(),
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      ...event,
    };

    this.events.push(fullEvent);

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.warn('🔒 Security Event:', fullEvent);
    }

    // Report to monitoring service
    this.reportSecurityEvent(fullEvent);

    // Trigger alerts for high severity events
    if (fullEvent.severity === 'high' || fullEvent.severity === 'critical') {
      this.triggerSecurityAlert(fullEvent);
    }
  }

  public trackLoginAttempt(identifier: string, success: boolean): boolean {
    const now = Date.now();
    const attempts = this.loginAttempts.get(identifier) || { count: 0, lastAttempt: 0 };

    // Reset attempts if lockout period has passed
    if (now - attempts.lastAttempt > this.config.authentication.lockoutDuration) {
      attempts.count = 0;
    }

    if (success) {
      // Reset attempts on successful login
      this.loginAttempts.delete(identifier);
      this.logSecurityEvent({
        type: SecurityEventType.LOGIN_SUCCESS,
        details: { identifier },
        severity: 'low',
        blocked: false,
      });
      return true;
    } else {
      // Increment failed attempts
      attempts.count++;
      attempts.lastAttempt = now;
      this.loginAttempts.set(identifier, attempts);

      const isBlocked = attempts.count >= this.config.authentication.maxLoginAttempts;
      
      this.logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        details: { 
          identifier, 
          attemptCount: attempts.count,
          blocked: isBlocked,
        },
        severity: isBlocked ? 'high' : 'medium',
        blocked: isBlocked,
      });

      return !isBlocked;
    }
  }

  public isAccountLocked(identifier: string): boolean {
    const attempts = this.loginAttempts.get(identifier);
    if (!attempts) return false;

    const now = Date.now();
    const isLocked = attempts.count >= this.config.authentication.maxLoginAttempts &&
                    now - attempts.lastAttempt < this.config.authentication.lockoutDuration;

    return isLocked;
  }

  public validateToken(token: string): boolean {
    try {
      // Basic JWT structure validation
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      // Decode payload (without verification for client-side)
      const payload = JSON.parse(atob(parts[1]));
      const now = Math.floor(Date.now() / 1000);

      // Check expiration
      if (payload.exp && payload.exp < now) {
        this.logSecurityEvent({
          type: SecurityEventType.UNAUTHORIZED_ACCESS,
          details: { reason: 'expired_token' },
          severity: 'medium',
          blocked: true,
        });
        return false;
      }

      return true;
    } catch (error) {
      this.logSecurityEvent({
        type: SecurityEventType.UNAUTHORIZED_ACCESS,
        details: { reason: 'invalid_token', error: error.message },
        severity: 'high',
        blocked: true,
      });
      return false;
    }
  }

  public sanitizeInput(input: string): string {
    // Basic input sanitization
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }

  public validateCSRFToken(token: string): boolean {
    const storedToken = sessionStorage.getItem('csrf_token');
    const isValid = storedToken === token;

    if (!isValid) {
      this.logSecurityEvent({
        type: SecurityEventType.CSRF_ATTEMPT,
        details: { providedToken: token.substring(0, 10) },
        severity: 'high',
        blocked: true,
      });
    }

    return isValid;
  }

  public generateCSRFToken(): string {
    const token = Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    sessionStorage.setItem('csrf_token', token);
    return token;
  }

  private generateEventId(): string {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async reportSecurityEvent(event: SecurityEvent): Promise<void> {
    if (!this.config.monitoring.enabled || !this.config.monitoring.reportEndpoint) {
      return;
    }

    try {
      await fetch(this.config.monitoring.reportEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'security_event',
          event,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href,
        }),
      });
    } catch (error) {
      console.warn('Failed to report security event:', error);
    }
  }

  private triggerSecurityAlert(event: SecurityEvent): void {
    // Trigger immediate alerts for critical security events
    if (event.severity === 'critical') {
      console.error('🚨 CRITICAL SECURITY EVENT:', event);
      
      // Could integrate with alerting services here
      if (window.alert && process.env.NODE_ENV === 'development') {
        window.alert(`Critical security event detected: ${event.type}`);
      }
    }
  }

  // Getters
  public getSecurityEvents(): SecurityEvent[] {
    return [...this.events];
  }

  public getSecurityEventsByType(type: SecurityEventType): SecurityEvent[] {
    return this.events.filter(event => event.type === type);
  }

  public getSecuritySummary(): {
    totalEvents: number;
    criticalEvents: number;
    blockedEvents: number;
    recentEvents: SecurityEvent[];
  } {
    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);

    return {
      totalEvents: this.events.length,
      criticalEvents: this.events.filter(e => e.severity === 'critical').length,
      blockedEvents: this.events.filter(e => e.blocked).length,
      recentEvents: this.events.filter(e => e.timestamp > last24Hours),
    };
  }
}

// Security utilities
export class SecurityUtils {
  // Secure random string generation
  static generateSecureRandom(length: number = 32): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  // Secure password validation
  static validatePassword(password: string): {
    isValid: boolean;
    errors: string[];
    strength: 'weak' | 'medium' | 'strong';
  } {
    const errors: string[] = [];
    let score = 0;

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    } else {
      score += 1;
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    } else {
      score += 1;
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    } else {
      score += 1;
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    } else {
      score += 1;
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    } else {
      score += 1;
    }

    const strength = score < 3 ? 'weak' : score < 5 ? 'medium' : 'strong';

    return {
      isValid: errors.length === 0,
      errors,
      strength,
    };
  }

  // Secure data encryption (client-side)
  static async encryptData(data: string, key: string): Promise<string> {
    const encoder = new TextEncoder();
    const keyData = encoder.encode(key);
    const dataBuffer = encoder.encode(data);

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'AES-GCM' },
      false,
      ['encrypt']
    );

    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      cryptoKey,
      dataBuffer
    );

    const result = new Uint8Array(iv.length + encrypted.byteLength);
    result.set(iv);
    result.set(new Uint8Array(encrypted), iv.length);

    return btoa(String.fromCharCode(...result));
  }

  // Secure data decryption (client-side)
  static async decryptData(encryptedData: string, key: string): Promise<string> {
    const encoder = new TextEncoder();
    const decoder = new TextDecoder();
    const keyData = encoder.encode(key);

    const data = new Uint8Array(
      atob(encryptedData)
        .split('')
        .map(char => char.charCodeAt(0))
    );

    const iv = data.slice(0, 12);
    const encrypted = data.slice(12);

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'AES-GCM' },
      false,
      ['decrypt']
    );

    const decrypted = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv },
      cryptoKey,
      encrypted
    );

    return decoder.decode(decrypted);
  }

  // Rate limiting
  static createRateLimiter(maxRequests: number, windowMs: number) {
    const requests = new Map<string, number[]>();

    return (identifier: string): boolean => {
      const now = Date.now();
      const windowStart = now - windowMs;
      
      if (!requests.has(identifier)) {
        requests.set(identifier, []);
      }

      const userRequests = requests.get(identifier)!;
      
      // Remove old requests outside the window
      const validRequests = userRequests.filter(time => time > windowStart);
      
      if (validRequests.length >= maxRequests) {
        SecurityMonitor.getInstance().logSecurityEvent({
          type: SecurityEventType.RATE_LIMIT_EXCEEDED,
          details: { identifier, requestCount: validRequests.length },
          severity: 'medium',
          blocked: true,
        });
        return false;
      }

      validRequests.push(now);
      requests.set(identifier, validRequests);
      return true;
    };
  }
}

// Initialize security monitoring
export function initializeSecurity(config?: Partial<SecurityConfig>): SecurityMonitor {
  const fullConfig = { ...DEFAULT_SECURITY_CONFIG, ...config };
  return SecurityMonitor.getInstance(fullConfig);
}

export default SecurityMonitor;

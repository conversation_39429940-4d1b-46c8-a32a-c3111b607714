/**
 * Performance Optimization Utilities
 * Provides comprehensive performance monitoring, optimization, and analytics
 */

// Performance metrics interface
export interface PerformanceMetrics {
  id: string;
  timestamp: number;
  type: 'navigation' | 'resource' | 'measure' | 'paint' | 'layout-shift' | 'input-delay';
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta?: number;
  entries?: PerformanceEntry[];
  attribution?: Record<string, any>;
}

// Performance thresholds based on Core Web Vitals
export const PERFORMANCE_THRESHOLDS = {
  // Largest Contentful Paint (LCP)
  LCP: {
    good: 2500,
    poor: 4000,
  },
  // First Input Delay (FID)
  FID: {
    good: 100,
    poor: 300,
  },
  // Cumulative Layout Shift (CLS)
  CLS: {
    good: 0.1,
    poor: 0.25,
  },
  // First Contentful Paint (FCP)
  FCP: {
    good: 1800,
    poor: 3000,
  },
  // Time to Interactive (TTI)
  TTI: {
    good: 3800,
    poor: 7300,
  },
  // Total Blocking Time (TBT)
  TBT: {
    good: 200,
    poor: 600,
  },
} as const;

// Performance observer class
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private observers: PerformanceObserver[] = [];
  private metrics: PerformanceMetrics[] = [];
  private callbacks: ((metric: PerformanceMetrics) => void)[] = [];

  private constructor() {
    this.initializeObservers();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private initializeObservers(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    // Observe navigation timing
    this.observeNavigationTiming();

    // Observe resource timing
    this.observeResourceTiming();

    // Observe paint timing
    this.observePaintTiming();

    // Observe layout shift
    this.observeLayoutShift();

    // Observe largest contentful paint
    this.observeLargestContentfulPaint();

    // Observe first input delay
    this.observeFirstInputDelay();
  }

  private observeNavigationTiming(): void {
    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            
            // Time to First Byte (TTFB)
            const ttfb = navEntry.responseStart - navEntry.requestStart;
            this.recordMetric({
              type: 'navigation',
              name: 'TTFB',
              value: ttfb,
              rating: this.getRating(ttfb, { good: 800, poor: 1800 }),
            });

            // DOM Content Loaded
            const dcl = navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart;
            this.recordMetric({
              type: 'navigation',
              name: 'DCL',
              value: dcl,
              rating: this.getRating(dcl, { good: 1600, poor: 3000 }),
            });

            // Load Complete
            const loadComplete = navEntry.loadEventEnd - navEntry.loadEventStart;
            this.recordMetric({
              type: 'navigation',
              name: 'Load',
              value: loadComplete,
              rating: this.getRating(loadComplete, { good: 2500, poor: 4000 }),
            });
          }
        }
      });

      observer.observe({ entryTypes: ['navigation'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Navigation timing observer not supported:', error);
    }
  }

  private observeResourceTiming(): void {
    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming;
            
            // Track large resources
            if (resourceEntry.transferSize > 100000) { // > 100KB
              this.recordMetric({
                type: 'resource',
                name: 'Large Resource',
                value: resourceEntry.transferSize,
                rating: 'needs-improvement',
                attribution: {
                  url: resourceEntry.name,
                  type: this.getResourceType(resourceEntry.name),
                },
              });
            }

            // Track slow resources
            if (resourceEntry.duration > 1000) { // > 1s
              this.recordMetric({
                type: 'resource',
                name: 'Slow Resource',
                value: resourceEntry.duration,
                rating: 'poor',
                attribution: {
                  url: resourceEntry.name,
                  type: this.getResourceType(resourceEntry.name),
                },
              });
            }
          }
        }
      });

      observer.observe({ entryTypes: ['resource'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Resource timing observer not supported:', error);
    }
  }

  private observePaintTiming(): void {
    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            this.recordMetric({
              type: 'paint',
              name: 'FCP',
              value: entry.startTime,
              rating: this.getRating(entry.startTime, PERFORMANCE_THRESHOLDS.FCP),
            });
          }
        }
      });

      observer.observe({ entryTypes: ['paint'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Paint timing observer not supported:', error);
    }
  }

  private observeLayoutShift(): void {
    try {
      let clsValue = 0;
      let clsEntries: PerformanceEntry[] = [];

      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
            clsEntries.push(entry);
          }
        }

        this.recordMetric({
          type: 'layout-shift',
          name: 'CLS',
          value: clsValue,
          rating: this.getRating(clsValue, PERFORMANCE_THRESHOLDS.CLS),
          entries: clsEntries,
        });
      });

      observer.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Layout shift observer not supported:', error);
    }
  }

  private observeLargestContentfulPaint(): void {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        this.recordMetric({
          type: 'paint',
          name: 'LCP',
          value: lastEntry.startTime,
          rating: this.getRating(lastEntry.startTime, PERFORMANCE_THRESHOLDS.LCP),
          attribution: {
            element: (lastEntry as any).element?.tagName,
            url: (lastEntry as any).url,
          },
        });
      });

      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Largest contentful paint observer not supported:', error);
    }
  }

  private observeFirstInputDelay(): void {
    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric({
            type: 'input-delay',
            name: 'FID',
            value: (entry as any).processingStart - entry.startTime,
            rating: this.getRating(
              (entry as any).processingStart - entry.startTime,
              PERFORMANCE_THRESHOLDS.FID
            ),
            attribution: {
              eventType: (entry as any).name,
              target: (entry as any).target?.tagName,
            },
          });
        }
      });

      observer.observe({ entryTypes: ['first-input'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('First input delay observer not supported:', error);
    }
  }

  private recordMetric(metric: Omit<PerformanceMetrics, 'id' | 'timestamp'>): void {
    const fullMetric: PerformanceMetrics = {
      id: this.generateId(),
      timestamp: Date.now(),
      ...metric,
    };

    this.metrics.push(fullMetric);
    this.callbacks.forEach(callback => callback(fullMetric));

    // Log poor performance in development
    if (process.env.NODE_ENV === 'development' && fullMetric.rating === 'poor') {
      console.warn(`Poor performance detected: ${fullMetric.name} = ${fullMetric.value}ms`, fullMetric);
    }
  }

  private getRating(value: number, thresholds: { good: number; poor: number }): 'good' | 'needs-improvement' | 'poor' {
    if (value <= thresholds.good) return 'good';
    if (value <= thresholds.poor) return 'needs-improvement';
    return 'poor';
  }

  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'image';
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
    return 'other';
  }

  private generateId(): string {
    return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public methods
  public onMetric(callback: (metric: PerformanceMetrics) => void): void {
    this.callbacks.push(callback);
  }

  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  public getMetricsByType(type: PerformanceMetrics['type']): PerformanceMetrics[] {
    return this.metrics.filter(metric => metric.type === type);
  }

  public getLatestMetric(name: string): PerformanceMetrics | undefined {
    return this.metrics
      .filter(metric => metric.name === name)
      .sort((a, b) => b.timestamp - a.timestamp)[0];
  }

  public clearMetrics(): void {
    this.metrics = [];
  }

  public disconnect(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.callbacks = [];
  }
}

// Performance utilities
export class PerformanceUtils {
  // Measure function execution time
  static measureFunction<T extends (...args: any[]) => any>(
    fn: T,
    name?: string
  ): T {
    return ((...args: any[]) => {
      const startTime = performance.now();
      const result = fn(...args);
      const endTime = performance.now();
      
      const measureName = name || fn.name || 'Anonymous Function';
      console.log(`${measureName} took ${endTime - startTime} milliseconds`);
      
      return result;
    }) as T;
  }

  // Measure async function execution time
  static measureAsyncFunction<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    name?: string
  ): T {
    return (async (...args: any[]) => {
      const startTime = performance.now();
      const result = await fn(...args);
      const endTime = performance.now();
      
      const measureName = name || fn.name || 'Anonymous Async Function';
      console.log(`${measureName} took ${endTime - startTime} milliseconds`);
      
      return result;
    }) as T;
  }

  // Create performance mark
  static mark(name: string): void {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
    }
  }

  // Measure between two marks
  static measure(name: string, startMark: string, endMark?: string): number | null {
    if ('performance' in window && 'measure' in performance) {
      try {
        performance.measure(name, startMark, endMark);
        const entries = performance.getEntriesByName(name, 'measure');
        return entries.length > 0 ? entries[entries.length - 1].duration : null;
      } catch (error) {
        console.warn('Performance measure failed:', error);
        return null;
      }
    }
    return null;
  }

  // Get memory usage (if available)
  static getMemoryUsage(): any {
    if ('memory' in performance) {
      return (performance as any).memory;
    }
    return null;
  }

  // Check if performance API is available
  static isSupported(): boolean {
    return typeof window !== 'undefined' && 'performance' in window;
  }
}

// React hooks for performance monitoring
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = React.useState<PerformanceMetrics[]>([]);
  const monitor = React.useRef<PerformanceMonitor>();

  React.useEffect(() => {
    monitor.current = PerformanceMonitor.getInstance();
    
    const handleMetric = (metric: PerformanceMetrics) => {
      setMetrics(prev => [...prev, metric]);
    };

    monitor.current.onMetric(handleMetric);

    return () => {
      monitor.current?.disconnect();
    };
  }, []);

  const clearMetrics = React.useCallback(() => {
    monitor.current?.clearMetrics();
    setMetrics([]);
  }, []);

  const getMetricsByType = React.useCallback((type: PerformanceMetrics['type']) => {
    return metrics.filter(metric => metric.type === type);
  }, [metrics]);

  return {
    metrics,
    clearMetrics,
    getMetricsByType,
    monitor: monitor.current,
  };
}

// Performance measurement hook
export function usePerformanceMeasure(name: string) {
  const startTime = React.useRef<number>();

  const start = React.useCallback(() => {
    startTime.current = performance.now();
    PerformanceUtils.mark(`${name}-start`);
  }, [name]);

  const end = React.useCallback(() => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current;
      PerformanceUtils.mark(`${name}-end`);
      PerformanceUtils.measure(name, `${name}-start`, `${name}-end`);
      return duration;
    }
    return null;
  }, [name]);

  return { start, end };
}

// Initialize performance monitoring
export function initializePerformanceMonitoring(): void {
  if (typeof window === 'undefined') return;

  const monitor = PerformanceMonitor.getInstance();
  
  // Report metrics to analytics service
  monitor.onMetric((metric) => {
    // Send to analytics service
    if (process.env.REACT_APP_ANALYTICS_URL) {
      fetch(process.env.REACT_APP_ANALYTICS_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'performance',
          metric,
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: Date.now(),
        }),
      }).catch(error => {
        console.warn('Failed to send performance metric:', error);
      });
    }
  });
}

export default PerformanceMonitor;

import React from 'react';
import {
  DashboardIcon,
  ProvidersIcon,
  CustomersIcon,
  CategoriesIcon,
  BookingsIcon,
  AnalyticsIcon,
  SystemAdminIcon,
  ReportsIcon,
  SettingsIcon,
  MonitoringIcon,
  MaintenanceIcon,
  AuditIcon,
  SupportIcon,
} from '../components/icons/DaltiIcons';

export interface NavigationItem {
  id: string;
  name: string;
  icon: React.ReactNode;
  path?: string;
  description?: string;
  badge?: string | number;
  subItems?: NavigationSubItem[];
  permissions?: string[];
  pro?: boolean;
}

export interface NavigationSubItem {
  id: string;
  name: string;
  path: string;
  description?: string;
  badge?: string | number;
  permissions?: string[];
  pro?: boolean;
}

/**
 * Main Navigation Items
 * Core platform management features
 */
export const mainNavigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/',
    description: 'Platform overview and key metrics',
  },
  {
    id: 'providers',
    name: 'Providers',
    icon: <ProvidersIcon />,
    description: 'Service provider management and verification',
    subItems: [
      {
        id: 'providers-all',
        name: 'All Providers',
        path: '/providers',
        description: 'Complete provider directory',
      },
      {
        id: 'providers-pending',
        name: 'Pending Approval',
        path: '/providers/pending',
        description: 'Providers awaiting verification',
        badge: 'pending',
      },
      {
        id: 'providers-verified',
        name: 'Verified Providers',
        path: '/providers/verified',
        description: 'Approved and active providers',
      },
      {
        id: 'providers-analytics',
        name: 'Provider Analytics',
        path: '/providers/analytics',
        description: 'Provider performance and insights',
      },
    ],
  },
  {
    id: 'customers',
    name: 'Customers',
    icon: <CustomersIcon />,
    description: 'Customer management and support',
    subItems: [
      {
        id: 'customers-all',
        name: 'All Customers',
        path: '/customers',
        description: 'Complete customer directory',
      },
      {
        id: 'customers-active',
        name: 'Active Customers',
        path: '/customers/active',
        description: 'Currently active customers',
      },
      {
        id: 'customers-analytics',
        name: 'Customer Analytics',
        path: '/customers/analytics',
        description: 'Customer behavior and insights',
      },
      {
        id: 'customers-support',
        name: 'Support Tools',
        path: '/customers/support',
        description: 'Customer support and assistance',
        icon: <SupportIcon />,
      },
    ],
  },
  {
    id: 'categories',
    name: 'Categories',
    icon: <CategoriesIcon />,
    description: 'Service category management',
    subItems: [
      {
        id: 'categories-tree',
        name: 'Category Tree',
        path: '/categories',
        description: 'Hierarchical category structure',
      },
      {
        id: 'categories-analytics',
        name: 'Category Analytics',
        path: '/categories/analytics',
        description: 'Category performance metrics',
      },
    ],
  },
  {
    id: 'bookings',
    name: 'Bookings',
    icon: <BookingsIcon />,
    description: 'Appointment booking management',
    subItems: [
      {
        id: 'bookings-all',
        name: 'All Bookings',
        path: '/bookings',
        description: 'Complete booking history',
      },
      {
        id: 'bookings-recent',
        name: 'Recent Bookings',
        path: '/bookings/recent',
        description: 'Latest booking activity',
      },
      {
        id: 'bookings-analytics',
        name: 'Booking Analytics',
        path: '/bookings/analytics',
        description: 'Booking trends and insights',
      },
    ],
  },
];

/**
 * Secondary Navigation Items
 * Analytics, administration, and reporting features
 */
export const secondaryNavigationItems: NavigationItem[] = [
  {
    id: 'analytics',
    name: 'Analytics',
    icon: <AnalyticsIcon />,
    description: 'Platform analytics and insights',
    subItems: [
      {
        id: 'analytics-overview',
        name: 'Platform Overview',
        path: '/analytics',
        description: 'Comprehensive platform metrics',
      },
      {
        id: 'analytics-revenue',
        name: 'Revenue Reports',
        path: '/analytics/revenue',
        description: 'Financial performance analysis',
      },
      {
        id: 'analytics-growth',
        name: 'Growth Metrics',
        path: '/analytics/growth',
        description: 'Platform growth and expansion',
      },
      {
        id: 'analytics-performance',
        name: 'Performance Reports',
        path: '/analytics/performance',
        description: 'System and user performance',
      },
    ],
  },
  {
    id: 'system-admin',
    name: 'System Admin',
    icon: <SystemAdminIcon />,
    description: 'System administration and configuration',
    permissions: ['admin', 'super_admin'],
    subItems: [
      {
        id: 'admin-settings',
        name: 'Platform Settings',
        path: '/admin/settings',
        description: 'Global platform configuration',
        icon: <SettingsIcon />,
        permissions: ['admin', 'super_admin'],
      },
      {
        id: 'admin-users',
        name: 'Admin Users',
        path: '/admin/users',
        description: 'Administrative user management',
        permissions: ['super_admin'],
      },
      {
        id: 'admin-audit',
        name: 'Audit Logs',
        path: '/admin/audit',
        description: 'System activity and security logs',
        icon: <AuditIcon />,
        permissions: ['admin', 'super_admin'],
      },
      {
        id: 'admin-monitoring',
        name: 'System Monitoring',
        path: '/admin/monitoring',
        description: 'Real-time system health monitoring',
        icon: <MonitoringIcon />,
        permissions: ['admin', 'super_admin'],
      },
      {
        id: 'admin-maintenance',
        name: 'Maintenance',
        path: '/admin/maintenance',
        description: 'System maintenance and backup tools',
        icon: <MaintenanceIcon />,
        permissions: ['admin', 'super_admin'],
      },
    ],
  },
  {
    id: 'reports',
    name: 'Reports',
    icon: <ReportsIcon />,
    description: 'Comprehensive reporting and exports',
    subItems: [
      {
        id: 'reports-providers',
        name: 'Provider Reports',
        path: '/reports/providers',
        description: 'Provider performance and activity reports',
      },
      {
        id: 'reports-customers',
        name: 'Customer Reports',
        path: '/reports/customers',
        description: 'Customer engagement and behavior reports',
      },
      {
        id: 'reports-financial',
        name: 'Financial Reports',
        path: '/reports/financial',
        description: 'Revenue and financial analysis reports',
      },
      {
        id: 'reports-system',
        name: 'System Reports',
        path: '/reports/system',
        description: 'System performance and usage reports',
      },
    ],
  },
];

/**
 * Quick Actions
 * Frequently used actions for quick access
 */
export const quickActions = [
  {
    id: 'add-provider',
    name: 'Add Provider',
    path: '/providers/new',
    icon: <ProvidersIcon />,
    description: 'Register a new service provider',
  },
  {
    id: 'add-customer',
    name: 'Add Customer',
    path: '/customers/new',
    icon: <CustomersIcon />,
    description: 'Create a new customer account',
  },
  {
    id: 'add-category',
    name: 'Add Category',
    path: '/categories/new',
    icon: <CategoriesIcon />,
    description: 'Create a new service category',
  },
  {
    id: 'system-health',
    name: 'System Health',
    path: '/admin/monitoring',
    icon: <MonitoringIcon />,
    description: 'Check system status',
  },
];

/**
 * Navigation Configuration
 */
export const navigationConfig = {
  main: mainNavigationItems,
  secondary: secondaryNavigationItems,
  quickActions,
  
  // Navigation settings
  settings: {
    collapsible: true,
    defaultExpanded: true,
    showBadges: true,
    showDescriptions: true,
    groupSeparators: true,
  },
  
  // Permission levels
  permissions: {
    public: ['dashboard', 'providers', 'customers', 'categories', 'bookings'],
    admin: ['analytics', 'reports', 'system-admin'],
    super_admin: ['admin-users'],
  },
};

/**
 * Helper function to filter navigation items based on user permissions
 */
export const filterNavigationByPermissions = (
  items: NavigationItem[],
  userPermissions: string[]
): NavigationItem[] => {
  return items
    .filter(item => {
      if (!item.permissions) return true;
      return item.permissions.some(permission => userPermissions.includes(permission));
    })
    .map(item => ({
      ...item,
      subItems: item.subItems?.filter(subItem => {
        if (!subItem.permissions) return true;
        return subItem.permissions.some(permission => userPermissions.includes(permission));
      }),
    }));
};

/**
 * Helper function to get navigation item by ID
 */
export const getNavigationItemById = (id: string): NavigationItem | NavigationSubItem | null => {
  // Search in main navigation
  for (const item of mainNavigationItems) {
    if (item.id === id) return item;
    if (item.subItems) {
      const subItem = item.subItems.find(sub => sub.id === id);
      if (subItem) return subItem;
    }
  }
  
  // Search in secondary navigation
  for (const item of secondaryNavigationItems) {
    if (item.id === id) return item;
    if (item.subItems) {
      const subItem = item.subItems.find(sub => sub.id === id);
      if (subItem) return subItem;
    }
  }
  
  return null;
};

/**
 * Helper function to get breadcrumb trail for a path
 */
export const getBreadcrumbTrail = (path: string): Array<{ name: string; path: string }> => {
  const breadcrumbs: Array<{ name: string; path: string }> = [];
  
  // Search through all navigation items
  const allItems = [...mainNavigationItems, ...secondaryNavigationItems];
  
  for (const item of allItems) {
    if (item.path === path) {
      breadcrumbs.push({ name: item.name, path: item.path });
      break;
    }
    
    if (item.subItems) {
      const subItem = item.subItems.find(sub => sub.path === path);
      if (subItem) {
        breadcrumbs.push({ name: item.name, path: item.path || '#' });
        breadcrumbs.push({ name: subItem.name, path: subItem.path });
        break;
      }
    }
  }
  
  return breadcrumbs;
};

export default navigationConfig;

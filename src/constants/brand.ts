/**
 * <PERSON><PERSON> Brand Constants
 * Central configuration for all brand-related constants
 */

export const BRAND_CONFIG = {
  // Company Information
  company: {
    name: '<PERSON><PERSON>',
    fullName: 'Dalti Appointment Booking Platform',
    tagline: 'Professional Appointment Booking Made Simple',
    description: 'Comprehensive appointment booking platform connecting service providers with customers',
    website: 'https://dalti.com',
    supportEmail: '<EMAIL>',
    adminEmail: '<EMAIL>',
  },

  // Admin Panel Specific
  admin: {
    title: 'Dalti Admin Panel',
    subtitle: 'Platform Management Dashboard',
    description: 'Comprehensive admin panel for managing providers, customers, and platform operations',
    version: '1.0.0',
  },

  // API Configuration
  api: {
    development: 'https://dapi-test.adscloud.org:8443',
    production: 'https://dapi.adscloud.org',
  },

  // Social Media & Links
  social: {
    twitter: 'https://twitter.com/dalti',
    linkedin: 'https://linkedin.com/company/dalti',
    facebook: 'https://facebook.com/dalti',
    instagram: 'https://instagram.com/dalti',
  },

  // Legal
  legal: {
    privacyPolicy: 'https://dalti.com/privacy',
    termsOfService: 'https://dalti.com/terms',
    cookiePolicy: 'https://dalti.com/cookies',
    copyright: `© ${new Date().getFullYear()} Dalti. All rights reserved.`,
  },

  // Features
  features: {
    providers: {
      name: 'Provider Management',
      description: 'Comprehensive provider onboarding, verification, and management',
      icon: '👥',
    },
    customers: {
      name: 'Customer Management',
      description: 'Customer profiles, analytics, and support tools',
      icon: '👤',
    },
    categories: {
      name: 'Category Management',
      description: 'Hierarchical service category organization',
      icon: '📂',
    },
    analytics: {
      name: 'Analytics & Insights',
      description: 'Comprehensive platform analytics and reporting',
      icon: '📊',
    },
    monitoring: {
      name: 'System Monitoring',
      description: 'Real-time system health and performance monitoring',
      icon: '🔍',
    },
    maintenance: {
      name: 'Maintenance Tools',
      description: 'System maintenance, backups, and health checks',
      icon: '🔧',
    },
  },

  // Status Types
  statusTypes: {
    provider: {
      verified: { label: 'Verified', color: 'green', icon: '✅' },
      pending: { label: 'Pending Approval', color: 'yellow', icon: '⏳' },
      rejected: { label: 'Rejected', color: 'red', icon: '❌' },
      suspended: { label: 'Suspended', color: 'red', icon: '🚫' },
    },
    customer: {
      active: { label: 'Active', color: 'green', icon: '✅' },
      inactive: { label: 'Inactive', color: 'gray', icon: '⏸️' },
      blocked: { label: 'Blocked', color: 'red', icon: '🚫' },
    },
    system: {
      online: { label: 'Online', color: 'green', icon: '🟢' },
      offline: { label: 'Offline', color: 'gray', icon: '⚫' },
      maintenance: { label: 'Maintenance', color: 'yellow', icon: '🔧' },
      error: { label: 'Error', color: 'red', icon: '🔴' },
    },
    backup: {
      completed: { label: 'Completed', color: 'green', icon: '✅' },
      in_progress: { label: 'In Progress', color: 'blue', icon: '🔄' },
      failed: { label: 'Failed', color: 'red', icon: '❌' },
      scheduled: { label: 'Scheduled', color: 'yellow', icon: '⏰' },
    },
  },

  // Priority Levels
  priorities: {
    critical: { label: 'Critical', color: 'red', icon: '🚨' },
    high: { label: 'High', color: 'orange', icon: '⚠️' },
    medium: { label: 'Medium', color: 'yellow', icon: '📋' },
    low: { label: 'Low', color: 'green', icon: '📝' },
  },

  // Navigation Structure
  navigation: {
    main: [
      {
        name: 'Dashboard',
        path: '/',
        icon: '📊',
        description: 'Platform overview and key metrics',
      },
      {
        name: 'Providers',
        icon: '👥',
        description: 'Provider management and verification',
        subItems: [
          { name: 'All Providers', path: '/providers' },
          { name: 'Pending Approval', path: '/providers?status=pending' },
          { name: 'Verified', path: '/providers?verified=true' },
          { name: 'Analytics', path: '/providers/analytics' },
        ],
      },
      {
        name: 'Customers',
        icon: '👤',
        description: 'Customer management and support',
        subItems: [
          { name: 'All Customers', path: '/customers' },
          { name: 'Active', path: '/customers?status=active' },
          { name: 'Analytics', path: '/customers/analytics' },
          { name: 'Support Tools', path: '/customers/support' },
        ],
      },
      {
        name: 'Categories',
        icon: '📂',
        description: 'Service category management',
        subItems: [
          { name: 'Category Tree', path: '/categories' },
          { name: 'Analytics', path: '/categories/analytics' },
        ],
      },
    ],
    admin: [
      {
        name: 'System',
        icon: '⚙️',
        description: 'System administration and settings',
        subItems: [
          { name: 'Settings', path: '/admin/settings' },
          { name: 'Admin Users', path: '/admin/users' },
          { name: 'Audit Logs', path: '/admin/audit' },
          { name: 'Monitoring', path: '/admin/monitoring' },
          { name: 'Maintenance', path: '/admin/maintenance' },
        ],
      },
    ],
  },

  // Notification Types
  notifications: {
    provider_registration: {
      title: 'New Provider Registration',
      icon: '👥',
      color: 'blue',
    },
    provider_verification: {
      title: 'Provider Verification Required',
      icon: '✅',
      color: 'yellow',
    },
    system_alert: {
      title: 'System Alert',
      icon: '🚨',
      color: 'red',
    },
    backup_completed: {
      title: 'Backup Completed',
      icon: '💾',
      color: 'green',
    },
    maintenance_scheduled: {
      title: 'Maintenance Scheduled',
      icon: '🔧',
      color: 'yellow',
    },
  },

  // File Upload Limits
  uploads: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
    allowedDocumentTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  },

  // Pagination Defaults
  pagination: {
    defaultPageSize: 20,
    pageSizeOptions: [10, 20, 50, 100],
    maxPageSize: 100,
  },

  // Date Formats
  dateFormats: {
    display: 'MMM dd, yyyy',
    displayWithTime: 'MMM dd, yyyy HH:mm',
    api: 'yyyy-MM-dd',
    apiWithTime: 'yyyy-MM-dd HH:mm:ss',
  },

  // Currency
  currency: {
    default: 'USD',
    symbol: '$',
    locale: 'en-US',
  },

  // Time Zones
  timezone: {
    default: 'UTC',
    display: 'America/New_York', // Adjust based on primary market
  },

  // Performance Thresholds
  performance: {
    apiResponseTime: {
      good: 200, // ms
      warning: 500, // ms
      critical: 1000, // ms
    },
    systemLoad: {
      good: 50, // %
      warning: 75, // %
      critical: 90, // %
    },
    diskUsage: {
      good: 70, // %
      warning: 85, // %
      critical: 95, // %
    },
  },

  // Cache Settings
  cache: {
    defaultTTL: 300, // 5 minutes
    longTTL: 3600, // 1 hour
    shortTTL: 60, // 1 minute
  },
} as const;

// Export individual sections for convenience
export const COMPANY_INFO = BRAND_CONFIG.company;
export const ADMIN_CONFIG = BRAND_CONFIG.admin;
export const API_CONFIG = BRAND_CONFIG.api;
export const STATUS_TYPES = BRAND_CONFIG.statusTypes;
export const PRIORITIES = BRAND_CONFIG.priorities;
export const NAVIGATION = BRAND_CONFIG.navigation;
export const NOTIFICATIONS = BRAND_CONFIG.notifications;

export default BRAND_CONFIG;

/**
 * Security React Hooks
 * Provides React hooks for security monitoring and protection
 */

import React from 'react';
import SecurityMonitor, { SecurityEventType, SecurityEvent } from '../utils/security';

// Security context
interface SecurityContextType {
  monitor: SecurityMonitor;
  events: SecurityEvent[];
  isSecure: boolean;
  securityLevel: 'low' | 'medium' | 'high';
  reportSecurityEvent: (event: Omit<SecurityEvent, 'id' | 'timestamp' | 'userAgent'>) => void;
}

const SecurityContext = React.createContext<SecurityContextType | null>(null);

// Security provider component
interface SecurityProviderProps {
  children: React.ReactNode;
  config?: any;
}

export function SecurityProvider({ children, config }: SecurityProviderProps) {
  const [monitor] = React.useState(() => SecurityMonitor.getInstance(config));
  const [events, setEvents] = React.useState<SecurityEvent[]>([]);
  const [isSecure, setIsSecure] = React.useState(true);
  const [securityLevel, setSecurityLevel] = React.useState<'low' | 'medium' | 'high'>('low');

  React.useEffect(() => {
    // Update events periodically
    const interval = setInterval(() => {
      const currentEvents = monitor.getSecurityEvents();
      setEvents(currentEvents);

      // Calculate security level based on recent events
      const recentEvents = currentEvents.filter(
        event => Date.now() - event.timestamp < 60000 // Last minute
      );

      const criticalEvents = recentEvents.filter(event => event.severity === 'critical');
      const highEvents = recentEvents.filter(event => event.severity === 'high');

      if (criticalEvents.length > 0) {
        setSecurityLevel('high');
        setIsSecure(false);
      } else if (highEvents.length > 2) {
        setSecurityLevel('medium');
        setIsSecure(true);
      } else {
        setSecurityLevel('low');
        setIsSecure(true);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [monitor]);

  const reportSecurityEvent = React.useCallback((event: Omit<SecurityEvent, 'id' | 'timestamp' | 'userAgent'>) => {
    monitor.logSecurityEvent(event);
  }, [monitor]);

  const value: SecurityContextType = {
    monitor,
    events,
    isSecure,
    securityLevel,
    reportSecurityEvent,
  };

  return (
    <SecurityContext.Provider value={value}>
      {children}
    </SecurityContext.Provider>
  );
}

// Main security hook
export function useSecurity() {
  const context = React.useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
}

// Hook for login attempt tracking
export function useLoginSecurity() {
  const { monitor, reportSecurityEvent } = useSecurity();

  const trackLoginAttempt = React.useCallback((identifier: string, success: boolean) => {
    return monitor.trackLoginAttempt(identifier, success);
  }, [monitor]);

  const isAccountLocked = React.useCallback((identifier: string) => {
    return monitor.isAccountLocked(identifier);
  }, [monitor]);

  const reportSuspiciousLogin = React.useCallback((details: Record<string, any>) => {
    reportSecurityEvent({
      type: SecurityEventType.SUSPICIOUS_ACTIVITY,
      details,
      severity: 'medium',
      blocked: false,
    });
  }, [reportSecurityEvent]);

  return {
    trackLoginAttempt,
    isAccountLocked,
    reportSuspiciousLogin,
  };
}

// Hook for input sanitization
export function useInputSecurity() {
  const { monitor, reportSecurityEvent } = useSecurity();

  const sanitizeInput = React.useCallback((input: string) => {
    return monitor.sanitizeInput(input);
  }, [monitor]);

  const validateInput = React.useCallback((input: string, type: 'email' | 'text' | 'password' = 'text') => {
    const sanitized = sanitizeInput(input);
    
    // Check for potential XSS
    const xssPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
    ];

    const hasXSS = xssPatterns.some(pattern => pattern.test(input));
    
    if (hasXSS) {
      reportSecurityEvent({
        type: SecurityEventType.XSS_ATTEMPT,
        details: { input: input.substring(0, 100), type },
        severity: 'high',
        blocked: true,
      });
      return { isValid: false, sanitized: '', error: 'Invalid input detected' };
    }

    // Type-specific validation
    switch (type) {
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(sanitized)) {
          return { isValid: false, sanitized, error: 'Invalid email format' };
        }
        break;
      case 'password':
        if (sanitized.length < 8) {
          return { isValid: false, sanitized, error: 'Password too short' };
        }
        break;
    }

    return { isValid: true, sanitized, error: null };
  }, [sanitizeInput, reportSecurityEvent]);

  return {
    sanitizeInput,
    validateInput,
  };
}

// Hook for CSRF protection
export function useCSRFProtection() {
  const { monitor } = useSecurity();
  const [csrfToken, setCSRFToken] = React.useState<string>('');

  React.useEffect(() => {
    const token = monitor.generateCSRFToken();
    setCSRFToken(token);
  }, [monitor]);

  const validateCSRFToken = React.useCallback((token: string) => {
    return monitor.validateCSRFToken(token);
  }, [monitor]);

  const getCSRFHeaders = React.useCallback(() => {
    return {
      'X-CSRF-Token': csrfToken,
    };
  }, [csrfToken]);

  return {
    csrfToken,
    validateCSRFToken,
    getCSRFHeaders,
  };
}

// Hook for rate limiting
export function useRateLimit(maxRequests: number = 10, windowMs: number = 60000) {
  const { reportSecurityEvent } = useSecurity();
  const rateLimiter = React.useRef(
    SecurityMonitor.getInstance().constructor.createRateLimiter?.(maxRequests, windowMs)
  );

  const checkRateLimit = React.useCallback((identifier: string = 'default') => {
    if (!rateLimiter.current) return true;
    
    const allowed = rateLimiter.current(identifier);
    
    if (!allowed) {
      reportSecurityEvent({
        type: SecurityEventType.RATE_LIMIT_EXCEEDED,
        details: { identifier, maxRequests, windowMs },
        severity: 'medium',
        blocked: true,
      });
    }

    return allowed;
  }, [maxRequests, windowMs, reportSecurityEvent]);

  return { checkRateLimit };
}

// Hook for secure storage
export function useSecureStorage() {
  const { monitor, reportSecurityEvent } = useSecurity();

  const setSecureItem = React.useCallback(async (key: string, value: string, encrypt: boolean = false) => {
    try {
      let finalValue = value;
      
      if (encrypt) {
        const encryptionKey = await monitor.generateSecureRandom(32);
        finalValue = await SecurityMonitor.encryptData(value, encryptionKey);
        sessionStorage.setItem(`${key}_key`, encryptionKey);
      }

      localStorage.setItem(key, finalValue);
      
      reportSecurityEvent({
        type: SecurityEventType.SUSPICIOUS_ACTIVITY,
        details: { action: 'secure_storage_write', key, encrypted: encrypt },
        severity: 'low',
        blocked: false,
      });
    } catch (error) {
      reportSecurityEvent({
        type: SecurityEventType.SUSPICIOUS_ACTIVITY,
        details: { action: 'secure_storage_error', key, error: error.message },
        severity: 'medium',
        blocked: false,
      });
      throw error;
    }
  }, [monitor, reportSecurityEvent]);

  const getSecureItem = React.useCallback(async (key: string, encrypted: boolean = false) => {
    try {
      const value = localStorage.getItem(key);
      if (!value) return null;

      if (encrypted) {
        const encryptionKey = sessionStorage.getItem(`${key}_key`);
        if (!encryptionKey) {
          throw new Error('Encryption key not found');
        }
        return await SecurityMonitor.decryptData(value, encryptionKey);
      }

      return value;
    } catch (error) {
      reportSecurityEvent({
        type: SecurityEventType.SUSPICIOUS_ACTIVITY,
        details: { action: 'secure_storage_read_error', key, error: error.message },
        severity: 'medium',
        blocked: false,
      });
      return null;
    }
  }, [reportSecurityEvent]);

  const removeSecureItem = React.useCallback((key: string) => {
    localStorage.removeItem(key);
    sessionStorage.removeItem(`${key}_key`);
  }, []);

  return {
    setSecureItem,
    getSecureItem,
    removeSecureItem,
  };
}

// Hook for monitoring user behavior
export function useUserBehaviorMonitoring() {
  const { reportSecurityEvent } = useSecurity();

  React.useEffect(() => {
    let mouseMovements = 0;
    let keystrokes = 0;
    let rapidClicks = 0;
    let lastActivity = Date.now();

    const handleMouseMove = () => {
      mouseMovements++;
      lastActivity = Date.now();
    };

    const handleKeyDown = () => {
      keystrokes++;
      lastActivity = Date.now();
    };

    const handleClick = () => {
      const now = Date.now();
      if (now - lastActivity < 100) {
        rapidClicks++;
        if (rapidClicks > 10) {
          reportSecurityEvent({
            type: SecurityEventType.SUSPICIOUS_ACTIVITY,
            details: { activity: 'rapid_clicking', count: rapidClicks },
            severity: 'low',
            blocked: false,
          });
          rapidClicks = 0;
        }
      } else {
        rapidClicks = 0;
      }
      lastActivity = now;
    };

    // Monitor for bot-like behavior
    const monitorInterval = setInterval(() => {
      const now = Date.now();
      const timeSinceLastActivity = now - lastActivity;

      // Check for unusual patterns
      if (mouseMovements === 0 && keystrokes > 100) {
        reportSecurityEvent({
          type: SecurityEventType.SUSPICIOUS_ACTIVITY,
          details: { activity: 'keyboard_only_interaction', keystrokes },
          severity: 'medium',
          blocked: false,
        });
      }

      if (timeSinceLastActivity > 300000) { // 5 minutes of inactivity
        reportSecurityEvent({
          type: SecurityEventType.SUSPICIOUS_ACTIVITY,
          details: { activity: 'prolonged_inactivity', duration: timeSinceLastActivity },
          severity: 'low',
          blocked: false,
        });
      }

      // Reset counters
      mouseMovements = 0;
      keystrokes = 0;
    }, 60000); // Check every minute

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('click', handleClick);
      clearInterval(monitorInterval);
    };
  }, [reportSecurityEvent]);
}

// Hook for session security
export function useSessionSecurity() {
  const { monitor, reportSecurityEvent } = useSecurity();

  const validateSession = React.useCallback(() => {
    const token = localStorage.getItem('dalti_admin_token');
    if (!token) return false;

    const isValid = monitor.validateToken(token);
    
    if (!isValid) {
      reportSecurityEvent({
        type: SecurityEventType.UNAUTHORIZED_ACCESS,
        details: { reason: 'invalid_session' },
        severity: 'high',
        blocked: true,
      });
    }

    return isValid;
  }, [monitor, reportSecurityEvent]);

  const refreshSession = React.useCallback(async () => {
    try {
      // This would typically call your refresh token endpoint
      reportSecurityEvent({
        type: SecurityEventType.TOKEN_REFRESH,
        details: { timestamp: Date.now() },
        severity: 'low',
        blocked: false,
      });
      
      return true;
    } catch (error) {
      reportSecurityEvent({
        type: SecurityEventType.UNAUTHORIZED_ACCESS,
        details: { reason: 'refresh_failed', error: error.message },
        severity: 'high',
        blocked: true,
      });
      return false;
    }
  }, [reportSecurityEvent]);

  const terminateSession = React.useCallback(() => {
    localStorage.removeItem('dalti_admin_token');
    localStorage.removeItem('dalti_admin_user');
    sessionStorage.clear();
    
    reportSecurityEvent({
      type: SecurityEventType.LOGOUT,
      details: { timestamp: Date.now() },
      severity: 'low',
      blocked: false,
    });
  }, [reportSecurityEvent]);

  return {
    validateSession,
    refreshSession,
    terminateSession,
  };
}

export default {
  SecurityProvider,
  useSecurity,
  useLoginSecurity,
  useInputSecurity,
  useCSRFProtection,
  useRateLimit,
  useSecureStorage,
  useUserBehaviorMonitoring,
  useSessionSecurity,
};

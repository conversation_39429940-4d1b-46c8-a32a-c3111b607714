// Loading State Management Hook for Dalti Admin Panel

import { useState, useCallback, useRef, useEffect } from 'react';

// Loading state interface
export interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
  error?: string | null;
}

// Loading options
export interface LoadingOptions {
  message?: string;
  showProgress?: boolean;
  timeout?: number;
  onTimeout?: () => void;
}

// Multiple loading states manager
export interface LoadingStates {
  [key: string]: LoadingState;
}

/**
 * Hook for managing single loading state
 */
export function useLoading(initialMessage?: string) {
  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    message: initialMessage,
    progress: 0,
    error: null,
  });

  const timeoutRef = useRef<NodeJS.Timeout>();

  // Start loading
  const startLoading = useCallback((options?: LoadingOptions) => {
    setState(prev => ({
      ...prev,
      isLoading: true,
      message: options?.message || prev.message,
      progress: options?.showProgress ? 0 : undefined,
      error: null,
    }));

    // Set timeout if specified
    if (options?.timeout) {
      timeoutRef.current = setTimeout(() => {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Operation timed out',
        }));
        options.onTimeout?.();
      }, options.timeout);
    }
  }, []);

  // Stop loading
  const stopLoading = useCallback((error?: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setState(prev => ({
      ...prev,
      isLoading: false,
      error: error || null,
      progress: error ? prev.progress : 100,
    }));
  }, []);

  // Update progress
  const updateProgress = useCallback((progress: number, message?: string) => {
    setState(prev => ({
      ...prev,
      progress: Math.max(0, Math.min(100, progress)),
      message: message || prev.message,
    }));
  }, []);

  // Update message
  const updateMessage = useCallback((message: string) => {
    setState(prev => ({
      ...prev,
      message,
    }));
  }, []);

  // Reset state
  const reset = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    setState({
      isLoading: false,
      message: initialMessage,
      progress: 0,
      error: null,
    });
  }, [initialMessage]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    ...state,
    startLoading,
    stopLoading,
    updateProgress,
    updateMessage,
    reset,
  };
}

/**
 * Hook for managing multiple loading states
 */
export function useMultipleLoading() {
  const [states, setStates] = useState<LoadingStates>({});

  // Start loading for a specific key
  const startLoading = useCallback((key: string, options?: LoadingOptions) => {
    setStates(prev => ({
      ...prev,
      [key]: {
        isLoading: true,
        message: options?.message,
        progress: options?.showProgress ? 0 : undefined,
        error: null,
      },
    }));
  }, []);

  // Stop loading for a specific key
  const stopLoading = useCallback((key: string, error?: string) => {
    setStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        isLoading: false,
        error: error || null,
        progress: error ? prev[key]?.progress : 100,
      },
    }));
  }, []);

  // Update progress for a specific key
  const updateProgress = useCallback((key: string, progress: number, message?: string) => {
    setStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        progress: Math.max(0, Math.min(100, progress)),
        message: message || prev[key]?.message,
      },
    }));
  }, []);

  // Get loading state for a specific key
  const getLoadingState = useCallback((key: string): LoadingState => {
    return states[key] || {
      isLoading: false,
      message: undefined,
      progress: 0,
      error: null,
    };
  }, [states]);

  // Check if any operation is loading
  const isAnyLoading = useCallback(() => {
    return Object.values(states).some(state => state.isLoading);
  }, [states]);

  // Get all loading keys
  const getLoadingKeys = useCallback(() => {
    return Object.keys(states).filter(key => states[key].isLoading);
  }, [states]);

  // Clear specific loading state
  const clearLoading = useCallback((key: string) => {
    setStates(prev => {
      const newStates = { ...prev };
      delete newStates[key];
      return newStates;
    });
  }, []);

  // Clear all loading states
  const clearAllLoading = useCallback(() => {
    setStates({});
  }, []);

  return {
    states,
    startLoading,
    stopLoading,
    updateProgress,
    getLoadingState,
    isAnyLoading,
    getLoadingKeys,
    clearLoading,
    clearAllLoading,
  };
}

/**
 * Hook for async operations with automatic loading management
 */
export function useAsyncOperation<T = any>() {
  const loading = useLoading();

  const execute = useCallback(async (
    operation: () => Promise<T>,
    options?: LoadingOptions
  ): Promise<T> => {
    try {
      loading.startLoading(options);
      const result = await operation();
      loading.stopLoading();
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Operation failed';
      loading.stopLoading(errorMessage);
      throw error;
    }
  }, [loading]);

  return {
    ...loading,
    execute,
  };
}

/**
 * Hook for managing loading states with React Query integration
 */
export function useQueryLoading() {
  const [customLoading, setCustomLoading] = useState<Record<string, LoadingState>>({});

  // Add custom loading state
  const addLoading = useCallback((key: string, message?: string) => {
    setCustomLoading(prev => ({
      ...prev,
      [key]: {
        isLoading: true,
        message,
        progress: 0,
        error: null,
      },
    }));
  }, []);

  // Remove custom loading state
  const removeLoading = useCallback((key: string) => {
    setCustomLoading(prev => {
      const newLoading = { ...prev };
      delete newLoading[key];
      return newLoading;
    });
  }, []);

  // Check if any custom loading is active
  const hasCustomLoading = useCallback(() => {
    return Object.values(customLoading).some(state => state.isLoading);
  }, [customLoading]);

  return {
    customLoading,
    addLoading,
    removeLoading,
    hasCustomLoading,
  };
}

/**
 * Hook for debounced loading states
 */
export function useDebouncedLoading(delay: number = 300) {
  const [isLoading, setIsLoading] = useState(false);
  const [debouncedLoading, setDebouncedLoading] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const setLoading = useCallback((loading: boolean) => {
    setIsLoading(loading);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (loading) {
      // Show loading immediately when starting
      setDebouncedLoading(true);
    } else {
      // Delay hiding loading to prevent flicker
      timeoutRef.current = setTimeout(() => {
        setDebouncedLoading(false);
      }, delay);
    }
  }, [delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    isLoading,
    debouncedLoading,
    setLoading,
  };
}

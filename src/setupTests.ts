/**
 * Jest Setup for Dalti Admin Panel Tests
 * Configures testing environment and global mocks
 */

import '@testing-library/jest-dom';
import 'jest-axe/extend-expect';
import { configure } from '@testing-library/react';
import { server } from './__mocks__/server';
import { testHelpers } from './utils/testUtils';

// Configure React Testing Library
configure({
  testIdAttribute: 'data-testid',
  asyncUtilTimeout: 5000,
  computedStyleSupportsPseudoElements: true,
});

// Global test setup
beforeAll(() => {
  // Start MSW server
  server.listen({
    onUnhandledRequest: 'warn',
  });

  // Mock IntersectionObserver
  testHelpers.mockIntersectionObserver();

  // Mock ResizeObserver
  testHelpers.mockResizeObserver();

  // Mock matchMedia
  testHelpers.mockMatchMedia();

  // Mock scrollTo
  Object.defineProperty(window, 'scrollTo', {
    value: jest.fn(),
    writable: true,
  });

  // Mock scrollIntoView
  Element.prototype.scrollIntoView = jest.fn();

  // Mock getComputedStyle
  Object.defineProperty(window, 'getComputedStyle', {
    value: () => ({
      getPropertyValue: () => '',
      backgroundColor: 'rgb(255, 255, 255)',
      color: 'rgb(0, 0, 0)',
    }),
  });

  // Mock localStorage
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });

  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
  });

  // Mock URL.createObjectURL
  Object.defineProperty(URL, 'createObjectURL', {
    value: jest.fn(() => 'mocked-url'),
  });

  // Mock URL.revokeObjectURL
  Object.defineProperty(URL, 'revokeObjectURL', {
    value: jest.fn(),
  });

  // Mock fetch if not already mocked
  if (!global.fetch) {
    global.fetch = jest.fn();
  }

  // Mock console methods to reduce noise in tests
  const originalError = console.error;
  const originalWarn = console.warn;
  
  console.error = (...args: any[]) => {
    // Ignore React warnings in tests
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning:') || args[0].includes('React'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };

  console.warn = (...args: any[]) => {
    // Ignore specific warnings
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('componentWillReceiveProps') ||
       args[0].includes('componentWillUpdate'))
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

// Reset handlers after each test
afterEach(() => {
  server.resetHandlers();
  
  // Clear all mocks
  jest.clearAllMocks();
  
  // Clear localStorage
  localStorage.clear();
  sessionStorage.clear();
  
  // Clear authentication
  testHelpers.clearAuth();
  
  // Clear any timers
  jest.clearAllTimers();
});

// Clean up after all tests
afterAll(() => {
  server.close();
  jest.restoreAllMocks();
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Extend Jest matchers
expect.extend({
  toBeAccessible(received) {
    const hasAriaLabel = received.hasAttribute('aria-label') || 
                        received.hasAttribute('aria-labelledby');
    const hasRole = received.hasAttribute('role');
    const isFocusable = received.tabIndex >= 0 || 
                       ['button', 'input', 'select', 'textarea', 'a']
                         .includes(received.tagName.toLowerCase());
    
    const pass = hasAriaLabel || hasRole || !isFocusable;
    
    return {
      pass,
      message: () => 
        pass 
          ? `Expected element not to be accessible`
          : `Expected element to be accessible (have aria-label, aria-labelledby, or role attribute)`,
    };
  },

  toHaveValidContrast(received) {
    const styles = window.getComputedStyle(received);
    const backgroundColor = styles.backgroundColor;
    const color = styles.color;
    
    // Simplified contrast check for testing
    const pass = backgroundColor !== color && 
                 backgroundColor !== 'transparent' && 
                 color !== 'transparent';
    
    return {
      pass,
      message: () =>
        pass
          ? `Expected element not to have valid contrast`
          : `Expected element to have valid color contrast`,
    };
  },

  toBeVisible(received) {
    const styles = window.getComputedStyle(received);
    const pass = styles.display !== 'none' && 
                 styles.visibility !== 'hidden' && 
                 styles.opacity !== '0';
    
    return {
      pass,
      message: () =>
        pass
          ? `Expected element not to be visible`
          : `Expected element to be visible`,
    };
  },

  toHaveLoadingState(received) {
    const hasLoadingAttribute = received.hasAttribute('aria-busy') && 
                               received.getAttribute('aria-busy') === 'true';
    const hasLoadingClass = received.classList.contains('loading') ||
                           received.classList.contains('animate-pulse');
    const hasLoadingText = received.textContent?.includes('Loading') ||
                          received.textContent?.includes('loading');
    
    const pass = hasLoadingAttribute || hasLoadingClass || hasLoadingText;
    
    return {
      pass,
      message: () =>
        pass
          ? `Expected element not to have loading state`
          : `Expected element to have loading state`,
    };
  },
});

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.REACT_APP_API_BASE_URL = 'http://localhost:3001/api';
process.env.REACT_APP_ENVIRONMENT = 'test';

// Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByName: jest.fn(() => []),
    getEntriesByType: jest.fn(() => []),
  },
});

// Mock requestAnimationFrame
Object.defineProperty(window, 'requestAnimationFrame', {
  value: jest.fn((cb) => setTimeout(cb, 16)),
});

Object.defineProperty(window, 'cancelAnimationFrame', {
  value: jest.fn((id) => clearTimeout(id)),
});

// Mock crypto for UUID generation
Object.defineProperty(window, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'mock-uuid-1234'),
    getRandomValues: jest.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    }),
  },
});

// Mock File and FileReader
global.File = class MockFile {
  constructor(public parts: any[], public name: string, public options: any = {}) {}
  get size() { return 1024; }
  get type() { return this.options.type || 'text/plain'; }
} as any;

global.FileReader = class MockFileReader {
  result: any = null;
  error: any = null;
  readyState: number = 0;
  onload: any = null;
  onerror: any = null;
  onabort: any = null;
  
  readAsText() {
    this.readyState = 2;
    this.result = 'mock file content';
    if (this.onload) this.onload({ target: this });
  }
  
  readAsDataURL() {
    this.readyState = 2;
    this.result = 'data:text/plain;base64,bW9jayBmaWxlIGNvbnRlbnQ=';
    if (this.onload) this.onload({ target: this });
  }
  
  abort() {
    this.readyState = 2;
    if (this.onabort) this.onabort({ target: this });
  }
} as any;

// Mock Blob
global.Blob = class MockBlob {
  constructor(public parts: any[] = [], public options: any = {}) {}
  get size() { return 1024; }
  get type() { return this.options.type || ''; }
} as any;

// Silence specific warnings
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
  if (
    args[0]?.includes?.('React Router Future Flag Warning') ||
    args[0]?.includes?.('componentWillReceiveProps') ||
    args[0]?.includes?.('componentWillUpdate')
  ) {
    return;
  }
  originalConsoleWarn(...args);
};

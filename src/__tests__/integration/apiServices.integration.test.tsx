/**
 * Integration Tests for API Services
 * Tests the complete API service layer including error handling, caching, and data flow
 */

import { waitFor } from '@testing-library/react';
import { QueryClient } from '@tanstack/react-query';
import { adminApi } from '../../services/adminApi';
import { server, mockHandlers } from '../../__mocks__/server';
import { factories, testHelpers } from '../../utils/testUtils';

describe('API Services Integration', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false, gcTime: 0 },
        mutations: { retry: false },
      },
    });
    testHelpers.clearAuth();
    localStorage.clear();
  });

  afterEach(() => {
    server.resetHandlers();
    queryClient.clear();
  });

  describe('Authentication API', () => {
    it('should handle successful login', async () => {
      const mockResponse = factories.auth.createAuthResponse();
      mockHandlers.mockSuccessfulLogin(mockResponse.user);

      const result = await adminApi.login({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(result.user).toEqual(mockResponse.user);
      expect(result.token).toBeTruthy();
    });

    it('should handle login failure', async () => {
      mockHandlers.mockFailedLogin('Invalid credentials');

      await expect(
        adminApi.login({
          email: '<EMAIL>',
          password: 'wrongpassword',
        })
      ).rejects.toThrow('Invalid credentials');
    });

    it('should handle network errors', async () => {
      mockHandlers.mockNetworkError('/admin/login');

      await expect(
        adminApi.login({
          email: '<EMAIL>',
          password: 'password123',
        })
      ).rejects.toThrow('Network error');
    });

    it('should fetch current user info', async () => {
      const mockUser = factories.auth.createAuthUser();
      testHelpers.authenticateUser(mockUser);

      const result = await adminApi.getCurrentUser();

      expect(result).toEqual(mockUser);
    });

    it('should handle unauthorized requests', async () => {
      server.use(
        rest.get('*/auth/me', (req, res, ctx) => {
          return res(ctx.status(401), ctx.json({ message: 'Unauthorized' }));
        })
      );

      await expect(adminApi.getCurrentUser()).rejects.toThrow('Unauthorized');
    });
  });

  describe('Provider API', () => {
    beforeEach(() => {
      testHelpers.authenticateUser();
    });

    it('should fetch providers list with pagination', async () => {
      const mockProviders = factories.provider.createProviderList(10);
      
      const result = await adminApi.getProviders({
        page: 1,
        limit: 10,
      });

      expect(result.data).toHaveLength(10);
      expect(result.pagination).toEqual({
        page: 1,
        limit: 10,
        total: expect.any(Number),
        totalPages: expect.any(Number),
      });
    });

    it('should fetch providers with search filter', async () => {
      const result = await adminApi.getProviders({
        page: 1,
        limit: 10,
        search: 'Dr. Smith',
      });

      expect(result.data).toBeDefined();
      // Mock should filter results based on search
    });

    it('should fetch providers with status filter', async () => {
      const result = await adminApi.getProviders({
        page: 1,
        limit: 10,
        status: 'pending',
      });

      expect(result.data).toBeDefined();
      // Mock should filter results based on status
    });

    it('should fetch single provider details', async () => {
      const mockProvider = factories.provider.createProvider();

      const result = await adminApi.getProvider('provider-1');

      expect(result.id).toBe('provider-1');
      expect(result.name).toBeTruthy();
      expect(result.email).toBeTruthy();
    });

    it('should handle provider not found', async () => {
      server.use(
        rest.get('*/admin/providers/:id', (req, res, ctx) => {
          return res(ctx.status(404), ctx.json({ message: 'Provider not found' }));
        })
      );

      await expect(adminApi.getProvider('nonexistent')).rejects.toThrow('Provider not found');
    });

    it('should approve provider', async () => {
      const result = await adminApi.providers.approveProvider('provider-1', { reason: 'Test approval' });

      expect(result.success).toBe(true);
      expect(result.data.verified).toBe(true);
    });

    it('should reject provider', async () => {
      const result = await adminApi.providers.rejectProvider('provider-1', { reason: 'Test rejection' });

      expect(result.success).toBe(true);
      expect(result.data.verified).toBe(false);
    });

    it('should handle bulk operations', async () => {
      const providerIds = ['provider-1', 'provider-2', 'provider-3'];

      // Mock bulk approval
      server.use(
        rest.put('*/admin/providers/bulk/approve', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              success: true,
              approved: providerIds.length,
              failed: 0,
            })
          );
        })
      );

      const result = await adminApi.bulkApproveProviders(providerIds);

      expect(result.success).toBe(true);
      expect(result.approved).toBe(3);
    });
  });

  describe('Customer API', () => {
    beforeEach(() => {
      testHelpers.authenticateUser();
    });

    it('should fetch customers list', async () => {
      const result = await adminApi.getCustomers({
        page: 1,
        limit: 20,
      });

      expect(result.data).toBeDefined();
      expect(result.pagination).toBeDefined();
    });

    it('should search customers', async () => {
      const result = await adminApi.getCustomers({
        page: 1,
        limit: 20,
        search: 'John Doe',
      });

      expect(result.data).toBeDefined();
    });

    it('should fetch customer details', async () => {
      const result = await adminApi.getCustomer('customer-1');

      expect(result.id).toBe('customer-1');
      expect(result.firstName).toBeTruthy();
      expect(result.lastName).toBeTruthy();
    });

    it('should update customer credits', async () => {
      server.use(
        rest.put('*/admin/customers/:id/credits', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              ...factories.customer.createCustomer({ id: req.params.id }),
              credits: 150,
            })
          );
        })
      );

      const result = await adminApi.updateCustomerCredits('customer-1', 150);

      expect(result.credits).toBe(150);
    });
  });

  describe('Category API', () => {
    beforeEach(() => {
      testHelpers.authenticateUser();
    });

    it('should fetch categories tree', async () => {
      const result = await adminApi.getCategories();

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
    });

    it('should create new category', async () => {
      const newCategory = {
        title: 'New Category',
        description: 'Test category',
        parentId: null,
      };

      const result = await adminApi.createCategory(newCategory);

      expect(result.title).toBe(newCategory.title);
      expect(result.description).toBe(newCategory.description);
      expect(result.id).toBeTruthy();
    });

    it('should update existing category', async () => {
      const updates = {
        title: 'Updated Category',
        description: 'Updated description',
      };

      const result = await adminApi.updateCategory('category-1', updates);

      expect(result.title).toBe(updates.title);
      expect(result.description).toBe(updates.description);
    });

    it('should delete category', async () => {
      const result = await adminApi.deleteCategory('category-1');

      expect(result.message).toBe('Category deleted successfully');
    });

    it('should handle category deletion with dependencies', async () => {
      server.use(
        rest.delete('*/admin/categories/:id', (req, res, ctx) => {
          return res(
            ctx.status(400),
            ctx.json({ message: 'Category has dependent providers' })
          );
        })
      );

      await expect(adminApi.deleteCategory('category-with-providers')).rejects.toThrow(
        'Category has dependent providers'
      );
    });
  });

  describe('Dashboard API', () => {
    beforeEach(() => {
      testHelpers.authenticateUser();
    });

    it('should fetch dashboard statistics', async () => {
      const result = await adminApi.getDashboardStats();

      expect(result.totalProviders).toBeDefined();
      expect(result.activeCustomers).toBeDefined();
      expect(result.totalBookings).toBeDefined();
      expect(result.revenue).toBeDefined();
    });

    it('should fetch recent activity', async () => {
      const result = await adminApi.getRecentActivity();

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty('type');
      expect(result[0]).toHaveProperty('title');
      expect(result[0]).toHaveProperty('timestamp');
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      testHelpers.authenticateUser();
    });

    it('should handle 500 server errors', async () => {
      mockHandlers.mockNetworkError('/admin/providers');

      await expect(
        adminApi.getProviders({ page: 1, limit: 10 })
      ).rejects.toThrow();
    });

    it('should handle timeout errors', async () => {
      mockHandlers.mockSlowResponse('/admin/providers', 10000);

      // Set a shorter timeout for testing
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), 1000);
      });

      await expect(
        Promise.race([
          adminApi.getProviders({ page: 1, limit: 10 }),
          timeoutPromise,
        ])
      ).rejects.toThrow('Request timeout');
    });

    it('should retry failed requests', async () => {
      let attemptCount = 0;
      
      server.use(
        rest.get('*/admin/providers', (req, res, ctx) => {
          attemptCount++;
          if (attemptCount < 3) {
            return res(ctx.status(500), ctx.json({ message: 'Server error' }));
          }
          return res(
            ctx.status(200),
            ctx.json(factories.api.paginated(factories.provider.createProviderList(5)))
          );
        })
      );

      // Configure retry for this test
      const retryQueryClient = new QueryClient({
        defaultOptions: {
          queries: { retry: 2, retryDelay: 100 },
        },
      });

      // Should eventually succeed after retries
      const result = await retryQueryClient.fetchQuery({
        queryKey: ['providers'],
        queryFn: () => adminApi.getProviders({ page: 1, limit: 10 }),
      });

      expect(result.data).toBeDefined();
      expect(attemptCount).toBe(3);
    });
  });

  describe('Caching Behavior', () => {
    beforeEach(() => {
      testHelpers.authenticateUser();
    });

    it('should cache frequently accessed data', async () => {
      let requestCount = 0;
      
      server.use(
        rest.get('*/admin/categories', (req, res, ctx) => {
          requestCount++;
          return res(
            ctx.status(200),
            ctx.json(factories.category.createCategoryTree())
          );
        })
      );

      // First request
      await queryClient.fetchQuery({
        queryKey: ['categories'],
        queryFn: () => adminApi.getCategories(),
        staleTime: 5 * 60 * 1000, // 5 minutes
      });

      // Second request should use cache
      await queryClient.fetchQuery({
        queryKey: ['categories'],
        queryFn: () => adminApi.getCategories(),
        staleTime: 5 * 60 * 1000,
      });

      expect(requestCount).toBe(1); // Only one actual request
    });

    it('should invalidate cache when data changes', async () => {
      let requestCount = 0;
      
      server.use(
        rest.get('*/admin/providers', (req, res, ctx) => {
          requestCount++;
          return res(
            ctx.status(200),
            ctx.json(factories.api.paginated(factories.provider.createProviderList(5)))
          );
        })
      );

      // Initial fetch
      await queryClient.fetchQuery({
        queryKey: ['providers'],
        queryFn: () => adminApi.getProviders({ page: 1, limit: 10 }),
      });

      // Simulate data change (approve provider)
      await adminApi.providers.approveProvider('provider-1', { reason: 'Test approval' });

      // Invalidate cache
      queryClient.invalidateQueries({ queryKey: ['providers'] });

      // Next fetch should make new request
      await queryClient.fetchQuery({
        queryKey: ['providers'],
        queryFn: () => adminApi.getProviders({ page: 1, limit: 10 }),
      });

      expect(requestCount).toBe(2);
    });
  });

  describe('Request Interceptors', () => {
    it('should add authorization headers to requests', async () => {
      const mockUser = factories.auth.createAuthUser();
      testHelpers.authenticateUser(mockUser);

      let authHeader: string | null = null;
      
      server.use(
        rest.get('*/admin/providers', (req, res, ctx) => {
          authHeader = req.headers.get('Authorization');
          return res(
            ctx.status(200),
            ctx.json(factories.api.paginated(factories.provider.createProviderList(1)))
          );
        })
      );

      await adminApi.getProviders({ page: 1, limit: 10 });

      expect(authHeader).toBe('Bearer mock-token');
    });

    it('should handle token refresh on 401 responses', async () => {
      const mockUser = factories.auth.createAuthUser();
      testHelpers.authenticateUser(mockUser);

      let requestCount = 0;
      
      server.use(
        rest.get('*/admin/providers', (req, res, ctx) => {
          requestCount++;
          if (requestCount === 1) {
            return res(ctx.status(401), ctx.json({ message: 'Token expired' }));
          }
          return res(
            ctx.status(200),
            ctx.json(factories.api.paginated(factories.provider.createProviderList(1)))
          );
        }),
        rest.post('*/auth/refresh', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              token: 'new-token',
              user: mockUser,
            })
          );
        })
      );

      // Should automatically retry with new token
      const result = await adminApi.getProviders({ page: 1, limit: 10 });

      expect(result.data).toBeDefined();
      expect(requestCount).toBe(2); // Initial request + retry
    });
  });
});

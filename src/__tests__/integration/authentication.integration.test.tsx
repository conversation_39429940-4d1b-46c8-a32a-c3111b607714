/**
 * Integration Tests for Authentication Flow
 * Tests the complete authentication workflow including login, token management, and protected routes
 */

import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, testHelpers, factories } from '../../utils/testUtils';
import { server, mockHandlers } from '../../__mocks__/server';
import App from '../../App';
import AdminLogin from '../../components/auth/AdminLogin';
import { AuthProvider } from '../../contexts/AuthContext';

describe('Authentication Integration', () => {
  beforeEach(() => {
    testHelpers.clearAuth();
    localStorage.clear();
    sessionStorage.clear();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  describe('Login Flow', () => {
    it('should complete full login workflow', async () => {
      const user = userEvent.setup();
      const mockUser = factories.auth.createAuthUser();
      
      // Mock successful login
      mockHandlers.mockSuccessfulLogin(mockUser);
      
      renderWithProviders(<AdminLogin />);
      
      // Fill in login form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /sign in/i });
      
      await user.type(emailInput, mockUser.email);
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);
      
      // Should show loading state
      expect(screen.getByText(/signing in/i)).toBeInTheDocument();
      
      // Should redirect to dashboard after successful login
      await waitFor(() => {
        expect(localStorage.getItem('dalti_admin_token')).toBeTruthy();
        expect(localStorage.getItem('dalti_admin_user')).toBeTruthy();
      });
    });

    it('should handle login failure gracefully', async () => {
      const user = userEvent.setup();
      
      // Mock failed login
      mockHandlers.mockFailedLogin('Invalid credentials');
      
      renderWithProviders(<AdminLogin />);
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /sign in/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(loginButton);
      
      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
      });
      
      // Should not store any tokens
      expect(localStorage.getItem('dalti_admin_token')).toBeNull();
      expect(localStorage.getItem('dalti_admin_user')).toBeNull();
    });

    it('should handle network errors during login', async () => {
      const user = userEvent.setup();
      
      // Mock network error
      mockHandlers.mockNetworkError('/admin/login');
      
      renderWithProviders(<AdminLogin />);
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /sign in/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);
      
      // Should show network error message
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
      });
    });

    it('should validate form inputs before submission', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(<AdminLogin />);
      
      const loginButton = screen.getByRole('button', { name: /sign in/i });
      
      // Try to submit empty form
      await user.click(loginButton);
      
      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });
      
      // Should not make API call
      expect(localStorage.getItem('dalti_admin_token')).toBeNull();
    });
  });

  describe('Token Management', () => {
    it('should automatically authenticate user with valid stored token', async () => {
      const mockUser = factories.auth.createAuthUser();
      
      // Pre-populate localStorage with valid token
      localStorage.setItem('dalti_admin_token', 'valid-token');
      localStorage.setItem('dalti_admin_user', JSON.stringify(mockUser));
      
      renderWithProviders(<App />, { authUser: mockUser });
      
      // Should automatically authenticate and show dashboard
      await waitFor(() => {
        expect(screen.queryByText(/sign in/i)).not.toBeInTheDocument();
      });
    });

    it('should handle expired token gracefully', async () => {
      // Mock expired token response
      server.use(
        rest.get('*/auth/me', (req, res, ctx) => {
          return res(ctx.status(401), ctx.json({ message: 'Token expired' }));
        })
      );
      
      // Pre-populate localStorage with expired token
      localStorage.setItem('dalti_admin_token', 'expired-token');
      localStorage.setItem('dalti_admin_user', JSON.stringify(factories.auth.createAuthUser()));
      
      renderWithProviders(<App />);
      
      // Should clear tokens and redirect to login
      await waitFor(() => {
        expect(localStorage.getItem('dalti_admin_token')).toBeNull();
        expect(localStorage.getItem('dalti_admin_user')).toBeNull();
        expect(screen.getByText(/sign in/i)).toBeInTheDocument();
      });
    });

    it('should refresh token automatically when needed', async () => {
      const mockUser = factories.auth.createAuthUser();
      let tokenRefreshed = false;
      
      // Mock token refresh
      server.use(
        rest.post('*/auth/refresh', (req, res, ctx) => {
          tokenRefreshed = true;
          return res(
            ctx.status(200),
            ctx.json({
              token: 'new-token',
              user: mockUser,
            })
          );
        })
      );
      
      // Pre-populate with token that needs refresh
      localStorage.setItem('dalti_admin_token', 'token-needs-refresh');
      localStorage.setItem('dalti_admin_user', JSON.stringify(mockUser));
      
      renderWithProviders(<App />, { authUser: mockUser });
      
      // Should refresh token automatically
      await waitFor(() => {
        expect(tokenRefreshed).toBe(true);
      });
    });
  });

  describe('Protected Routes', () => {
    it('should redirect unauthenticated users to login', async () => {
      renderWithProviders(<App />);
      
      // Should show login page for unauthenticated user
      expect(screen.getByText(/sign in/i)).toBeInTheDocument();
      expect(screen.queryByText(/dashboard/i)).not.toBeInTheDocument();
    });

    it('should allow authenticated users to access protected routes', async () => {
      const mockUser = factories.auth.createAuthUser();
      
      renderWithProviders(<App />, { authUser: mockUser });
      
      // Should show dashboard for authenticated user
      await waitFor(() => {
        expect(screen.queryByText(/sign in/i)).not.toBeInTheDocument();
      });
    });

    it('should handle route protection for admin-only areas', async () => {
      const regularUser = factories.auth.createAuthUser({ 
        isAdmin: false,
        role: 'user' 
      });
      
      renderWithProviders(<App />, { authUser: regularUser });
      
      // Should redirect non-admin users
      await waitFor(() => {
        expect(screen.getByText(/unauthorized/i)).toBeInTheDocument();
      });
    });
  });

  describe('Logout Flow', () => {
    it('should complete logout workflow', async () => {
      const user = userEvent.setup();
      const mockUser = factories.auth.createAuthUser();
      
      // Start with authenticated user
      renderWithProviders(<App />, { authUser: mockUser });
      
      // Find and click logout button
      const logoutButton = screen.getByRole('button', { name: /logout/i });
      await user.click(logoutButton);
      
      // Should clear tokens and redirect to login
      await waitFor(() => {
        expect(localStorage.getItem('dalti_admin_token')).toBeNull();
        expect(localStorage.getItem('dalti_admin_user')).toBeNull();
        expect(screen.getByText(/sign in/i)).toBeInTheDocument();
      });
    });

    it('should handle logout API call failure gracefully', async () => {
      const user = userEvent.setup();
      const mockUser = factories.auth.createAuthUser();
      
      // Mock logout API failure
      server.use(
        rest.post('*/admin/logout', (req, res, ctx) => {
          return res(ctx.status(500), ctx.json({ message: 'Server error' }));
        })
      );
      
      renderWithProviders(<App />, { authUser: mockUser });
      
      const logoutButton = screen.getByRole('button', { name: /logout/i });
      await user.click(logoutButton);
      
      // Should still clear local tokens even if API fails
      await waitFor(() => {
        expect(localStorage.getItem('dalti_admin_token')).toBeNull();
        expect(localStorage.getItem('dalti_admin_user')).toBeNull();
        expect(screen.getByText(/sign in/i)).toBeInTheDocument();
      });
    });
  });

  describe('Session Management', () => {
    it('should handle concurrent sessions', async () => {
      const mockUser = factories.auth.createAuthUser();
      
      // Simulate token being invalidated in another tab
      localStorage.setItem('dalti_admin_token', 'valid-token');
      localStorage.setItem('dalti_admin_user', JSON.stringify(mockUser));
      
      renderWithProviders(<App />, { authUser: mockUser });
      
      // Simulate storage event (token cleared in another tab)
      fireEvent(window, new StorageEvent('storage', {
        key: 'dalti_admin_token',
        oldValue: 'valid-token',
        newValue: null,
      }));
      
      // Should detect token removal and redirect to login
      await waitFor(() => {
        expect(screen.getByText(/sign in/i)).toBeInTheDocument();
      });
    });

    it('should handle session timeout', async () => {
      const mockUser = factories.auth.createAuthUser();
      
      // Mock session timeout
      server.use(
        rest.get('*/auth/me', (req, res, ctx) => {
          return res(ctx.status(401), ctx.json({ message: 'Session expired' }));
        })
      );
      
      localStorage.setItem('dalti_admin_token', 'expired-token');
      localStorage.setItem('dalti_admin_user', JSON.stringify(mockUser));
      
      renderWithProviders(<App />);
      
      // Should handle session timeout and redirect to login
      await waitFor(() => {
        expect(screen.getByText(/session expired/i)).toBeInTheDocument();
        expect(screen.getByText(/sign in/i)).toBeInTheDocument();
      });
    });
  });

  describe('Authentication Context Integration', () => {
    it('should provide authentication state to child components', async () => {
      const mockUser = factories.auth.createAuthUser();
      
      const TestComponent = () => {
        const { user, isAuthenticated, isLoading } = useAuth();
        
        if (isLoading) return <div>Loading...</div>;
        if (!isAuthenticated) return <div>Not authenticated</div>;
        
        return <div>Welcome {user?.firstName}</div>;
      };
      
      renderWithProviders(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>,
        { authUser: mockUser }
      );
      
      await waitFor(() => {
        expect(screen.getByText(`Welcome ${mockUser.firstName}`)).toBeInTheDocument();
      });
    });

    it('should update authentication state across components', async () => {
      const user = userEvent.setup();
      const mockUser = factories.auth.createAuthUser();
      
      const AuthStatus = () => {
        const { user, logout } = useAuth();
        
        return (
          <div>
            {user ? (
              <div>
                <span>Logged in as {user.email}</span>
                <button onClick={logout}>Logout</button>
              </div>
            ) : (
              <span>Not logged in</span>
            )}
          </div>
        );
      };
      
      renderWithProviders(
        <AuthProvider>
          <AuthStatus />
        </AuthProvider>,
        { authUser: mockUser }
      );
      
      // Should show authenticated state
      expect(screen.getByText(`Logged in as ${mockUser.email}`)).toBeInTheDocument();
      
      // Logout should update state
      const logoutButton = screen.getByRole('button', { name: /logout/i });
      await user.click(logoutButton);
      
      await waitFor(() => {
        expect(screen.getByText('Not logged in')).toBeInTheDocument();
      });
    });
  });

  describe('Error Recovery', () => {
    it('should recover from authentication errors', async () => {
      const user = userEvent.setup();
      
      // Start with network error
      mockHandlers.mockNetworkError('/admin/login');
      
      renderWithProviders(<AdminLogin />);
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /sign in/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);
      
      // Should show error
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
      });
      
      // Fix network and retry
      mockHandlers.resetHandlers();
      mockHandlers.mockSuccessfulLogin();
      
      await user.click(loginButton);
      
      // Should succeed on retry
      await waitFor(() => {
        expect(localStorage.getItem('dalti_admin_token')).toBeTruthy();
      });
    });
  });
});

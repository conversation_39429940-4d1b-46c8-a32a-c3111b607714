/**
 * Integration Tests for Critical User Workflows
 * Tests complete user journeys through the admin panel
 */

import React from 'react';
import { screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, testHelpers, factories } from '../../utils/testUtils';
import { server, mockHandlers } from '../../__mocks__/server';
import App from '../../App';

describe('Critical User Workflows', () => {
  beforeEach(() => {
    testHelpers.clearAuth();
    localStorage.clear();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  describe('Provider Management Workflow', () => {
    it('should complete provider approval workflow', async () => {
      const user = userEvent.setup();
      const mockAdmin = factories.auth.createAuthUser();
      const mockProviders = factories.provider.createProviderList(5).map((p, i) => ({
        ...p,
        status: i < 2 ? 'pending' : 'approved',
      }));

      // Mock API responses
      server.use(
        rest.get('*/admin/providers', (req, res, ctx) => {
          const status = req.url.searchParams.get('status');
          const filteredProviders = status 
            ? mockProviders.filter(p => p.status === status)
            : mockProviders;
          
          return res(
            ctx.status(200),
            ctx.json(factories.api.paginated(filteredProviders))
          );
        })
      );

      renderWithProviders(<App />, { authUser: mockAdmin });

      // Navigate to providers page
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });

      const providersLink = screen.getByRole('link', { name: /providers/i });
      await user.click(providersLink);

      // Should show providers list
      await waitFor(() => {
        expect(screen.getByText(/provider management/i)).toBeInTheDocument();
      });

      // Filter by pending status
      const statusFilter = screen.getByRole('combobox', { name: /status/i });
      await user.selectOptions(statusFilter, 'pending');

      // Should show only pending providers
      await waitFor(() => {
        const providerRows = screen.getAllByRole('row');
        expect(providerRows).toHaveLength(3); // Header + 2 pending providers
      });

      // Select first provider for approval
      const firstProviderRow = screen.getAllByRole('row')[1];
      const checkbox = within(firstProviderRow).getByRole('checkbox');
      await user.click(checkbox);

      // Click approve button
      const approveButton = screen.getByRole('button', { name: /approve/i });
      await user.click(approveButton);

      // Should show confirmation dialog
      await waitFor(() => {
        expect(screen.getByText(/confirm approval/i)).toBeInTheDocument();
      });

      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      await user.click(confirmButton);

      // Should show success message
      await waitFor(() => {
        expect(screen.getByText(/provider approved successfully/i)).toBeInTheDocument();
      });

      // Provider should be removed from pending list
      await waitFor(() => {
        const providerRows = screen.getAllByRole('row');
        expect(providerRows).toHaveLength(2); // Header + 1 remaining pending provider
      });
    });

    it('should handle bulk provider approval', async () => {
      const user = userEvent.setup();
      const mockAdmin = factories.auth.createAuthUser();
      const pendingProviders = factories.provider.createProviderList(3).map(p => ({
        ...p,
        status: 'pending',
      }));

      server.use(
        rest.get('*/admin/providers', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json(factories.api.paginated(pendingProviders))
          );
        }),
        rest.put('*/admin/providers/bulk/approve', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              success: true,
              approved: 3,
              failed: 0,
            })
          );
        })
      );

      renderWithProviders(<App />, { authUser: mockAdmin });

      // Navigate to providers
      const providersLink = screen.getByRole('link', { name: /providers/i });
      await user.click(providersLink);

      await waitFor(() => {
        expect(screen.getByText(/provider management/i)).toBeInTheDocument();
      });

      // Select all providers
      const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i });
      await user.click(selectAllCheckbox);

      // Click bulk approve
      const bulkApproveButton = screen.getByRole('button', { name: /bulk approve/i });
      await user.click(bulkApproveButton);

      // Confirm bulk approval
      await waitFor(() => {
        expect(screen.getByText(/approve 3 providers/i)).toBeInTheDocument();
      });

      const confirmButton = screen.getByRole('button', { name: /approve all/i });
      await user.click(confirmButton);

      // Should show success message
      await waitFor(() => {
        expect(screen.getByText(/3 providers approved successfully/i)).toBeInTheDocument();
      });
    });

    it('should view provider details and history', async () => {
      const user = userEvent.setup();
      const mockAdmin = factories.auth.createAuthUser();
      const mockProvider = factories.provider.createProvider({
        id: 'provider-123',
        name: 'Dr. John Smith',
        email: '<EMAIL>',
      });

      server.use(
        rest.get('*/admin/providers/provider-123', (req, res, ctx) => {
          return res(ctx.status(200), ctx.json(mockProvider));
        }),
        rest.get('*/admin/providers/provider-123/history', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json([
              {
                id: 'history-1',
                action: 'registration',
                timestamp: '2024-01-15T10:00:00Z',
                details: 'Provider registered',
              },
              {
                id: 'history-2',
                action: 'verification',
                timestamp: '2024-01-16T14:30:00Z',
                details: 'Documents verified',
              },
            ])
          );
        })
      );

      renderWithProviders(<App />, { authUser: mockAdmin });

      // Navigate to provider details
      const providersLink = screen.getByRole('link', { name: /providers/i });
      await user.click(providersLink);

      await waitFor(() => {
        const viewButton = screen.getByRole('button', { name: /view/i });
        await user.click(viewButton);
      });

      // Should show provider details
      await waitFor(() => {
        expect(screen.getByText('Dr. John Smith')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      // Switch to history tab
      const historyTab = screen.getByRole('tab', { name: /history/i });
      await user.click(historyTab);

      // Should show provider history
      await waitFor(() => {
        expect(screen.getByText(/provider registered/i)).toBeInTheDocument();
        expect(screen.getByText(/documents verified/i)).toBeInTheDocument();
      });
    });
  });

  describe('Customer Management Workflow', () => {
    it('should search and manage customer accounts', async () => {
      const user = userEvent.setup();
      const mockAdmin = factories.auth.createAuthUser();
      const mockCustomers = factories.customer.createCustomerList(10);

      server.use(
        rest.get('*/admin/customers', (req, res, ctx) => {
          const search = req.url.searchParams.get('search');
          let filteredCustomers = mockCustomers;
          
          if (search) {
            filteredCustomers = mockCustomers.filter(c => 
              c.firstName.toLowerCase().includes(search.toLowerCase()) ||
              c.lastName.toLowerCase().includes(search.toLowerCase()) ||
              c.email.toLowerCase().includes(search.toLowerCase())
            );
          }
          
          return res(
            ctx.status(200),
            ctx.json(factories.api.paginated(filteredCustomers))
          );
        })
      );

      renderWithProviders(<App />, { authUser: mockAdmin });

      // Navigate to customers
      const customersLink = screen.getByRole('link', { name: /customers/i });
      await user.click(customersLink);

      await waitFor(() => {
        expect(screen.getByText(/customer management/i)).toBeInTheDocument();
      });

      // Search for specific customer
      const searchInput = screen.getByRole('textbox', { name: /search/i });
      await user.type(searchInput, 'John');

      // Should filter results
      await waitFor(() => {
        const customerRows = screen.getAllByRole('row');
        expect(customerRows.length).toBeGreaterThan(1); // Header + filtered results
      });

      // Clear search
      await user.clear(searchInput);

      // Should show all customers again
      await waitFor(() => {
        const customerRows = screen.getAllByRole('row');
        expect(customerRows).toHaveLength(11); // Header + 10 customers
      });
    });

    it('should update customer credits', async () => {
      const user = userEvent.setup();
      const mockAdmin = factories.auth.createAuthUser();
      const mockCustomer = factories.customer.createCustomer({
        id: 'customer-123',
        credits: 100,
      });

      server.use(
        rest.get('*/admin/customers/customer-123', (req, res, ctx) => {
          return res(ctx.status(200), ctx.json(mockCustomer));
        }),
        rest.put('*/admin/customers/customer-123/credits', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              ...mockCustomer,
              credits: 150,
            })
          );
        })
      );

      renderWithProviders(<App />, { authUser: mockAdmin });

      // Navigate to customer details
      const customersLink = screen.getByRole('link', { name: /customers/i });
      await user.click(customersLink);

      await waitFor(() => {
        const viewButton = screen.getByRole('button', { name: /view/i });
        await user.click(viewButton);
      });

      // Should show customer details
      await waitFor(() => {
        expect(screen.getByText(/credits: 100/i)).toBeInTheDocument();
      });

      // Update credits
      const updateCreditsButton = screen.getByRole('button', { name: /update credits/i });
      await user.click(updateCreditsButton);

      const creditsInput = screen.getByRole('spinbutton', { name: /credits/i });
      await user.clear(creditsInput);
      await user.type(creditsInput, '150');

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      // Should show updated credits
      await waitFor(() => {
        expect(screen.getByText(/credits: 150/i)).toBeInTheDocument();
        expect(screen.getByText(/credits updated successfully/i)).toBeInTheDocument();
      });
    });
  });

  describe('Category Management Workflow', () => {
    it('should create and manage category hierarchy', async () => {
      const user = userEvent.setup();
      const mockAdmin = factories.auth.createAuthUser();
      const mockCategories = factories.category.createCategoryTree();

      server.use(
        rest.get('*/admin/categories', (req, res, ctx) => {
          return res(ctx.status(200), ctx.json(mockCategories));
        }),
        rest.post('*/admin/categories', (req, res, ctx) => {
          const newCategory = {
            id: 'new-category-id',
            ...req.body,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          return res(ctx.status(201), ctx.json(newCategory));
        })
      );

      renderWithProviders(<App />, { authUser: mockAdmin });

      // Navigate to categories
      const categoriesLink = screen.getByRole('link', { name: /categories/i });
      await user.click(categoriesLink);

      await waitFor(() => {
        expect(screen.getByText(/category management/i)).toBeInTheDocument();
      });

      // Should show category tree
      expect(screen.getByText(/healthcare/i)).toBeInTheDocument();
      expect(screen.getByText(/beauty & wellness/i)).toBeInTheDocument();

      // Add new category
      const addCategoryButton = screen.getByRole('button', { name: /add category/i });
      await user.click(addCategoryButton);

      // Fill category form
      const titleInput = screen.getByRole('textbox', { name: /title/i });
      const descriptionInput = screen.getByRole('textbox', { name: /description/i });

      await user.type(titleInput, 'Fitness & Sports');
      await user.type(descriptionInput, 'Fitness and sports services');

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      // Should show success message
      await waitFor(() => {
        expect(screen.getByText(/category created successfully/i)).toBeInTheDocument();
      });

      // New category should appear in tree
      await waitFor(() => {
        expect(screen.getByText(/fitness & sports/i)).toBeInTheDocument();
      });
    });

    it('should handle category deletion with safety checks', async () => {
      const user = userEvent.setup();
      const mockAdmin = factories.auth.createAuthUser();

      server.use(
        rest.delete('*/admin/categories/category-with-providers', (req, res, ctx) => {
          return res(
            ctx.status(400),
            ctx.json({ message: 'Cannot delete category with active providers' })
          );
        }),
        rest.delete('*/admin/categories/empty-category', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({ message: 'Category deleted successfully' })
          );
        })
      );

      renderWithProviders(<App />, { authUser: mockAdmin });

      // Navigate to categories
      const categoriesLink = screen.getByRole('link', { name: /categories/i });
      await user.click(categoriesLink);

      // Try to delete category with providers
      const deleteButton = screen.getByRole('button', { name: /delete category-with-providers/i });
      await user.click(deleteButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/cannot delete category with active providers/i)).toBeInTheDocument();
      });

      // Delete empty category
      const deleteEmptyButton = screen.getByRole('button', { name: /delete empty-category/i });
      await user.click(deleteEmptyButton);

      // Confirm deletion
      const confirmButton = screen.getByRole('button', { name: /confirm delete/i });
      await user.click(confirmButton);

      // Should show success message
      await waitFor(() => {
        expect(screen.getByText(/category deleted successfully/i)).toBeInTheDocument();
      });
    });
  });

  describe('Dashboard Analytics Workflow', () => {
    it('should display and interact with dashboard analytics', async () => {
      const user = userEvent.setup();
      const mockAdmin = factories.auth.createAuthUser();

      server.use(
        rest.get('*/admin/dashboard/stats', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              totalProviders: 2847,
              activeCustomers: 18392,
              totalBookings: 45621,
              revenue: 284750,
              pendingApprovals: 23,
            })
          );
        }),
        rest.get('*/admin/dashboard/activity', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json([
              {
                id: '1',
                type: 'provider_registration',
                title: 'New Provider Registration',
                description: 'Dr. Sarah Johnson registered',
                timestamp: new Date().toISOString(),
              },
            ])
          );
        })
      );

      renderWithProviders(<App />, { authUser: mockAdmin });

      // Should show dashboard by default
      await waitFor(() => {
        expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
      });

      // Should display statistics
      expect(screen.getByText(/2,847/)).toBeInTheDocument(); // Total providers
      expect(screen.getByText(/18,392/)).toBeInTheDocument(); // Active customers
      expect(screen.getByText(/45,621/)).toBeInTheDocument(); // Total bookings

      // Should show recent activity
      expect(screen.getByText(/new provider registration/i)).toBeInTheDocument();
      expect(screen.getByText(/dr. sarah johnson/i)).toBeInTheDocument();

      // Click on pending approvals
      const pendingApprovalsCard = screen.getByText(/23/).closest('div');
      if (pendingApprovalsCard) {
        await user.click(pendingApprovalsCard);
      }

      // Should navigate to providers with pending filter
      await waitFor(() => {
        expect(screen.getByText(/provider management/i)).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling Workflows', () => {
    it('should handle network errors gracefully', async () => {
      const user = userEvent.setup();
      const mockAdmin = factories.auth.createAuthUser();

      // Mock network error
      mockHandlers.mockNetworkError('/admin/providers');

      renderWithProviders(<App />, { authUser: mockAdmin });

      // Navigate to providers
      const providersLink = screen.getByRole('link', { name: /providers/i });
      await user.click(providersLink);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/failed to load providers/i)).toBeInTheDocument();
      });

      // Should show retry button
      const retryButton = screen.getByRole('button', { name: /retry/i });
      expect(retryButton).toBeInTheDocument();

      // Fix network and retry
      mockHandlers.resetHandlers();
      await user.click(retryButton);

      // Should load successfully
      await waitFor(() => {
        expect(screen.getByText(/provider management/i)).toBeInTheDocument();
      });
    });

    it('should handle session expiration during workflow', async () => {
      const user = userEvent.setup();
      const mockAdmin = factories.auth.createAuthUser();

      renderWithProviders(<App />, { authUser: mockAdmin });

      // Start on dashboard
      await waitFor(() => {
        expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
      });

      // Mock session expiration
      server.use(
        rest.get('*/admin/providers', (req, res, ctx) => {
          return res(ctx.status(401), ctx.json({ message: 'Session expired' }));
        })
      );

      // Try to navigate to providers
      const providersLink = screen.getByRole('link', { name: /providers/i });
      await user.click(providersLink);

      // Should redirect to login
      await waitFor(() => {
        expect(screen.getByText(/session expired/i)).toBeInTheDocument();
        expect(screen.getByText(/sign in/i)).toBeInTheDocument();
      });
    });
  });
});

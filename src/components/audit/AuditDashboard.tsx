import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';

interface AuditStats {
  totalLogs: number;
  logsByAction: { action: string; count: number }[];
  logsByEntityType: { entityType: string; count: number }[];
  logsByAdmin: { adminEmail: string; count: number }[];
  dailyActivity: { date: string; count: number }[];
}

interface AuditDashboardProps {
  className?: string;
}

export default function AuditDashboard({ className = '' }: AuditDashboardProps) {
  const [stats, setStats] = useState<AuditStats | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const loading = useLoading();

  useEffect(() => {
    fetchAuditStats();
  }, [selectedPeriod]);

  const fetchAuditStats = async () => {
    try {
      loading.startLoading({ message: 'Loading audit statistics...' });
      const response = await adminApi.auditLogs.getAuditLogStats(selectedPeriod);
      
      if (response.success) {
        setStats(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch audit statistics');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_audit_stats' });
      // Set fallback data for development
      setStats({
        totalLogs: 1247,
        logsByAction: [
          { action: 'provider_approved', count: 156 },
          { action: 'category_created', count: 89 },
          { action: 'customer_credits_updated', count: 234 },
          { action: 'system_settings_updated', count: 45 },
          { action: 'provider_rejected', count: 23 },
          { action: 'category_updated', count: 167 },
          { action: 'auth_login', count: 445 },
          { action: 'auth_logout', count: 432 },
        ],
        logsByEntityType: [
          { entityType: 'provider', count: 456 },
          { entityType: 'customer', count: 234 },
          { entityType: 'category', count: 256 },
          { entityType: 'system', count: 301 },
        ],
        logsByAdmin: [
          { adminEmail: '<EMAIL>', count: 678 },
          { adminEmail: '<EMAIL>', count: 345 },
          { adminEmail: '<EMAIL>', count: 224 },
        ],
        dailyActivity: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          count: Math.floor(Math.random() * 50) + 10,
        })),
      });
    } finally {
      loading.stopLoading();
    }
  };

  const periods = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 3 Months' },
    { value: '1y', label: 'Last Year' },
  ];

  const getActionIcon = (action: string) => {
    if (action.includes('approved') || action.includes('created')) {
      return '✅';
    } else if (action.includes('rejected') || action.includes('deleted')) {
      return '❌';
    } else if (action.includes('updated') || action.includes('modified')) {
      return '✏️';
    } else if (action.includes('login')) {
      return '🔐';
    } else if (action.includes('logout')) {
      return '🚪';
    } else {
      return '📝';
    }
  };

  const getEntityTypeIcon = (entityType: string) => {
    switch (entityType) {
      case 'provider':
        return '🏢';
      case 'customer':
        return '👤';
      case 'category':
        return '📁';
      case 'system':
        return '⚙️';
      default:
        return '📄';
    }
  };

  const formatAction = (action: string) => {
    return action
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  if (loading.isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            Unable to load audit statistics
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Audit Dashboard
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Overview of administrative activity and system changes
            </p>
          </div>
          
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            {periods.map((period) => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Logs</p>
                <p className="text-2xl font-semibold text-blue-900 dark:text-blue-100">{stats.totalLogs.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
                <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Active Admins</p>
                <p className="text-2xl font-semibold text-green-900 dark:text-green-100">{stats.logsByAdmin.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Daily Average</p>
                <p className="text-2xl font-semibold text-purple-900 dark:text-purple-100">
                  {Math.round(stats.dailyActivity.reduce((sum, day) => sum + day.count, 0) / stats.dailyActivity.length)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Top Action</p>
                <p className="text-sm font-semibold text-yellow-900 dark:text-yellow-100">
                  {formatAction(stats.logsByAction[0]?.action || 'N/A')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Charts and Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Actions Breakdown */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Top Actions
            </h3>
            <div className="space-y-3">
              {stats.logsByAction.slice(0, 8).map((action, index) => (
                <div key={action.action} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{getActionIcon(action.action)}</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatAction(action.action)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div
                        className="h-2 bg-blue-500 rounded-full"
                        style={{ width: `${(action.count / stats.logsByAction[0].count) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-12 text-right">
                      {action.count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Entity Types */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Activity by Entity Type
            </h3>
            <div className="space-y-4">
              {stats.logsByEntityType.map((entity) => (
                <div key={entity.entityType} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{getEntityTypeIcon(entity.entityType)}</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                      {entity.entityType}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-3">
                      <div
                        className="h-3 bg-green-500 rounded-full"
                        style={{ width: `${(entity.count / stats.totalLogs) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-12 text-right">
                      {entity.count}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 w-10 text-right">
                      {((entity.count / stats.totalLogs) * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Admin Activity */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Admin Activity
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {stats.logsByAdmin.map((admin, index) => (
              <div key={admin.adminEmail} className="bg-white dark:bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${
                      index === 0 ? 'bg-yellow-500' : 
                      index === 1 ? 'bg-gray-400' : 
                      index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                    }`}></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {admin.adminEmail}
                    </span>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    #{index + 1}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-lg font-semibold text-gray-900 dark:text-white">
                    {admin.count}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {((admin.count / stats.totalLogs) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                  <div
                    className={`h-2 rounded-full ${
                      index === 0 ? 'bg-yellow-500' : 
                      index === 1 ? 'bg-gray-400' : 
                      index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                    }`}
                    style={{ width: `${(admin.count / stats.logsByAdmin[0].count) * 100}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Daily Activity Chart */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Daily Activity Trend
          </h3>
          <div className="h-64 flex items-end justify-between space-x-1">
            {stats.dailyActivity.slice(-14).map((day, index) => {
              const maxCount = Math.max(...stats.dailyActivity.map(d => d.count));
              const height = (day.count / maxCount) * 100;
              
              return (
                <div key={day.date} className="flex flex-col items-center flex-1">
                  <div
                    className="w-full bg-blue-500 rounded-t hover:bg-blue-600 transition-colors cursor-pointer"
                    style={{ height: `${height}%`, minHeight: '4px' }}
                    title={`${day.date}: ${day.count} logs`}
                  ></div>
                  <span className="text-xs text-gray-500 dark:text-gray-400 mt-2 transform -rotate-45 origin-top-left">
                    {new Date(day.date).toLocaleDateString('en', { month: 'short', day: 'numeric' })}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

import React, { useState, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { BRAND_CONFIG } from '../../constants/brand';
import { LoadingButton } from './LoadingOverlay';
import toast from 'react-hot-toast';

interface ImageUploadProps {
  currentImage?: string;
  onImageUpload: (file: File) => Promise<void>;
  onImageRemove?: () => Promise<void>;
  loading?: boolean;
  disabled?: boolean;
  className?: string;
  maxSize?: number;
  acceptedTypes?: string[];
  placeholder?: string;
}

export default function ImageUpload({
  currentImage,
  onImageUpload,
  onImageRemove,
  loading = false,
  disabled = false,
  className = '',
  maxSize = BRAND_CONFIG.uploads.maxFileSize,
  acceptedTypes = BRAND_CONFIG.uploads.allowedImageTypes,
  placeholder = 'Click to upload or drag and drop an image',
}: ImageUploadProps) {
  const [preview, setPreview] = useState<string | null>(currentImage || null);
  const [isDragActive, setIsDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `Invalid file type. Accepted types: ${acceptedTypes.join(', ')}`;
    }
    
    if (file.size > maxSize) {
      return `File size too large. Maximum size: ${(maxSize / (1024 * 1024)).toFixed(1)}MB`;
    }
    
    return null;
  };

  const handleFileSelect = async (file: File) => {
    const error = validateFile(file);
    if (error) {
      toast.error(error);
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    try {
      await onImageUpload(file);
      toast.success('Image uploaded successfully');
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload image');
      setPreview(currentImage || null);
    }
  };

  const handleRemove = async () => {
    if (!onImageRemove) return;

    try {
      await onImageRemove();
      setPreview(null);
      toast.success('Image removed successfully');
    } catch (error) {
      console.error('Remove error:', error);
      toast.error('Failed to remove image');
    }
  };

  const { getRootProps, getInputProps, isDragActive: dropzoneActive } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        handleFileSelect(acceptedFiles[0]);
      }
    },
    accept: acceptedTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    multiple: false,
    disabled: disabled || loading,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
  });

  const handleClick = () => {
    if (!disabled && !loading) {
      fileInputRef.current?.click();
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${isDragActive || dropzoneActive
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }
          ${disabled || loading ? 'opacity-50 cursor-not-allowed' : ''}
          ${preview ? 'border-solid' : ''}
        `}
        onClick={preview ? undefined : handleClick}
      >
        <input
          {...getInputProps()}
          ref={fileInputRef}
          onChange={handleFileInputChange}
          className="hidden"
        />

        {preview ? (
          /* Image Preview */
          <div className="relative">
            <img
              src={preview}
              alt="Preview"
              className="max-w-full max-h-48 mx-auto rounded-lg object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-opacity rounded-lg flex items-center justify-center">
              <div className="opacity-0 hover:opacity-100 transition-opacity flex space-x-2">
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClick();
                  }}
                  disabled={disabled || loading}
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50"
                >
                  Change
                </button>
                {onImageRemove && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemove();
                    }}
                    disabled={disabled || loading}
                    className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50"
                  >
                    Remove
                  </button>
                )}
              </div>
            </div>
          </div>
        ) : (
          /* Upload Placeholder */
          <div className="space-y-2">
            <div className="mx-auto w-12 h-12 text-gray-400">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {isDragActive || dropzoneActive ? 'Drop image here' : placeholder}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                {acceptedTypes.map(type => type.split('/')[1]).join(', ').toUpperCase()} up to {(maxSize / (1024 * 1024)).toFixed(1)}MB
              </p>
            </div>
          </div>
        )}

        {loading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 dark:bg-gray-800 dark:bg-opacity-75 flex items-center justify-center rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">Uploading...</span>
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons (when image exists) */}
      {preview && (
        <div className="flex justify-center space-x-3">
          <LoadingButton
            loading={loading}
            onClick={handleClick}
            disabled={disabled}
            className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30"
          >
            Change Image
          </LoadingButton>
          {onImageRemove && (
            <LoadingButton
              loading={loading}
              onClick={handleRemove}
              disabled={disabled}
              className="px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 dark:bg-red-900/20 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/30"
            >
              Remove Image
            </LoadingButton>
          )}
        </div>
      )}
    </div>
  );
}

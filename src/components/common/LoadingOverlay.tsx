import React from 'react';
import LoadingSpinner, { ProgressBar } from './LoadingSpinner';
import { LoadingState } from '../../hooks/useLoading';

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  progress?: number;
  showProgress?: boolean;
  backdrop?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

/**
 * Loading overlay component that can be used to show loading states
 */
export default function LoadingOverlay({
  isVisible,
  message = 'Loading...',
  progress,
  showProgress = false,
  backdrop = true,
  size = 'md',
  className = '',
}: LoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div 
      className={`
        fixed inset-0 z-50 flex items-center justify-center
        ${backdrop ? 'bg-black bg-opacity-50' : ''}
        ${className}
      `}
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-sm w-full mx-4">
        <div className="text-center">
          <LoadingSpinner size={size} />
          
          {message && (
            <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
              {message}
            </p>
          )}
          
          {showProgress && typeof progress === 'number' && (
            <div className="mt-4">
              <ProgressBar progress={progress} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Inline loading overlay for specific containers
 */
export function InlineLoadingOverlay({
  isVisible,
  message,
  progress,
  showProgress = false,
  className = '',
}: Omit<LoadingOverlayProps, 'backdrop'>) {
  if (!isVisible) return null;

  return (
    <div 
      className={`
        absolute inset-0 z-10 flex items-center justify-center
        bg-white bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75
        ${className}
      `}
    >
      <div className="text-center p-4">
        <LoadingSpinner size="md" />
        
        {message && (
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {message}
          </p>
        )}
        
        {showProgress && typeof progress === 'number' && (
          <div className="mt-3 w-32">
            <ProgressBar progress={progress} />
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Loading state wrapper component
 */
interface LoadingWrapperProps {
  loading: LoadingState;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  overlay?: boolean;
  className?: string;
}

export function LoadingWrapper({
  loading,
  children,
  fallback,
  overlay = false,
  className = '',
}: LoadingWrapperProps) {
  if (loading.isLoading && !overlay) {
    return (
      <div className={className}>
        {fallback || (
          <div className="flex items-center justify-center p-8">
            <LoadingSpinner 
              size="lg" 
              message={loading.message} 
            />
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {children}
      
      {overlay && (
        <InlineLoadingOverlay
          isVisible={loading.isLoading}
          message={loading.message}
          progress={loading.progress}
          showProgress={typeof loading.progress === 'number'}
        />
      )}
    </div>
  );
}

/**
 * Button loading state component
 */
interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean;
  loadingText?: string;
  children: React.ReactNode;
}

export function LoadingButton({
  loading = false,
  loadingText = 'Loading...',
  disabled,
  children,
  className = '',
  ...props
}: LoadingButtonProps) {
  return (
    <button
      {...props}
      disabled={disabled || loading}
      className={`
        relative
        ${loading ? 'cursor-not-allowed' : ''}
        ${className}
      `}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="sm" color="white" />
          {loadingText && (
            <span className="ml-2 text-sm">
              {loadingText}
            </span>
          )}
        </div>
      )}
      
      <div className={loading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </div>
    </button>
  );
}

/**
 * Loading state for tables
 */
interface TableLoadingProps {
  isLoading: boolean;
  rows?: number;
  columns?: number;
  message?: string;
}

export function TableLoading({
  isLoading,
  rows = 5,
  columns = 4,
  message = 'Loading data...',
}: TableLoadingProps) {
  if (!isLoading) return null;

  return (
    <div className="space-y-4">
      {message && (
        <div className="text-center py-4">
          <LoadingSpinner size="md" message={message} />
        </div>
      )}
      
      <div className="animate-pulse">
        {/* Table header skeleton */}
        <div className="grid gap-4 mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-t-lg" 
             style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <div key={`header-${index}`} className="h-4 bg-gray-300 dark:bg-gray-600 rounded" />
          ))}
        </div>
        
        {/* Table rows skeleton */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div 
            key={`row-${rowIndex}`} 
            className="grid gap-4 p-4 border-b border-gray-200 dark:border-gray-700"
            style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
          >
            {Array.from({ length: columns }).map((_, colIndex) => (
              <div 
                key={`cell-${rowIndex}-${colIndex}`} 
                className="h-4 bg-gray-200 dark:bg-gray-700 rounded"
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Loading state for cards/grid layouts
 */
interface GridLoadingProps {
  isLoading: boolean;
  items?: number;
  columns?: number;
  message?: string;
}

export function GridLoading({
  isLoading,
  items = 6,
  columns = 3,
  message,
}: GridLoadingProps) {
  if (!isLoading) return null;

  return (
    <div className="space-y-4">
      {message && (
        <div className="text-center py-4">
          <LoadingSpinner size="md" message={message} />
        </div>
      )}
      
      <div className={`grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-${columns}`}>
        {Array.from({ length: items }).map((_, index) => (
          <div key={index} className="animate-pulse bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full" />
              <div className="flex-1">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2" />
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
              </div>
            </div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded" />
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6" />
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-4/6" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

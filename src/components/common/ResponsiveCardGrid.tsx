import React from 'react';
import { useScreenSize, responsiveGrids, getResponsiveClasses } from '../../utils/responsive';

interface ResponsiveCardGridProps {
  children: React.ReactNode;
  gridType?: keyof typeof responsiveGrids;
  customGrid?: Record<string, string>;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
  loading?: boolean;
  loadingCards?: number;
}

export default function ResponsiveCardGrid({
  children,
  gridType = 'entityCards',
  customGrid,
  gap = 'md',
  className = '',
  loading = false,
  loadingCards = 8,
}: ResponsiveCardGridProps) {
  const { isMobile, isTablet } = useScreenSize();

  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-4 md:gap-6',
    lg: 'gap-6 md:gap-8',
  };

  const gridClasses = customGrid 
    ? getResponsiveClasses(customGrid)
    : getResponsiveClasses(responsiveGrids[gridType]);

  if (loading) {
    return (
      <div className={`grid ${gridClasses} ${gapClasses[gap]} ${className}`}>
        {Array.from({ length: loadingCards }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-48"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={`grid ${gridClasses} ${gapClasses[gap]} ${className}`}>
      {children}
    </div>
  );
}

// Responsive Card Component
interface ResponsiveCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg';
}

export function ResponsiveCard({
  children,
  className = '',
  onClick,
  hover = true,
  padding = 'md',
}: ResponsiveCardProps) {
  const paddingClasses = {
    sm: 'p-3 md:p-4',
    md: 'p-4 md:p-6',
    lg: 'p-6 md:p-8',
  };

  return (
    <div
      className={`
        bg-white dark:bg-gray-800 
        rounded-lg shadow-sm 
        border border-gray-200 dark:border-gray-700
        ${paddingClasses[padding]}
        ${onClick ? 'cursor-pointer' : ''}
        ${hover ? 'hover:shadow-md transition-shadow duration-200' : ''}
        ${className}
      `}
      onClick={onClick}
    >
      {children}
    </div>
  );
}

// Responsive Stats Card
interface ResponsiveStatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string;
    trend: 'up' | 'down' | 'neutral';
  };
  icon?: React.ReactNode;
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  className?: string;
}

export function ResponsiveStatsCard({
  title,
  value,
  change,
  icon,
  color = 'blue',
  className = '',
}: ResponsiveStatsCardProps) {
  const colorClasses = {
    blue: 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
    green: 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400',
    yellow: 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400',
    red: 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400',
    purple: 'bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400',
    gray: 'bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400',
  };

  const trendClasses = {
    up: 'text-green-600 dark:text-green-400',
    down: 'text-red-600 dark:text-red-400',
    neutral: 'text-gray-600 dark:text-gray-400',
  };

  const trendIcons = {
    up: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
      </svg>
    ),
    down: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
      </svg>
    ),
    neutral: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
      </svg>
    ),
  };

  return (
    <ResponsiveCard className={className}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
            {title}
          </p>
          <p className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">
            {value}
          </p>
          {change && (
            <div className={`flex items-center mt-2 text-sm ${trendClasses[change.trend]}`}>
              {trendIcons[change.trend]}
              <span className="ml-1">{change.value}</span>
            </div>
          )}
        </div>
        {icon && (
          <div className={`p-3 rounded-full ${colorClasses[color]}`}>
            {icon}
          </div>
        )}
      </div>
    </ResponsiveCard>
  );
}

// Responsive Entity Card (for providers, customers, etc.)
interface ResponsiveEntityCardProps {
  title: string;
  subtitle?: string;
  status?: {
    label: string;
    color: 'green' | 'yellow' | 'red' | 'blue' | 'gray';
  };
  avatar?: string;
  metadata?: Array<{
    label: string;
    value: string;
  }>;
  actions?: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export function ResponsiveEntityCard({
  title,
  subtitle,
  status,
  avatar,
  metadata = [],
  actions,
  onClick,
  className = '',
}: ResponsiveEntityCardProps) {
  const statusColors = {
    green: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    yellow: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    red: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    blue: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    gray: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
  };

  return (
    <ResponsiveCard onClick={onClick} className={className}>
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {avatar && (
            <div className="flex-shrink-0">
              <img
                className="w-10 h-10 md:w-12 md:h-12 rounded-full object-cover"
                src={avatar}
                alt={title}
              />
            </div>
          )}
          <div className="flex-1 min-w-0">
            <h3 className="text-sm md:text-base font-medium text-gray-900 dark:text-white truncate">
              {title}
            </h3>
            {subtitle && (
              <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400 truncate">
                {subtitle}
              </p>
            )}
          </div>
        </div>
        {status && (
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[status.color]}`}>
            {status.label}
          </span>
        )}
      </div>

      {/* Metadata */}
      {metadata.length > 0 && (
        <div className="space-y-2 mb-4">
          {metadata.map((item, index) => (
            <div key={index} className="flex justify-between items-center text-sm">
              <span className="text-gray-600 dark:text-gray-400">{item.label}:</span>
              <span className="text-gray-900 dark:text-white font-medium">{item.value}</span>
            </div>
          ))}
        </div>
      )}

      {/* Actions */}
      {actions && (
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          {actions}
        </div>
      )}
    </ResponsiveCard>
  );
}

// Responsive Chart Card
interface ResponsiveChartCardProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  loading?: boolean;
  className?: string;
}

export function ResponsiveChartCard({
  title,
  subtitle,
  children,
  actions,
  loading = false,
  className = '',
}: ResponsiveChartCardProps) {
  return (
    <ResponsiveCard className={className} hover={false}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {title}
          </h3>
          {subtitle && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {subtitle}
            </p>
          )}
        </div>
        {actions && (
          <div className="mt-3 sm:mt-0">
            {actions}
          </div>
        )}
      </div>

      {/* Chart Content */}
      <div className="relative">
        {loading ? (
          <div className="animate-pulse">
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        ) : (
          children
        )}
      </div>
    </ResponsiveCard>
  );
}

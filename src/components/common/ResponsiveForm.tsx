import React from 'react';
import { useScreenSize, formResponsive, getResponsiveClasses } from '../../utils/responsive';

interface ResponsiveFormProps {
  children: React.ReactNode;
  onSubmit?: (e: React.FormEvent) => void;
  className?: string;
  layout?: 'vertical' | 'horizontal' | 'inline';
  columns?: 1 | 2 | 3;
}

export default function ResponsiveForm({
  children,
  onSubmit,
  className = '',
  layout = 'vertical',
  columns = 2,
}: ResponsiveFormProps) {
  const { isMobile } = useScreenSize();

  const getFormLayout = () => {
    if (layout === 'inline' && !isMobile) {
      return 'flex flex-wrap items-end gap-4';
    }
    
    if (layout === 'horizontal' && !isMobile) {
      return 'space-y-6';
    }

    // Vertical layout or mobile
    const gridCols = {
      1: { sm: 'grid-cols-1', md: 'grid-cols-1', lg: 'grid-cols-1' },
      2: { sm: 'grid-cols-1', md: 'grid-cols-2', lg: 'grid-cols-2' },
      3: { sm: 'grid-cols-1', md: 'grid-cols-2', lg: 'grid-cols-3' },
    };

    return `grid ${getResponsiveClasses(gridCols[columns])} gap-4 md:gap-6`;
  };

  return (
    <form onSubmit={onSubmit} className={`${formResponsive.container} ${className}`}>
      <div className={getFormLayout()}>
        {children}
      </div>
    </form>
  );
}

// Responsive Form Field
interface ResponsiveFormFieldProps {
  label: string;
  children: React.ReactNode;
  error?: string;
  required?: boolean;
  description?: string;
  className?: string;
  layout?: 'vertical' | 'horizontal';
  fullWidth?: boolean;
}

export function ResponsiveFormField({
  label,
  children,
  error,
  required = false,
  description,
  className = '',
  layout = 'vertical',
  fullWidth = false,
}: ResponsiveFormFieldProps) {
  const { isMobile } = useScreenSize();
  const isHorizontal = layout === 'horizontal' && !isMobile;

  return (
    <div className={`${fullWidth ? 'col-span-full' : ''} ${className}`}>
      <div className={isHorizontal ? 'flex items-start space-x-4' : 'space-y-2'}>
        <div className={isHorizontal ? 'w-1/3 pt-2' : ''}>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
          {description && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {description}
            </p>
          )}
        </div>
        <div className={isHorizontal ? 'flex-1' : ''}>
          {children}
          {error && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">
              {error}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

// Responsive Input
interface ResponsiveInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

export function ResponsiveInput({
  error = false,
  icon,
  iconPosition = 'left',
  className = '',
  ...props
}: ResponsiveInputProps) {
  const baseClasses = `
    w-full px-3 py-2 text-sm md:text-base
    border rounded-lg
    focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    dark:bg-gray-700 dark:text-white
    transition-colors
    ${error 
      ? 'border-red-300 dark:border-red-600' 
      : 'border-gray-300 dark:border-gray-600'
    }
    ${icon ? (iconPosition === 'left' ? 'pl-10' : 'pr-10') : ''}
  `;

  if (icon) {
    return (
      <div className="relative">
        {iconPosition === 'left' && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div className="text-gray-400">{icon}</div>
          </div>
        )}
        <input className={`${baseClasses} ${className}`} {...props} />
        {iconPosition === 'right' && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <div className="text-gray-400">{icon}</div>
          </div>
        )}
      </div>
    );
  }

  return <input className={`${baseClasses} ${className}`} {...props} />;
}

// Responsive Select
interface ResponsiveSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: boolean;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  placeholder?: string;
}

export function ResponsiveSelect({
  error = false,
  options,
  placeholder,
  className = '',
  ...props
}: ResponsiveSelectProps) {
  const baseClasses = `
    w-full px-3 py-2 text-sm md:text-base
    border rounded-lg
    focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    dark:bg-gray-700 dark:text-white
    transition-colors
    ${error 
      ? 'border-red-300 dark:border-red-600' 
      : 'border-gray-300 dark:border-gray-600'
    }
  `;

  return (
    <select className={`${baseClasses} ${className}`} {...props}>
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {options.map((option) => (
        <option key={option.value} value={option.value} disabled={option.disabled}>
          {option.label}
        </option>
      ))}
    </select>
  );
}

// Responsive Textarea
interface ResponsiveTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
  autoResize?: boolean;
}

export function ResponsiveTextarea({
  error = false,
  autoResize = false,
  className = '',
  ...props
}: ResponsiveTextareaProps) {
  const baseClasses = `
    w-full px-3 py-2 text-sm md:text-base
    border rounded-lg
    focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    dark:bg-gray-700 dark:text-white
    transition-colors
    ${error 
      ? 'border-red-300 dark:border-red-600' 
      : 'border-gray-300 dark:border-gray-600'
    }
    ${autoResize ? 'resize-none' : 'resize-y'}
  `;

  const handleInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
    if (autoResize) {
      const target = e.target as HTMLTextAreaElement;
      target.style.height = 'auto';
      target.style.height = `${target.scrollHeight}px`;
    }
  };

  return (
    <textarea
      className={`${baseClasses} ${className}`}
      onInput={autoResize ? handleInput : undefined}
      {...props}
    />
  );
}

// Responsive Button Group
interface ResponsiveButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical' | 'responsive';
  align?: 'left' | 'center' | 'right';
}

export function ResponsiveButtonGroup({
  children,
  className = '',
  orientation = 'responsive',
  align = 'right',
}: ResponsiveButtonGroupProps) {
  const { isMobile } = useScreenSize();

  const getOrientation = () => {
    if (orientation === 'responsive') {
      return isMobile ? 'flex-col' : 'flex-row';
    }
    return orientation === 'vertical' ? 'flex-col' : 'flex-row';
  };

  const getAlignment = () => {
    if (align === 'center') return 'justify-center';
    if (align === 'left') return 'justify-start';
    return 'justify-end';
  };

  return (
    <div className={`flex ${getOrientation()} ${getAlignment()} gap-3 ${className}`}>
      {children}
    </div>
  );
}

// Responsive Button
interface ResponsiveButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

export function ResponsiveButton({
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  children,
  className = '',
  disabled,
  ...props
}: ResponsiveButtonProps) {
  const baseClasses = `
    inline-flex items-center justify-center
    font-medium rounded-lg
    transition-colors duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${fullWidth ? 'w-full' : ''}
  `;

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm md:text-base',
    lg: 'px-6 py-3 text-base md:text-lg',
  };

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-blue-500 dark:text-gray-300 dark:hover:bg-gray-700',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
  };

  return (
    <button
      className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {!loading && icon && iconPosition === 'left' && (
        <span className="mr-2">{icon}</span>
      )}
      {children}
      {!loading && icon && iconPosition === 'right' && (
        <span className="ml-2">{icon}</span>
      )}
    </button>
  );
}

import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { getBreadcrumbTrail } from '../../config/navigation';

interface BreadcrumbProps {
  className?: string;
  showHome?: boolean;
  separator?: React.ReactNode;
}

export default function Breadcrumb({ 
  className = '', 
  showHome = true,
  separator 
}: BreadcrumbProps) {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbTrail(location.pathname);

  const defaultSeparator = (
    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
  );

  const separatorElement = separator || defaultSeparator;

  if (breadcrumbs.length === 0 && !showHome) {
    return null;
  }

  return (
    <nav className={`flex items-center space-x-2 text-sm ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {showHome && (
          <>
            <li>
              <Link
                to="/"
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span className="sr-only">Home</span>
              </Link>
            </li>
            {breadcrumbs.length > 0 && (
              <li className="flex items-center">
                {separatorElement}
              </li>
            )}
          </>
        )}
        
        {breadcrumbs.map((breadcrumb, index) => (
          <React.Fragment key={breadcrumb.path}>
            <li>
              {index === breadcrumbs.length - 1 ? (
                <span className="text-gray-900 dark:text-white font-medium">
                  {breadcrumb.name}
                </span>
              ) : (
                <Link
                  to={breadcrumb.path}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                >
                  {breadcrumb.name}
                </Link>
              )}
            </li>
            {index < breadcrumbs.length - 1 && (
              <li className="flex items-center">
                {separatorElement}
              </li>
            )}
          </React.Fragment>
        ))}
      </ol>
    </nav>
  );
}

// Breadcrumb with custom styling for admin pages
export function AdminBreadcrumb({ className = '' }: { className?: string }) {
  return (
    <Breadcrumb
      className={`bg-white dark:bg-gray-800 px-6 py-3 border-b border-gray-200 dark:border-gray-700 ${className}`}
      showHome={true}
      separator={
        <svg className="w-3 h-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      }
    />
  );
}

// Compact breadcrumb for mobile
export function CompactBreadcrumb({ className = '' }: { className?: string }) {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbTrail(location.pathname);
  
  if (breadcrumbs.length === 0) {
    return null;
  }

  const currentPage = breadcrumbs[breadcrumbs.length - 1];
  const parentPage = breadcrumbs.length > 1 ? breadcrumbs[breadcrumbs.length - 2] : null;

  return (
    <nav className={`flex items-center space-x-2 text-sm ${className}`} aria-label="Breadcrumb">
      {parentPage && (
        <>
          <Link
            to={parentPage.path}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          >
            <svg className="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            {parentPage.name}
          </Link>
          <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </>
      )}
      <span className="text-gray-900 dark:text-white font-medium">
        {currentPage.name}
      </span>
    </nav>
  );
}

// Breadcrumb with page actions
interface BreadcrumbWithActionsProps {
  className?: string;
  actions?: React.ReactNode;
}

export function BreadcrumbWithActions({ className = '', actions }: BreadcrumbWithActionsProps) {
  return (
    <div className={`flex items-center justify-between bg-white dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700 ${className}`}>
      <Breadcrumb />
      {actions && (
        <div className="flex items-center space-x-3">
          {actions}
        </div>
      )}
    </div>
  );
}

// Breadcrumb with page title
interface BreadcrumbWithTitleProps {
  className?: string;
  title?: string;
  subtitle?: string;
}

export function BreadcrumbWithTitle({ 
  className = '', 
  title,
  subtitle 
}: BreadcrumbWithTitleProps) {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbTrail(location.pathname);
  const currentPage = breadcrumbs[breadcrumbs.length - 1];
  
  const pageTitle = title || currentPage?.name || 'Page';

  return (
    <div className={`bg-white dark:bg-gray-800 px-6 py-6 border-b border-gray-200 dark:border-gray-700 ${className}`}>
      <Breadcrumb className="mb-4" />
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          {pageTitle}
        </h1>
        {subtitle && (
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {subtitle}
          </p>
        )}
      </div>
    </div>
  );
}

// Breadcrumb with stats
interface BreadcrumbWithStatsProps {
  className?: string;
  stats?: Array<{
    label: string;
    value: string | number;
    change?: string;
    trend?: 'up' | 'down' | 'neutral';
  }>;
}

export function BreadcrumbWithStats({ className = '', stats = [] }: BreadcrumbWithStatsProps) {
  return (
    <div className={`bg-white dark:bg-gray-800 px-6 py-6 border-b border-gray-200 dark:border-gray-700 ${className}`}>
      <Breadcrumb className="mb-6" />
      
      {stats.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.label}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                </div>
                {stat.change && (
                  <div className={`flex items-center text-sm ${
                    stat.trend === 'up' ? 'text-green-600' :
                    stat.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {stat.trend === 'up' && (
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                      </svg>
                    )}
                    {stat.trend === 'down' && (
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
                      </svg>
                    )}
                    {stat.change}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

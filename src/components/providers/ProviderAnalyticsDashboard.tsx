import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';

interface ProviderAnalytics {
  overview: {
    totalProviders: number;
    activeProviders: number;
    pendingApprovals: number;
    averageRating: number;
    totalServices: number;
    totalBookings: number;
    totalRevenue: number;
    growthRate: number;
  };
  categoryDistribution: {
    categoryId: string;
    categoryName: string;
    providerCount: number;
    percentage: number;
    averageRating: number;
    totalBookings: number;
  }[];
  performanceMetrics: {
    topPerformers: {
      id: string;
      name: string;
      rating: number;
      bookings: number;
      revenue: number;
      responseTime: string;
    }[];
    lowPerformers: {
      id: string;
      name: string;
      rating: number;
      bookings: number;
      issues: string[];
    }[];
  };
  trends: {
    registrations: { date: string; count: number }[];
    approvals: { date: string; count: number }[];
    ratings: { date: string; average: number }[];
  };
}

interface ProviderAnalyticsDashboardProps {
  className?: string;
}

export default function ProviderAnalyticsDashboard({ className = '' }: ProviderAnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<ProviderAnalytics | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');
  const loading = useLoading();

  useEffect(() => {
    fetchAnalytics();
  }, [selectedPeriod]);

  const fetchAnalytics = async () => {
    try {
      loading.startLoading({ message: 'Loading provider analytics...' });
      const response = await adminApi.providers.getAnalytics(selectedPeriod);
      
      if (response.success) {
        setAnalytics(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch analytics');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_provider_analytics' });
      // Set fallback data for development
      setAnalytics({
        overview: {
          totalProviders: 1234,
          activeProviders: 987,
          pendingApprovals: 45,
          averageRating: 4.6,
          totalServices: 3456,
          totalBookings: 12890,
          totalRevenue: 245670,
          growthRate: 12.5,
        },
        categoryDistribution: [
          { categoryId: '1', categoryName: 'Beauty & Wellness', providerCount: 345, percentage: 28, averageRating: 4.7, totalBookings: 4567 },
          { categoryId: '2', categoryName: 'Healthcare', providerCount: 289, percentage: 23, averageRating: 4.8, totalBookings: 3890 },
          { categoryId: '3', categoryName: 'Fitness', providerCount: 234, percentage: 19, averageRating: 4.5, totalBookings: 2345 },
          { categoryId: '4', categoryName: 'Automotive', providerCount: 198, percentage: 16, averageRating: 4.4, totalBookings: 1567 },
          { categoryId: '5', categoryName: 'Home Services', providerCount: 168, percentage: 14, averageRating: 4.6, totalBookings: 521 },
        ],
        performanceMetrics: {
          topPerformers: [
            { id: '1', name: 'Elite Beauty Salon', rating: 4.9, bookings: 567, revenue: 23450, responseTime: '15 min' },
            { id: '2', name: 'Premium Health Clinic', rating: 4.8, bookings: 445, revenue: 34560, responseTime: '12 min' },
            { id: '3', name: 'FitZone Gym', rating: 4.7, bookings: 389, revenue: 18900, responseTime: '8 min' },
            { id: '4', name: 'AutoCare Pro', rating: 4.6, bookings: 234, revenue: 15670, responseTime: '20 min' },
            { id: '5', name: 'Home Fix Masters', rating: 4.5, bookings: 178, revenue: 12340, responseTime: '25 min' },
          ],
          lowPerformers: [
            { id: '6', name: 'Basic Cuts', rating: 3.2, bookings: 23, issues: ['Slow response', 'Quality complaints'] },
            { id: '7', name: 'Quick Fix Auto', rating: 3.0, bookings: 15, issues: ['Poor communication', 'Delays'] },
            { id: '8', name: 'Budget Fitness', rating: 2.8, bookings: 12, issues: ['Equipment issues', 'Staff problems'] },
          ],
        },
        trends: {
          registrations: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            count: Math.floor(Math.random() * 10) + 2,
          })),
          approvals: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            count: Math.floor(Math.random() * 8) + 1,
          })),
          ratings: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            average: 4.0 + Math.random() * 1.0,
          })),
        },
      });
    } finally {
      loading.stopLoading();
    }
  };

  const periods = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 3 Months' },
    { value: '1y', label: 'Last Year' },
  ];

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'categories', name: 'Categories', icon: '🏷️' },
    { id: 'performance', name: 'Performance', icon: '🏆' },
    { id: 'trends', name: 'Trends', icon: '📈' },
  ];

  if (loading.isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            Unable to load provider analytics
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Provider Analytics
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Comprehensive insights into provider performance and platform metrics
            </p>
          </div>
          
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            {periods.map((period) => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }
              `}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <OverviewTab analytics={analytics} />
        )}
        {activeTab === 'categories' && (
          <CategoriesTab categories={analytics.categoryDistribution} />
        )}
        {activeTab === 'performance' && (
          <PerformanceTab metrics={analytics.performanceMetrics} />
        )}
        {activeTab === 'trends' && (
          <TrendsTab trends={analytics.trends} />
        )}
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ analytics }: { analytics: ProviderAnalytics }) {
  const { overview } = analytics;
  
  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Providers</p>
              <p className="text-2xl font-semibold text-blue-900 dark:text-blue-100">{overview.totalProviders.toLocaleString()}</p>
              <p className="text-xs text-blue-700 dark:text-blue-300">+{overview.growthRate}% growth</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
              <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-green-600 dark:text-green-400">Active Providers</p>
              <p className="text-2xl font-semibold text-green-900 dark:text-green-100">{overview.activeProviders.toLocaleString()}</p>
              <p className="text-xs text-green-700 dark:text-green-300">{((overview.activeProviders / overview.totalProviders) * 100).toFixed(1)}% of total</p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
              <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Pending Approvals</p>
              <p className="text-2xl font-semibold text-yellow-900 dark:text-yellow-100">{overview.pendingApprovals}</p>
              <p className="text-xs text-yellow-700 dark:text-yellow-300">Awaiting review</p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
              <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Average Rating</p>
              <p className="text-2xl font-semibold text-purple-900 dark:text-purple-100">{overview.averageRating.toFixed(1)}</p>
              <p className="text-xs text-purple-700 dark:text-purple-300">Platform average</p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Service Metrics</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Services</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{overview.totalServices.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Bookings</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{overview.totalBookings.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Avg. Services per Provider</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{(overview.totalServices / overview.totalProviders).toFixed(1)}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Revenue Metrics</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Revenue</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">${overview.totalRevenue.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Avg. per Provider</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">${(overview.totalRevenue / overview.activeProviders).toFixed(0)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Avg. per Booking</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">${(overview.totalRevenue / overview.totalBookings).toFixed(0)}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Platform Health</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Approval Rate</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{(((overview.totalProviders - overview.pendingApprovals) / overview.totalProviders) * 100).toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Activity Rate</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{((overview.activeProviders / overview.totalProviders) * 100).toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Growth Rate</span>
              <span className="text-sm font-medium text-green-600 dark:text-green-400">+{overview.growthRate}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Categories Tab Component
function CategoriesTab({ categories }: { categories: ProviderAnalytics['categoryDistribution'] }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Category Distribution Chart */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Provider Distribution by Category
          </h3>
          <div className="space-y-4">
            {categories.map((category, index) => (
              <div key={category.categoryId} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">{category.categoryName}</span>
                  <span className="text-gray-600 dark:text-gray-400">{category.providerCount} providers ({category.percentage}%)</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      index === 0 ? 'bg-blue-500' :
                      index === 1 ? 'bg-green-500' :
                      index === 2 ? 'bg-yellow-500' :
                      index === 3 ? 'bg-purple-500' : 'bg-pink-500'
                    }`}
                    style={{ width: `${category.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Category Performance */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Category Performance
          </h3>
          <div className="space-y-4">
            {categories.map((category) => (
              <div key={category.categoryId} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">{category.categoryName}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{category.providerCount} providers</p>
                </div>
                <div className="text-right">
                  <div className="flex items-center">
                    <span className="text-yellow-500 mr-1">⭐</span>
                    <span className="font-medium text-gray-900 dark:text-white">{category.averageRating.toFixed(1)}</span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{category.totalBookings} bookings</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Performance Tab Component
function PerformanceTab({ metrics }: { metrics: ProviderAnalytics['performanceMetrics'] }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performers */}
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
          <h3 className="text-lg font-medium text-green-900 dark:text-green-100 mb-4 flex items-center">
            <span className="mr-2">🏆</span>
            Top Performing Providers
          </h3>
          <div className="space-y-4">
            {metrics.topPerformers.map((provider, index) => (
              <div key={provider.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold mr-3 ${
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-400' :
                    index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                  }`}>
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">{provider.name}</p>
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <span className="text-yellow-500 mr-1">⭐</span>
                      <span className="mr-3">{provider.rating}</span>
                      <span className="mr-3">{provider.bookings} bookings</span>
                      <span>⏱️ {provider.responseTime}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-green-600 dark:text-green-400">
                    ${provider.revenue.toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">revenue</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Low Performers */}
        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-6">
          <h3 className="text-lg font-medium text-red-900 dark:text-red-100 mb-4 flex items-center">
            <span className="mr-2">⚠️</span>
            Providers Needing Attention
          </h3>
          <div className="space-y-4">
            {metrics.lowPerformers.map((provider) => (
              <div key={provider.id} className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="font-medium text-gray-900 dark:text-white">{provider.name}</p>
                  <div className="flex items-center">
                    <span className="text-yellow-500 mr-1">⭐</span>
                    <span className="text-sm text-gray-900 dark:text-white">{provider.rating}</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {provider.bookings} bookings
                </p>
                <div className="flex flex-wrap gap-1">
                  {provider.issues.map((issue, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                    >
                      {issue}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Trends Tab Component
function TrendsTab({ trends }: { trends: ProviderAnalytics['trends'] }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Registration Trends */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-4">
            📈 Registration Trends
          </h3>
          <div className="space-y-3">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {trends.registrations.reduce((sum, day) => sum + day.count, 0)}
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300">Total Registrations</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                {(trends.registrations.reduce((sum, day) => sum + day.count, 0) / trends.registrations.length).toFixed(1)}
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300">Daily Average</p>
            </div>
          </div>
        </div>

        {/* Approval Trends */}
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
          <h3 className="text-lg font-medium text-green-900 dark:text-green-100 mb-4">
            ✅ Approval Trends
          </h3>
          <div className="space-y-3">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {trends.approvals.reduce((sum, day) => sum + day.count, 0)}
              </p>
              <p className="text-sm text-green-700 dark:text-green-300">Total Approvals</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                {(trends.approvals.reduce((sum, day) => sum + day.count, 0) / trends.approvals.length).toFixed(1)}
              </p>
              <p className="text-sm text-green-700 dark:text-green-300">Daily Average</p>
            </div>
          </div>
        </div>

        {/* Rating Trends */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
          <h3 className="text-lg font-medium text-yellow-900 dark:text-yellow-100 mb-4">
            ⭐ Rating Trends
          </h3>
          <div className="space-y-3">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                {(trends.ratings.reduce((sum, day) => sum + day.average, 0) / trends.ratings.length).toFixed(1)}
              </p>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">Average Rating</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-yellow-600 dark:text-yellow-400">
                {Math.max(...trends.ratings.map(r => r.average)).toFixed(1)}
              </p>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">Peak Rating</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

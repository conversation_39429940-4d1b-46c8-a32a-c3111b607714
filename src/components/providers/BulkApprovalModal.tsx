import React, { useState } from 'react';
import { Provider } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import toast from 'react-hot-toast';

interface BulkApprovalModalProps {
  providers: Provider[];
  action: 'approve' | 'reject';
  onClose: () => void;
  onComplete: (updatedProviders: Provider[]) => void;
}

export default function BulkApprovalModal({
  providers,
  action,
  onClose,
  onComplete,
}: BulkApprovalModalProps) {
  const [note, setNote] = useState('');
  const [processing, setProcessing] = useState(false);
  const [results, setResults] = useState<{ success: number; failed: number; errors: string[] }>({
    success: 0,
    failed: 0,
    errors: [],
  });
  const loading = useLoading();

  const handleBulkAction = async () => {
    if (!note.trim()) {
      toast.error('Please provide a note for the bulk action');
      return;
    }

    try {
      setProcessing(true);
      loading.startLoading({ message: `${action === 'approve' ? 'Approving' : 'Rejecting'} ${providers.length} providers...` });

      const updatedProviders: Provider[] = [];
      const errors: string[] = [];
      let successCount = 0;
      let failedCount = 0;

      // Process providers in batches to avoid overwhelming the API
      const batchSize = 5;
      for (let i = 0; i < providers.length; i += batchSize) {
        const batch = providers.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (provider) => {
          try {
            const response = action === 'approve'
              ? await adminApi.providers.approveProvider(provider.id, { reason: note.trim() })
              : await adminApi.providers.rejectProvider(provider.id, { reason: note.trim() });

            if (response.success) {
              const updatedProvider = {
                ...provider,
                status: action === 'approve' ? 'approved' as const : 'rejected' as const,
                verified: action === 'approve',
              };
              updatedProviders.push(updatedProvider);
              successCount++;
            } else {
              throw new Error(response.message || `Failed to ${action} provider`);
            }
          } catch (error) {
            failedCount++;
            errors.push(`${provider.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        });

        await Promise.all(batchPromises);
        
        // Update progress
        setResults({
          success: successCount,
          failed: failedCount,
          errors,
        });
      }

      // Show final results
      if (successCount > 0) {
        toast.success(`Successfully ${action === 'approve' ? 'approved' : 'rejected'} ${successCount} provider(s)`);
      }
      
      if (failedCount > 0) {
        toast.error(`Failed to process ${failedCount} provider(s)`);
      }

      onComplete(updatedProviders);
      
      // Close modal after a short delay if all succeeded
      if (failedCount === 0) {
        setTimeout(() => {
          onClose();
        }, 2000);
      }
    } catch (error) {
      handleError(error, { action: `bulk_${action}_providers` });
    } finally {
      loading.stopLoading();
      setProcessing(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Bulk {action === 'approve' ? 'Approval' : 'Rejection'}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {action === 'approve' ? 'Approve' : 'Reject'} {providers.length} selected provider(s)
            </p>
          </div>
          <button
            onClick={onClose}
            disabled={processing}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6">
          {/* Provider List */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Selected Providers ({providers.length})
            </h3>
            <div className="max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg">
              <div className="divide-y divide-gray-200 dark:divide-gray-600">
                {providers.map((provider) => (
                  <div key={provider.id} className="px-3 py-2 flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {provider.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {provider.email}
                      </p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      provider.status === 'pending' 
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        : provider.status === 'approved'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {provider.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Note Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {action === 'approve' ? 'Approval Note' : 'Rejection Reason'}
            </label>
            <textarea
              value={note}
              onChange={(e) => setNote(e.target.value)}
              rows={3}
              disabled={processing}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white disabled:opacity-50"
              placeholder={
                action === 'approve' 
                  ? 'Provide a note for the bulk approval...'
                  : 'Explain the reason for bulk rejection...'
              }
            />
          </div>

          {/* Progress/Results */}
          {processing && (
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="flex items-center mb-2">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Processing providers...
                </span>
              </div>
              <div className="text-sm text-blue-700 dark:text-blue-200">
                Success: {results.success} | Failed: {results.failed}
              </div>
            </div>
          )}

          {/* Error Results */}
          {results.errors.length > 0 && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <h4 className="text-sm font-medium text-red-900 dark:text-red-100 mb-2">
                Errors ({results.errors.length})
              </h4>
              <div className="max-h-32 overflow-y-auto">
                <ul className="text-sm text-red-700 dark:text-red-200 space-y-1">
                  {results.errors.map((error, index) => (
                    <li key={index} className="text-xs">
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Warning */}
          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <div className="flex">
              <svg className="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div>
                <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Bulk Action Warning
                </h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                  This action will {action} {providers.length} provider(s) and cannot be easily undone. 
                  Please ensure you have reviewed all providers carefully.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={processing}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
          >
            {processing ? 'Processing...' : 'Cancel'}
          </button>
          <button
            onClick={handleBulkAction}
            disabled={processing || !note.trim()}
            className={`px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 ${
              action === 'approve' 
                ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500' 
                : 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {processing 
              ? `Processing ${results.success + results.failed}/${providers.length}...`
              : `${action === 'approve' ? 'Approve' : 'Reject'} ${providers.length} Provider(s)`
            }
          </button>
        </div>
      </div>
    </div>
  );
}

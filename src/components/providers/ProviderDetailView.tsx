import React, { useState, useEffect } from 'react';
import { Provider, ProviderStats, ProviderServiceDetail, ProviderLocationDetail } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { formatDistanceToNow } from 'date-fns';
import toast from 'react-hot-toast';

interface ProviderDetailViewProps {
  providerId: string;
  onClose: () => void;
  onProviderUpdate?: (provider: Provider) => void;
}

// Remove local interface since we're importing from types

interface Service {
  id: string;
  name: string;
  description: string;
  duration: number;
  price: number;
  category: string;
  isActive: boolean;
}

interface Location {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
  phone?: string;
  isMain: boolean;
}

export default function ProviderDetailView({
  providerId,
  onClose,
  onProviderUpdate
}: ProviderDetailViewProps) {
  const [provider, setProvider] = useState<Provider | null>(null);
  const [stats, setStats] = useState<ProviderStats | null>(null);
  const [services, setServices] = useState<ProviderServiceDetail[]>([]);
  const [locations, setLocations] = useState<ProviderLocationDetail[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const loading = useLoading();

  useEffect(() => {
    if (providerId) {
      fetchProviderDetails();
    }
  }, [providerId]);

  const fetchProviderDetails = async () => {
    try {
      loading.startLoading({ message: 'Loading provider details...' });

      const [providerResponse, statsResponse, servicesResponse, locationsResponse] = await Promise.all([
        adminApi.providers.getProvider(providerId),
        adminApi.providers.getProviderStats(providerId),
        adminApi.providers.getProviderServices(providerId),
        adminApi.providers.getProviderLocations(providerId),
      ]);

      if (providerResponse.success) {
        setProvider(providerResponse.data);
      }

      if (statsResponse.success) {
        setStats(statsResponse.data);
      } else {
        // Fallback stats for development matching API structure
        setStats({
          overview: {
            totalAppointments: 156,
            monthlyAppointments: 45,
            weeklyAppointments: 12,
            todayAppointments: 3,
            totalCustomers: 89,
            totalServices: 8,
            totalLocations: 2,
            totalQueues: 3,
            averageRating: 4.7,
            totalReviews: 89,
            isVerified: true,
            isSetupComplete: true,
            memberSince: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
          },
          appointments: {
            byStatus: {
              completed: 142,
              confirmed: 14,
            },
            recent: [],
          },
          revenue: {
            monthlyTotal: 12450,
            currency: 'DZD',
          },
        });
      }

      if (servicesResponse.success) {
        setServices(servicesResponse.data);
      } else {
        // Fallback services for development
        setServices([
          {
            id: '1',
            name: 'Hair Cut & Styling',
            description: 'Professional hair cutting and styling service',
            duration: 60,
            price: 45,
            category: 'Hair Services',
            isActive: true,
          },
          {
            id: '2',
            name: 'Hair Coloring',
            description: 'Professional hair coloring and highlights',
            duration: 120,
            price: 85,
            category: 'Hair Services',
            isActive: true,
          },
          {
            id: '3',
            name: 'Beard Trim',
            description: 'Professional beard trimming and shaping',
            duration: 30,
            price: 25,
            category: 'Grooming',
            isActive: false,
          },
        ]);
      }

      if (locationsResponse.success) {
        setLocations(locationsResponse.data);
      } else {
        // Fallback locations for development
        setLocations([
          {
            id: '1',
            name: 'Main Location',
            address: '123 Main Street',
            city: 'Algiers',
            country: 'Algeria',
            phone: '+213 21 123 456',
            isMain: true,
          },
          {
            id: '2',
            name: 'Branch Location',
            address: '456 Secondary Ave',
            city: 'Oran',
            country: 'Algeria',
            phone: '+213 41 789 012',
            isMain: false,
          },
        ]);
      }
    } catch (error) {
      handleError(error, { action: 'fetch_provider_details' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleApproveProvider = async () => {
    if (!provider) return;

    try {
      loading.startLoading({ message: 'Approving provider...' });
      const response = await adminApi.providers.approveProvider(provider.id, { reason: 'Quick approval from detail view' });

      if (response.success) {
        const updatedProvider = { ...provider, status: 'approved' as const, verified: true };
        setProvider(updatedProvider);
        onProviderUpdate?.(updatedProvider);
        toast.success('Provider approved successfully');
      } else {
        throw new Error(response.message || 'Failed to approve provider');
      }
    } catch (error) {
      handleError(error, { action: 'approve_provider' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleRejectProvider = async () => {
    if (!provider) return;

    try {
      loading.startLoading({ message: 'Rejecting provider...' });
      const response = await adminApi.providers.rejectProvider(provider.id);

      if (response.success) {
        const updatedProvider = { ...provider, status: 'rejected' as const };
        setProvider(updatedProvider);
        onProviderUpdate?.(updatedProvider);
        toast.success('Provider rejected');
      } else {
        throw new Error(response.message || 'Failed to reject provider');
      }
    } catch (error) {
      handleError(error, { action: 'reject_provider' });
    } finally {
      loading.stopLoading();
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Approved
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
            Pending
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            Rejected
          </span>
        );
      case 'suspended':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clipRule="evenodd" />
            </svg>
            Suspended
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            {status}
          </span>
        );
    }
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'services', name: 'Services', icon: '🛠️' },
    { id: 'locations', name: 'Locations', icon: '📍' },
    { id: 'reviews', name: 'Reviews', icon: '⭐' },
    { id: 'bookings', name: 'Bookings', icon: '📅' },
    { id: 'analytics', name: 'Analytics', icon: '📈' },
  ];

  if (loading.isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-5/6 overflow-hidden">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            </div>
          </div>
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!provider) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6">
          <p className="text-gray-500 dark:text-gray-400">Provider not found</p>
          <button
            onClick={onClose}
            className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-5/6 overflow-hidden flex flex-col">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <span className="text-lg font-medium text-gray-700 dark:text-gray-300">
                {provider.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {provider.name}
              </h2>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusBadge(provider.status)}
                {provider.verified && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Verified
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {provider.status === 'pending' && (
              <>
                <button
                  onClick={handleRejectProvider}
                  className="px-4 py-2 text-sm font-medium text-red-700 bg-red-100 border border-red-300 rounded-lg hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Reject
                </button>
                <button
                  onClick={handleApproveProvider}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  Approve
                </button>
              </>
            )}

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }
                `}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'overview' && (
            <OverviewTab provider={provider} stats={stats} />
          )}
          {activeTab === 'services' && (
            <ServicesTab services={services} />
          )}
          {activeTab === 'locations' && (
            <LocationsTab locations={locations} />
          )}
          {activeTab === 'reviews' && (
            <ReviewsTab providerId={provider.id} />
          )}
          {activeTab === 'bookings' && (
            <BookingsTab providerId={provider.id} />
          )}
          {activeTab === 'analytics' && (
            <AnalyticsTab providerId={provider.id} stats={stats} />
          )}
        </div>
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ provider, stats }: { provider: Provider; stats: ProviderStats | null }) {
  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Provider Information
          </h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Business Name</label>
              <p className="text-sm text-gray-900 dark:text-white">{provider.businessTitle || provider.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
              <p className="text-sm text-gray-900 dark:text-white">{provider.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
              <p className="text-sm text-gray-900 dark:text-white">{provider.phone || 'Not provided'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Category</label>
              <p className="text-sm text-gray-900 dark:text-white">{provider.category?.name || 'Uncategorized'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Member Since</label>
              <p className="text-sm text-gray-900 dark:text-white">
                {formatDistanceToNow(new Date(provider.createdAt), { addSuffix: true })}
              </p>
            </div>
          </div>
        </div>

        {/* Performance Stats */}
        {stats && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Performance Overview
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-2xl font-semibold text-blue-600 dark:text-blue-400">
                  {stats.overview.totalAppointments}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Total Appointments</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-green-600 dark:text-green-400">
                  {Math.round((stats.appointments.byStatus.completed / stats.overview.totalAppointments) * 100) || 0}%
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Completion Rate</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-yellow-600 dark:text-yellow-400">
                  {stats.overview.averageRating || 'N/A'}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Average Rating</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-purple-600 dark:text-purple-400">
                  {stats.revenue.monthlyTotal.toLocaleString()} {stats.revenue.currency}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Monthly Revenue</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Description */}
      {provider.description && (
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            About
          </h3>
          <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
            {provider.description}
          </p>
        </div>
      )}
    </div>
  );
}

// Services Tab Component
function ServicesTab({ services }: { services: Service[] }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Services ({services.length})
        </h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {services.map((service) => (
          <div key={service.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-start justify-between mb-2">
              <h4 className="font-medium text-gray-900 dark:text-white">
                {service.name}
              </h4>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                service.isActive
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
              }`}>
                {service.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              {service.description}
            </p>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-500 dark:text-gray-400">
                {service.duration} min
              </span>
              <span className="font-medium text-gray-900 dark:text-white">
                ${service.price}
              </span>
            </div>
          </div>
        ))}
      </div>

      {services.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">No services available</p>
        </div>
      )}
    </div>
  );
}

// Locations Tab Component
function LocationsTab({ locations }: { locations: Location[] }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Locations ({locations.length})
        </h3>
      </div>

      <div className="space-y-4">
        {locations.map((location) => (
          <div key={location.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-start justify-between mb-2">
              <h4 className="font-medium text-gray-900 dark:text-white">
                {location.name}
              </h4>
              {location.isMain && (
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  Main Location
                </span>
              )}
            </div>
            <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <p>{location.address}</p>
              <p>{location.city}, {location.country}</p>
              {location.phone && <p>📞 {location.phone}</p>}
            </div>
          </div>
        ))}
      </div>

      {locations.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">No locations available</p>
        </div>
      )}
    </div>
  );
}

// Reviews Tab Component
function ReviewsTab({ providerId }: { providerId: string }) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
        Customer Reviews
      </h3>
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">Reviews feature coming soon</p>
      </div>
    </div>
  );
}

// Bookings Tab Component
function BookingsTab({ providerId }: { providerId: string }) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
        Booking History
      </h3>
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">Booking history feature coming soon</p>
      </div>
    </div>
  );
}

// Analytics Tab Component
function AnalyticsTab({ providerId, stats }: { providerId: string; stats: ProviderStats | null }) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
        Provider Analytics
      </h3>
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">Analytics dashboard coming soon</p>
      </div>
    </div>
  );
}
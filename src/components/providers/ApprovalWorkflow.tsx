import React, { useState } from 'react';
import { Provider } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import toast from 'react-hot-toast';

interface ApprovalWorkflowProps {
  provider: Provider;
  onClose: () => void;
  onProviderUpdate: (provider: Provider) => void;
}

interface ApprovalNote {
  id: string;
  message: string;
  type: 'approval' | 'rejection' | 'request_info';
  createdAt: string;
  adminName: string;
}

export default function ApprovalWorkflow({ 
  provider, 
  onClose, 
  onProviderUpdate 
}: ApprovalWorkflowProps) {
  const [action, setAction] = useState<'approve' | 'reject' | 'request_info'>('approve');
  const [note, setNote] = useState('');
  const [notes, setNotes] = useState<ApprovalNote[]>([
    {
      id: '1',
      message: 'Provider registration submitted for review',
      type: 'request_info',
      createdAt: provider.createdAt,
      adminName: 'System',
    },
  ]);
  const [requiresDocuments, setRequiresDocuments] = useState(false);
  const [documentTypes, setDocumentTypes] = useState<string[]>([]);
  const loading = useLoading();

  const handleSubmitDecision = async () => {
    if (!note.trim()) {
      toast.error('Please provide a note for your decision');
      return;
    }

    try {
      loading.startLoading({ message: `${action === 'approve' ? 'Approving' : action === 'reject' ? 'Rejecting' : 'Requesting information from'} provider...` });
      
      let response;
      const requestData = {
        reason: note.trim(),
        requiresDocuments,
        documentTypes: requiresDocuments ? documentTypes : undefined,
      };

      switch (action) {
        case 'approve':
          response = await adminApi.providers.approveProvider(provider.id, { reason: requestData.reason });
          break;
        case 'reject':
          response = await adminApi.providers.rejectProvider(provider.id, { reason: requestData.reason });
          break;
        case 'request_info':
          response = await adminApi.providers.requestProviderInfo(provider.id, requestData);
          break;
        default:
          throw new Error('Invalid action');
      }

      if (response.success) {
        const updatedProvider = {
          ...provider,
          status: action === 'approve' ? 'approved' as const : 
                 action === 'reject' ? 'rejected' as const : 
                 'pending' as const,
          verified: action === 'approve',
        };

        // Add new note to the list
        const newNote: ApprovalNote = {
          id: Date.now().toString(),
          message: note.trim(),
          type: action,
          createdAt: new Date().toISOString(),
          adminName: 'Current Admin', // This would come from auth context
        };
        setNotes(prev => [...prev, newNote]);

        onProviderUpdate(updatedProvider);
        toast.success(
          action === 'approve' ? 'Provider approved successfully' :
          action === 'reject' ? 'Provider rejected' :
          'Information request sent to provider'
        );
        onClose();
      } else {
        throw new Error(response.message || `Failed to ${action} provider`);
      }
    } catch (error) {
      handleError(error, { action: `${action}_provider` });
    } finally {
      loading.stopLoading();
    }
  };

  const getActionColor = (actionType: string) => {
    switch (actionType) {
      case 'approve':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-200';
      case 'reject':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-200';
      case 'request_info':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'approval':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'rejection':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case 'request_info':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  const documentTypeOptions = [
    'Business License',
    'Professional Certification',
    'Insurance Certificate',
    'Tax Registration',
    'Identity Verification',
    'Address Proof',
    'Bank Account Verification',
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-5/6 overflow-hidden flex flex-col">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Provider Approval Workflow
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Review and make a decision for {provider.name}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
            {/* Provider Information */}
            <div className="space-y-6">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Provider Information
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Business Name</label>
                    <p className="text-sm text-gray-900 dark:text-white">{provider.businessTitle || provider.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
                    <p className="text-sm text-gray-900 dark:text-white">{provider.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
                    <p className="text-sm text-gray-900 dark:text-white">{provider.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Category</label>
                    <p className="text-sm text-gray-900 dark:text-white">{provider.category?.name || 'Uncategorized'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Current Status</label>
                    <p className="text-sm text-gray-900 dark:text-white capitalize">{provider.status}</p>
                  </div>
                </div>
              </div>

              {/* Previous Notes */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Review History
                </h3>
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {notes.map((note) => (
                    <div key={note.id} className="flex items-start space-x-3">
                      <div className={`p-1 rounded-full ${getActionColor(note.type)}`}>
                        {getActionIcon(note.type)}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-gray-900 dark:text-white">{note.message}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {note.adminName} • {new Date(note.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Decision Form */}
            <div className="space-y-6">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Make Decision
                </h3>

                {/* Action Selection */}
                <div className="space-y-3 mb-6">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Select Action
                  </label>
                  <div className="space-y-2">
                    {[
                      { value: 'approve', label: 'Approve Provider', color: 'green' },
                      { value: 'reject', label: 'Reject Provider', color: 'red' },
                      { value: 'request_info', label: 'Request More Information', color: 'blue' },
                    ].map((option) => (
                      <label key={option.value} className="flex items-center">
                        <input
                          type="radio"
                          name="action"
                          value={option.value}
                          checked={action === option.value}
                          onChange={(e) => setAction(e.target.value as any)}
                          className={`h-4 w-4 text-${option.color}-600 focus:ring-${option.color}-500 border-gray-300`}
                        />
                        <span className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                          {option.label}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Note Input */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {action === 'approve' ? 'Approval Note' : 
                     action === 'reject' ? 'Rejection Reason' : 
                     'Information Request'}
                  </label>
                  <textarea
                    value={note}
                    onChange={(e) => setNote(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                    placeholder={
                      action === 'approve' ? 'Provide any additional notes for the approval...' :
                      action === 'reject' ? 'Explain the reason for rejection...' :
                      'Specify what additional information is needed...'
                    }
                  />
                </div>

                {/* Document Requirements (for request_info) */}
                {action === 'request_info' && (
                  <div className="mb-6">
                    <div className="flex items-center mb-3">
                      <input
                        id="requires-documents"
                        type="checkbox"
                        checked={requiresDocuments}
                        onChange={(e) => setRequiresDocuments(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="requires-documents" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Request specific documents
                      </label>
                    </div>

                    {requiresDocuments && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Required Documents
                        </label>
                        <div className="grid grid-cols-1 gap-2">
                          {documentTypeOptions.map((docType) => (
                            <label key={docType} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={documentTypes.includes(docType)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setDocumentTypes(prev => [...prev, docType]);
                                  } else {
                                    setDocumentTypes(prev => prev.filter(type => type !== docType));
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                {docType}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmitDecision}
            disabled={loading.isLoading || !note.trim()}
            className={`px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 ${
              action === 'approve' 
                ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500' 
                : action === 'reject'
                ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {loading.isLoading ? 'Processing...' : 
             action === 'approve' ? 'Approve Provider' :
             action === 'reject' ? 'Reject Provider' :
             'Send Request'}
          </button>
        </div>
      </div>
    </div>
  );
}

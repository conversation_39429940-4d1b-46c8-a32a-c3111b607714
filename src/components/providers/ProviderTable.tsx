import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Provider, ProvidersListRequest } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { formatDistanceToNow } from 'date-fns';
import ResponsiveTable from '../common/ResponsiveTable';
import { getStatusBgClass } from '../../utils/daltiColors';
// ProviderDetailView is now a page, not a modal
import ApprovalWorkflow from './ApprovalWorkflow';
import BulkApprovalModal from './BulkApprovalModal';
import ProviderSearchFilters from './ProviderSearchFilters';

interface ProviderTableProps {
  onProviderSelect?: (provider: Provider) => void;
  onProviderApprove?: (provider: Provider) => void;
  className?: string;
}

interface TableFilters {
  search: string;
  status: string;
  verified: string;
  category: string;
  dateRange: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export default function ProviderTable({
  onProviderSelect,
  onProviderApprove,
  className = ''
}: ProviderTableProps) {
  const navigate = useNavigate();
  const [providers, setProviders] = useState<Provider[]>([]);
  const [approvalProvider, setApprovalProvider] = useState<Provider | null>(null);
  const [bulkAction, setBulkAction] = useState<{ action: 'approve' | 'reject'; providers: Provider[] } | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });
  const [filters, setFilters] = useState<TableFilters>({
    search: '',
    status: '',
    verified: '',
    category: '',
    dateRange: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  const [selectedProviders, setSelectedProviders] = useState<string[]>([]);

  const loading = useLoading('Loading providers...');

  useEffect(() => {
    fetchProviders();
  }, [pagination.page, pagination.limit, filters]);

  // Reset pagination when filters change
  useEffect(() => {
    setPagination(prev => ({ ...prev, page: 1 }));
  }, [filters.search, filters.status, filters.verified, filters.category, filters.dateRange]);

  const fetchProviders = async () => {
    try {
      loading.startLoading();
      
      const params: ProvidersListRequest = {
        page: pagination.page,
        limit: pagination.limit,
        search: filters.search || undefined,
        status: (filters.status as 'pending' | 'approved' | 'rejected' | 'suspended') || undefined,
        verified: filters.verified ? filters.verified === 'true' : undefined,
        category: filters.category || undefined,
        dateRange: filters.dateRange || undefined,
        sortBy: filters.sortBy as any,
        sortOrder: filters.sortOrder,
      };

      const response = await adminApi.providers.getProviders(params);

      if (response.success) {
        setProviders(response.data);
        if (response.pagination) {
          setPagination(prev => ({
            ...prev,
            total: response.pagination!.total,
            totalPages: response.pagination!.totalPages,
          }));
        }
      } else {
        throw new Error(response.message || 'Failed to fetch providers');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_providers' });
      // Set fallback data for development
      setProviders([
        {
          id: '1',
          name: 'Beauty Salon XYZ',
          email: '<EMAIL>',
          phone: '+**********',
          businessTitle: 'Premium Beauty Services',
          description: 'Full-service beauty salon offering hair, nails, and skincare services',
          verified: true,
          status: 'approved',
          locationCount: 2,
          serviceCount: 15,
          customerCount: 245,
          rating: 4.8,
          totalRatings: 89,
          createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          approvedAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
          approvedBy: '<EMAIL>',
        },
        {
          id: '2',
          name: 'Dental Clinic ABC',
          email: '<EMAIL>',
          phone: '+**********',
          businessTitle: 'Modern Dental Care',
          description: 'State-of-the-art dental clinic with experienced professionals',
          verified: false,
          status: 'pending',
          locationCount: 1,
          serviceCount: 8,
          customerCount: 0,
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'Fitness Center Pro',
          email: '<EMAIL>',
          phone: '+1234567892',
          businessTitle: 'Professional Fitness Training',
          description: 'Personal training and group fitness classes',
          verified: false,
          status: 'pending',
          locationCount: 1,
          serviceCount: 12,
          customerCount: 0,
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'Auto Repair Shop',
          email: '<EMAIL>',
          phone: '+1234567893',
          businessTitle: 'Expert Auto Repairs',
          description: 'Complete automotive repair and maintenance services',
          verified: false,
          status: 'pending',
          locationCount: 1,
          serviceCount: 20,
          customerCount: 0,
          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ]);
    } finally {
      loading.stopLoading();
    }
  };



  const handleSort = (column: string) => {
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortBy === column && prev.sortOrder === 'asc' ? 'desc' : 'asc',
    }));
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleSelectProvider = (providerId: string) => {
    setSelectedProviders(prev => 
      prev.includes(providerId) 
        ? prev.filter(id => id !== providerId)
        : [...prev, providerId]
    );
  };

  const handleSelectAll = () => {
    setSelectedProviders(
      selectedProviders.length === providers.length 
        ? [] 
        : providers.map(p => p.id)
    );
  };

  const getStatusBadge = (status: string, verified: boolean) => {
    if (status === 'approved' && verified) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          Verified
        </span>
      );
    } else if (status === 'approved') {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          Approved
        </span>
      );
    } else if (status === 'pending') {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
          Pending
        </span>
      );
    } else if (status === 'rejected') {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          Rejected
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
          {status}
        </span>
      );
    }
  };

  const getRatingDisplay = (rating?: number, totalRatings?: number) => {
    if (!rating || !totalRatings) {
      return <span className="text-gray-400 text-sm">No ratings</span>;
    }

    return (
      <div className="flex items-center">
        <div className="flex items-center">
          {[1, 2, 3, 4, 5].map((star) => (
            <svg
              key={star}
              className={`w-4 h-4 ${
                star <= rating ? 'text-yellow-400' : 'text-gray-300'
              }`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          ))}
        </div>
        <span className="ml-1 text-sm text-gray-600 dark:text-gray-400">
          {rating.toFixed(1)} ({totalRatings})
        </span>
      </div>
    );
  };

  const handleFiltersReset = () => {
    setFilters({
      search: '',
      status: '',
      verified: '',
      category: '',
      dateRange: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  };

  // Define table columns for responsive table
  const tableColumns = [
    {
      key: 'select',
      title: '',
      width: '50px',
      render: (value: any, record: Provider) => (
        <input
          type="checkbox"
          checked={selectedProviders.includes(record.id)}
          onChange={(e) => {
            e.stopPropagation();
            handleSelectProvider(record.id);
          }}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      ),
    },
    {
      key: 'name',
      title: 'Provider',
      sortable: true,
      render: (value: any, record: Provider) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {record.name.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {record.name}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {record.email}
            </div>
            <div className="text-xs text-gray-400 dark:text-gray-500">
              {record.businessTitle}
            </div>
          </div>
        </div>
      ),
      mobileLabel: 'Provider',
    },
    {
      key: 'status',
      title: 'Status',
      render: (value: any, record: Provider) => getStatusBadge(record.status, record.verified),
      mobileLabel: 'Status',
    },
    {
      key: 'rating',
      title: 'Rating',
      hideOnMobile: true,
      render: (value: any, record: Provider) => getRatingDisplay(record.rating, record.totalRatings),
      mobileLabel: 'Rating',
    },
    {
      key: 'locationCount',
      title: 'Locations',
      hideOnTablet: true,
      render: (value: number) => (
        <span className="text-sm text-gray-900 dark:text-white">{value}</span>
      ),
      mobileLabel: 'Locations',
    },
    {
      key: 'customerCount',
      title: 'Customers',
      hideOnTablet: true,
      render: (value: number) => (
        <span className="text-sm text-gray-900 dark:text-white">{value}</span>
      ),
      mobileLabel: 'Customers',
    },
    {
      key: 'createdAt',
      title: 'Joined',
      sortable: true,
      hideOnMobile: true,
      render: (value: string) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {formatDistanceToNow(new Date(value), { addSuffix: true })}
        </span>
      ),
      mobileLabel: 'Joined',
    },
    {
      key: 'actions',
      title: 'Actions',
      align: 'right' as const,
      render: (value: any, record: Provider) => (
        <div className="flex items-center justify-end space-x-2">
          {record.status === 'pending' && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onProviderApprove?.(record);
              }}
              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
            >
              Approve
            </button>
          )}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onProviderSelect?.(record);
            }}
            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
          >
            View
          </button>
        </div>
      ),
      mobileLabel: 'Actions',
    },
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Providers ({pagination.total})
          </h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Manage and monitor all service providers on the platform
          </p>
        </div>

        <button
          onClick={fetchProviders}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
      </div>

      {/* Search and Filters */}
      <ProviderSearchFilters
        filters={filters}
        onFiltersChange={setFilters}
        onReset={handleFiltersReset}
      />

      {/* Bulk Actions */}
      {selectedProviders.length > 0 && (
        <div className="mb-4 flex items-center justify-between bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
          <span className="text-sm text-blue-700 dark:text-blue-300">
            {selectedProviders.length} provider(s) selected
          </span>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => {
                const selectedProviderObjects = providers.filter(p => selectedProviders.includes(p.id));
                setBulkAction({ action: 'approve', providers: selectedProviderObjects });
              }}
              className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
            >
              Bulk Approve
            </button>
            <button
              onClick={() => {
                const selectedProviderObjects = providers.filter(p => selectedProviders.includes(p.id));
                setBulkAction({ action: 'reject', providers: selectedProviderObjects });
              }}
              className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
            >
              Bulk Reject
            </button>
          </div>
        </div>
      )}

      {/* Responsive Provider Table */}
      <ResponsiveTable
        data={providers}
        columns={tableColumns}
        loading={loading.isLoading}
        pagination={{
          current: pagination.page,
          pageSize: pagination.limit,
          total: pagination.total,
          onChange: handlePageChange,
        }}
        onRowClick={(provider) => {
          navigate(`/provider/${provider.id}`);
          onProviderSelect?.(provider);
        }}
        emptyText="No providers found"
        rowKey="id"
      />

      {/* Select All Checkbox for Mobile */}
      <div className="block md:hidden mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={selectedProviders.length === providers.length && providers.length > 0}
            onChange={handleSelectAll}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
            Select all providers
          </span>
        </label>
      </div>

      {/* Provider Detail is now a separate page */}

      {/* Approval Workflow Modal */}
      {approvalProvider && (
        <ApprovalWorkflow
          provider={approvalProvider}
          onClose={() => setApprovalProvider(null)}
          onProviderUpdate={(updatedProvider) => {
            setProviders(prev =>
              prev.map(p => p.id === updatedProvider.id ? updatedProvider : p)
            );
            setApprovalProvider(null);
          }}
        />
      )}

      {/* Bulk Approval Modal */}
      {bulkAction && (
        <BulkApprovalModal
          providers={bulkAction.providers}
          action={bulkAction.action}
          onClose={() => setBulkAction(null)}
          onComplete={(updatedProviders) => {
            setProviders(prev => {
              const updatedMap = new Map(updatedProviders.map(p => [p.id, p]));
              return prev.map(p => updatedMap.get(p.id) || p);
            });
            setSelectedProviders([]);
            setBulkAction(null);
          }}
        />
      )}
    </div>
  );
}

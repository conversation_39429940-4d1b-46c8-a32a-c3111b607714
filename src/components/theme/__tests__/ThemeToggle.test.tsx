/**
 * Unit Tests for ThemeToggle Component
 */

import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, testHelpers } from '../../../utils/testUtils';
import ThemeToggle, { AnimatedThemeToggle } from '../ThemeToggle';

// Mock the useAccessibility hook
jest.mock('../../../utils/accessibility', () => ({
  useAccessibility: () => ({
    announce: jest.fn(),
    generateId: jest.fn(() => 'test-id'),
    FocusManager: {},
    KeyboardNavigation: {},
    AriaUtils: {},
  }),
}));

describe('ThemeToggle', () => {
  beforeEach(() => {
    testHelpers.clearAuth();
    localStorage.clear();
  });

  describe('Button Variant', () => {
    it('renders theme toggle button', () => {
      renderWithProviders(<ThemeToggle variant="button" />);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label');
    });

    it('toggles theme when clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ThemeToggle variant="button" />);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      // Theme should change (implementation depends on theme context)
      expect(button).toBeInTheDocument();
    });

    it('shows correct icon for light theme', () => {
      renderWithProviders(<ThemeToggle variant="button" />, { theme: 'light' });
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      
      // Check for moon icon (dark theme icon when in light mode)
      const moonIcon = button.querySelector('svg');
      expect(moonIcon).toBeInTheDocument();
    });

    it('shows correct icon for dark theme', () => {
      renderWithProviders(<ThemeToggle variant="button" />, { theme: 'dark' });
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      
      // Check for sun icon (light theme icon when in dark mode)
      const sunIcon = button.querySelector('svg');
      expect(sunIcon).toBeInTheDocument();
    });

    it('displays label when showLabel is true', () => {
      renderWithProviders(<ThemeToggle variant="button" showLabel={true} />);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      
      // Should contain text content
      expect(button.textContent).toBeTruthy();
    });

    it('applies correct size classes', () => {
      const { rerender } = renderWithProviders(<ThemeToggle variant="button" size="sm" />);
      let button = screen.getByRole('button');
      expect(button).toHaveClass('p-1.5');
      
      rerender(<ThemeToggle variant="button" size="lg" />);
      button = screen.getByRole('button');
      expect(button).toHaveClass('p-3');
    });

    it('is accessible via keyboard', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ThemeToggle variant="button" />);
      
      const button = screen.getByRole('button');
      
      // Focus the button
      await user.tab();
      expect(button).toHaveFocus();
      
      // Activate with Enter
      await user.keyboard('{Enter}');
      expect(button).toBeInTheDocument();
      
      // Activate with Space
      await user.keyboard(' ');
      expect(button).toBeInTheDocument();
    });
  });

  describe('Switch Variant', () => {
    it('renders theme switch', () => {
      renderWithProviders(<ThemeToggle variant="switch" />);
      
      const switchElement = screen.getByRole('switch');
      expect(switchElement).toBeInTheDocument();
      expect(switchElement).toHaveAttribute('aria-checked');
    });

    it('toggles when clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ThemeToggle variant="switch" />);
      
      const switchElement = screen.getByRole('switch');
      const initialChecked = switchElement.getAttribute('aria-checked');
      
      await user.click(switchElement);
      
      // Should toggle the checked state
      expect(switchElement).toBeInTheDocument();
    });

    it('shows label when showLabel is true', () => {
      renderWithProviders(<ThemeToggle variant="switch" showLabel={true} />);
      
      expect(screen.getByText('Theme')).toBeInTheDocument();
    });

    it('applies correct size classes for switch', () => {
      const { rerender } = renderWithProviders(<ThemeToggle variant="switch" size="sm" />);
      let switchElement = screen.getByRole('switch');
      expect(switchElement).toHaveClass('w-8', 'h-4');
      
      rerender(<ThemeToggle variant="switch" size="lg" />);
      switchElement = screen.getByRole('switch');
      expect(switchElement).toHaveClass('w-12', 'h-6');
    });
  });

  describe('Dropdown Variant', () => {
    it('renders theme dropdown', () => {
      renderWithProviders(<ThemeToggle variant="dropdown" />);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-expanded', 'false');
      expect(button).toHaveAttribute('aria-haspopup', 'menu');
    });

    it('opens dropdown when clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ThemeToggle variant="dropdown" />);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(button).toHaveAttribute('aria-expanded', 'true');
      
      // Check for menu items
      expect(screen.getByRole('menu')).toBeInTheDocument();
      expect(screen.getByRole('menuitem', { name: /light/i })).toBeInTheDocument();
      expect(screen.getByRole('menuitem', { name: /dark/i })).toBeInTheDocument();
      expect(screen.getByRole('menuitem', { name: /system/i })).toBeInTheDocument();
    });

    it('closes dropdown when clicking outside', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ThemeToggle variant="dropdown" />);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(button).toHaveAttribute('aria-expanded', 'true');
      
      // Click outside
      await user.click(document.body);
      
      await waitFor(() => {
        expect(button).toHaveAttribute('aria-expanded', 'false');
      });
    });

    it('selects theme option when clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ThemeToggle variant="dropdown" />);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      const lightOption = screen.getByRole('menuitem', { name: /light/i });
      await user.click(lightOption);
      
      // Dropdown should close
      await waitFor(() => {
        expect(button).toHaveAttribute('aria-expanded', 'false');
      });
    });

    it('shows current theme selection', () => {
      renderWithProviders(<ThemeToggle variant="dropdown" />, { theme: 'dark' });
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      
      // Should show current theme indicator
      expect(button.querySelector('svg')).toBeInTheDocument();
    });

    it('handles keyboard navigation in dropdown', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ThemeToggle variant="dropdown" />);
      
      const button = screen.getByRole('button');
      
      // Open with Enter
      await user.keyboard('{Enter}');
      expect(button).toHaveAttribute('aria-expanded', 'true');
      
      // Navigate with arrow keys
      await user.keyboard('{ArrowDown}');
      await user.keyboard('{ArrowUp}');
      
      // Close with Escape
      await user.keyboard('{Escape}');
      await waitFor(() => {
        expect(button).toHaveAttribute('aria-expanded', 'false');
      });
    });
  });

  describe('AnimatedThemeToggle', () => {
    it('renders animated toggle', () => {
      renderWithProviders(<AnimatedThemeToggle />);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label');
    });

    it('has animated classes', () => {
      renderWithProviders(<AnimatedThemeToggle />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('transition-all', 'duration-300');
    });

    it('shows different icons for light and dark themes', () => {
      const { rerender } = renderWithProviders(<AnimatedThemeToggle />, { theme: 'light' });
      let button = screen.getByRole('button');
      let icon = button.querySelector('svg');
      expect(icon).toBeInTheDocument();
      
      rerender(<AnimatedThemeToggle />);
      // In dark theme, should show different icon
      button = screen.getByRole('button');
      icon = button.querySelector('svg');
      expect(icon).toBeInTheDocument();
    });

    it('toggles theme when clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AnimatedThemeToggle />);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(button).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      renderWithProviders(<ThemeToggle variant="button" />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label');
    });

    it('announces theme changes to screen readers', async () => {
      const user = userEvent.setup();
      const mockAnnounce = jest.fn();
      
      // Mock the accessibility hook
      jest.doMock('../../../utils/accessibility', () => ({
        useAccessibility: () => ({
          announce: mockAnnounce,
          generateId: jest.fn(() => 'test-id'),
        }),
      }));
      
      renderWithProviders(<ThemeToggle variant="button" />);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      // Should announce theme change
      expect(mockAnnounce).toHaveBeenCalled();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ThemeToggle variant="dropdown" />);
      
      const button = screen.getByRole('button');
      
      // Should be focusable
      await user.tab();
      expect(button).toHaveFocus();
      
      // Should open with Enter
      await user.keyboard('{Enter}');
      expect(button).toHaveAttribute('aria-expanded', 'true');
    });

    it('has proper focus management', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ThemeToggle variant="dropdown" />);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      // Focus should be managed within dropdown
      const menu = screen.getByRole('menu');
      expect(menu).toBeInTheDocument();
      
      // Should close and restore focus with Escape
      await user.keyboard('{Escape}');
      await waitFor(() => {
        expect(button).toHaveAttribute('aria-expanded', 'false');
      });
    });
  });

  describe('Error Handling', () => {
    it('handles missing theme context gracefully', () => {
      // Test without theme provider
      expect(() => {
        renderWithProviders(<ThemeToggle variant="button" />, {});
      }).not.toThrow();
    });

    it('handles invalid props gracefully', () => {
      expect(() => {
        renderWithProviders(
          <ThemeToggle variant="button" size={'invalid' as any} />
        );
      }).not.toThrow();
    });
  });
});

import React from 'react';
import { useThemeStyles } from '../../contexts/ThemeContext';

// Theme-aware Card Component
interface ThemeCardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
}

export function ThemeCard({
  children,
  variant = 'default',
  padding = 'md',
  className = '',
  onClick,
}: ThemeCardProps) {
  const { themeClasses } = useThemeStyles();

  const variantClasses = {
    default: `${themeClasses.bg.card} ${themeClasses.border.primary} border`,
    elevated: `${themeClasses.bg.card} shadow-lg`,
    outlined: `${themeClasses.bg.card} ${themeClasses.border.secondary} border-2`,
    filled: themeClasses.bg.secondary,
  };

  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-4 md:p-6',
    lg: 'p-6 md:p-8',
  };

  return (
    <div
      className={`
        rounded-lg transition-colors duration-200
        ${variantClasses[variant]}
        ${paddingClasses[padding]}
        ${onClick ? `cursor-pointer ${themeClasses.interactive.hover}` : ''}
        ${className}
      `}
      onClick={onClick}
    >
      {children}
    </div>
  );
}

// Theme-aware Button Component
interface ThemeButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

export function ThemeButton({
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  children,
  className = '',
  disabled,
  ...props
}: ThemeButtonProps) {
  const { getThemeClass, isDark } = useThemeStyles();

  const baseClasses = `
    inline-flex items-center justify-center
    font-medium rounded-lg
    transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${fullWidth ? 'w-full' : ''}
  `;

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm md:text-base',
    lg: 'px-6 py-3 text-base md:text-lg',
  };

  const variantClasses = {
    primary: `
      bg-blue-600 text-white 
      hover:bg-blue-700 
      focus:ring-blue-500
      ${isDark ? 'focus:ring-offset-gray-800' : 'focus:ring-offset-white'}
    `,
    secondary: `
      bg-green-600 text-white 
      hover:bg-green-700 
      focus:ring-green-500
      ${isDark ? 'focus:ring-offset-gray-800' : 'focus:ring-offset-white'}
    `,
    outline: `
      ${getThemeClass('border border-gray-300 text-gray-700 bg-white hover:bg-gray-50', 'border border-gray-600 text-gray-300 bg-gray-800 hover:bg-gray-700')}
      focus:ring-blue-500
      ${isDark ? 'focus:ring-offset-gray-800' : 'focus:ring-offset-white'}
    `,
    ghost: `
      ${getThemeClass('text-gray-700 hover:bg-gray-100', 'text-gray-300 hover:bg-gray-700')}
      focus:ring-blue-500
      ${isDark ? 'focus:ring-offset-gray-800' : 'focus:ring-offset-white'}
    `,
    danger: `
      bg-red-600 text-white 
      hover:bg-red-700 
      focus:ring-red-500
      ${isDark ? 'focus:ring-offset-gray-800' : 'focus:ring-offset-white'}
    `,
  };

  return (
    <button
      className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {!loading && icon && iconPosition === 'left' && (
        <span className="mr-2">{icon}</span>
      )}
      {children}
      {!loading && icon && iconPosition === 'right' && (
        <span className="ml-2">{icon}</span>
      )}
    </button>
  );
}

// Theme-aware Input Component
interface ThemeInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

export function ThemeInput({
  error = false,
  icon,
  iconPosition = 'left',
  className = '',
  ...props
}: ThemeInputProps) {
  const { getThemeClass, isDark } = useThemeStyles();

  const baseClasses = `
    w-full px-3 py-2 text-sm md:text-base
    border rounded-lg
    focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none
    transition-colors duration-200
    ${getThemeClass('bg-white text-gray-900', 'bg-gray-700 text-white')}
    ${error 
      ? getThemeClass('border-red-300 focus:ring-red-500 focus:border-red-500', 'border-red-600 focus:ring-red-500 focus:border-red-500')
      : getThemeClass('border-gray-300', 'border-gray-600')
    }
    ${isDark ? 'focus:ring-offset-gray-800' : 'focus:ring-offset-white'}
    ${icon ? (iconPosition === 'left' ? 'pl-10' : 'pr-10') : ''}
  `;

  if (icon) {
    return (
      <div className="relative">
        {iconPosition === 'left' && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div className={getThemeClass('text-gray-400', 'text-gray-500')} aria-hidden="true">
              {icon}
            </div>
          </div>
        )}
        <input className={`${baseClasses} ${className}`} {...props} />
        {iconPosition === 'right' && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <div className={getThemeClass('text-gray-400', 'text-gray-500')} aria-hidden="true">
              {icon}
            </div>
          </div>
        )}
      </div>
    );
  }

  return <input className={`${baseClasses} ${className}`} {...props} />;
}

// Theme-aware Badge Component
interface ThemeBadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ThemeBadge({
  children,
  variant = 'default',
  size = 'md',
  className = '',
}: ThemeBadgeProps) {
  const { getThemeClass } = useThemeStyles();

  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base',
  };

  const variantClasses = {
    default: getThemeClass(
      'bg-gray-100 text-gray-800',
      'bg-gray-700 text-gray-200'
    ),
    primary: getThemeClass(
      'bg-blue-100 text-blue-800',
      'bg-blue-900/20 text-blue-200'
    ),
    secondary: getThemeClass(
      'bg-green-100 text-green-800',
      'bg-green-900/20 text-green-200'
    ),
    success: getThemeClass(
      'bg-green-100 text-green-800',
      'bg-green-900/20 text-green-200'
    ),
    warning: getThemeClass(
      'bg-yellow-100 text-yellow-800',
      'bg-yellow-900/20 text-yellow-200'
    ),
    danger: getThemeClass(
      'bg-red-100 text-red-800',
      'bg-red-900/20 text-red-200'
    ),
  };

  return (
    <span
      className={`
        inline-flex items-center font-medium rounded-full
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${className}
      `}
    >
      {children}
    </span>
  );
}

// Theme-aware Alert Component
interface ThemeAlertProps {
  children: React.ReactNode;
  variant?: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

export function ThemeAlert({
  children,
  variant = 'info',
  title,
  dismissible = false,
  onDismiss,
  className = '',
}: ThemeAlertProps) {
  const { getThemeClass } = useThemeStyles();

  const variantClasses = {
    info: getThemeClass(
      'bg-blue-50 border-blue-200 text-blue-800',
      'bg-blue-900/20 border-blue-800 text-blue-200'
    ),
    success: getThemeClass(
      'bg-green-50 border-green-200 text-green-800',
      'bg-green-900/20 border-green-800 text-green-200'
    ),
    warning: getThemeClass(
      'bg-yellow-50 border-yellow-200 text-yellow-800',
      'bg-yellow-900/20 border-yellow-800 text-yellow-200'
    ),
    error: getThemeClass(
      'bg-red-50 border-red-200 text-red-800',
      'bg-red-900/20 border-red-800 text-red-200'
    ),
  };

  const iconMap = {
    info: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    success: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    warning: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    ),
    error: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  };

  return (
    <div
      className={`
        border rounded-lg p-4
        ${variantClasses[variant]}
        ${className}
      `}
      role="alert"
    >
      <div className="flex">
        <div className="flex-shrink-0">
          {iconMap[variant]}
        </div>
        <div className="ml-3 flex-1">
          {title && (
            <h3 className="text-sm font-medium mb-1">
              {title}
            </h3>
          )}
          <div className="text-sm">
            {children}
          </div>
        </div>
        {dismissible && onDismiss && (
          <div className="ml-auto pl-3">
            <button
              onClick={onDismiss}
              className={`
                inline-flex rounded-md p-1.5
                ${getThemeClass('hover:bg-blue-100', 'hover:bg-blue-800/30')}
                focus:outline-none focus:ring-2 focus:ring-blue-500
              `}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// Theme-aware Loading Spinner
interface ThemeSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ThemeSpinner({ size = 'md', className = '' }: ThemeSpinnerProps) {
  const { getThemeClass } = useThemeStyles();

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <svg
      className={`
        animate-spin
        ${sizeClasses[size]}
        ${getThemeClass('text-gray-600', 'text-gray-400')}
        ${className}
      `}
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
}

// Theme-aware Divider
interface ThemeDividerProps {
  orientation?: 'horizontal' | 'vertical';
  className?: string;
}

export function ThemeDivider({ orientation = 'horizontal', className = '' }: ThemeDividerProps) {
  const { themeClasses } = useThemeStyles();

  const orientationClasses = {
    horizontal: `w-full h-px ${themeClasses.border.primary}`,
    vertical: `h-full w-px ${themeClasses.border.primary}`,
  };

  return (
    <div
      className={`
        ${orientationClasses[orientation]}
        ${className}
      `}
      role="separator"
      aria-orientation={orientation}
    />
  );
}

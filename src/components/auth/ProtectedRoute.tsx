import React from 'react';
import { Navigate, useLocation } from 'react-router';
import { useAuth } from '../../context/AuthContext';
import LoadingSpinner from '../common/LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

/**
 * ProtectedRoute component that handles authentication-based route protection
 * 
 * @param children - The component(s) to render if access is granted
 * @param requireAuth - Whether authentication is required (default: true)
 * @param redirectTo - Where to redirect if access is denied (default: '/login')
 */
export default function ProtectedRoute({ 
  children, 
  requireAuth = true, 
  redirectTo = '/login' 
}: ProtectedRouteProps) {
  const { state } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (state.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" message="Checking authentication..." />
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !state.isAuthenticated) {
    // Save the attempted location for redirect after login
    return (
      <Navigate 
        to={redirectTo} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // If user is authenticated but trying to access auth pages (login)
  if (!requireAuth && state.isAuthenticated) {
    // Get the intended destination from state or default to dashboard
    const from = location.state?.from || '/';
    return <Navigate to={from} replace />;
  }

  // Access granted - render the protected content
  return <>{children}</>;
}

/**
 * Higher-order component for protecting routes
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<ProtectedRouteProps, 'children'>
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

/**
 * Hook for checking authentication status in components
 */
export function useRequireAuth() {
  const { state } = useAuth();
  const location = useLocation();

  React.useEffect(() => {
    if (!state.isLoading && !state.isAuthenticated) {
      // This will trigger a navigation to login
      window.location.href = `/login?redirect=${encodeURIComponent(location.pathname)}`;
    }
  }, [state.isAuthenticated, state.isLoading, location.pathname]);

  return {
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    admin: state.admin,
  };
}

/**
 * Component for handling authentication redirects
 */
export function AuthRedirect() {
  const { state } = useAuth();
  const location = useLocation();

  if (state.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" message="Redirecting..." />
      </div>
    );
  }

  if (state.isAuthenticated) {
    // Get the intended destination from URL params or state
    const urlParams = new URLSearchParams(location.search);
    const redirectTo = urlParams.get('redirect') || location.state?.from || '/';
    return <Navigate to={redirectTo} replace />;
  }

  return <Navigate to="/login" replace />;
}

/**
 * Component for role-based access control (future enhancement)
 */
interface RoleGuardProps {
  children: React.ReactNode;
  requiredRole?: string;
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
}

export function RoleGuard({ 
  children, 
  requiredRole = 'ADMIN',
  requiredPermissions = [],
  fallback = <div>Access Denied</div>
}: RoleGuardProps) {
  const { state } = useAuth();

  // Check if user has required role
  if (requiredRole && state.admin?.role !== requiredRole) {
    return <>{fallback}</>;
  }

  // Check if user has required permissions (placeholder for future implementation)
  if (requiredPermissions.length > 0) {
    // TODO: Implement permission checking logic
    // For now, all admins have all permissions
    if (!state.admin?.isAdmin) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
}

/**
 * Hook for checking permissions
 */
export function usePermissions() {
  const { state } = useAuth();

  const hasRole = (role: string) => {
    return state.admin?.role === role;
  };

  const hasPermission = (permission: string) => {
    // TODO: Implement actual permission checking
    // For now, all admins have all permissions
    return state.admin?.isAdmin || false;
  };

  const isAdmin = () => {
    return state.admin?.isAdmin || false;
  };

  return {
    hasRole,
    hasPermission,
    isAdmin,
    admin: state.admin,
  };
}

import React, { useEffect, useState } from 'react';
import { RecentActivity } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { formatDistanceToNow } from 'date-fns';

interface ActivityItemProps {
  activity: RecentActivity;
  onClick?: (activity: RecentActivity) => void;
}

function ActivityItem({ activity, onClick }: ActivityItemProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'provider_registration':
        return (
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
        );
      case 'provider_approval':
        return (
          <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'customer_registration':
        return (
          <div className="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        );
      case 'booking_created':
        return (
          <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        );
      case 'booking_completed':
        return (
          <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="flex-shrink-0 w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  const timeAgo = formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true });

  return (
    <div 
      className={`flex items-start space-x-3 p-3 rounded-lg transition-colors ${
        onClick ? 'hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer' : ''
      }`}
      onClick={() => onClick?.(activity)}
    >
      {getActivityIcon(activity.type)}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-white">
          {activity.title}
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {activity.description}
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
          {timeAgo}
        </p>
      </div>
    </div>
  );
}

interface RecentActivityFeedProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
  onActivityClick?: (activity: RecentActivity) => void;
}

export default function RecentActivityFeed({
  limit = 10,
  showHeader = true,
  className = '',
  onActivityClick
}: RecentActivityFeedProps) {
  const [activities, setActivities] = useState<RecentActivity[]>([]);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const loading = useLoading('Loading recent activities...');

  useEffect(() => {
    fetchActivities();

    // Set up auto-refresh every 30 seconds
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        fetchActivities();
        setLastRefresh(new Date());
      }, 30000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [limit, autoRefresh]);

  const fetchActivities = async () => {
    try {
      loading.startLoading();
      const response = await adminApi.dashboard.getRecentActivities(limit);
      
      if (response.success) {
        setActivities(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch activities');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_recent_activities' });
      // Set fallback data for development
      setActivities([
        {
          id: '1',
          type: 'provider_registration',
          title: 'New Provider Registration',
          description: 'Beauty Salon XYZ has registered and is pending approval',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
          entityId: 'provider-123',
          entityType: 'provider',
        },
        {
          id: '2',
          type: 'provider_approval',
          title: 'Provider Approved',
          description: 'Dental Clinic ABC has been approved and is now active',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
          entityId: 'provider-122',
          entityType: 'provider',
        },
        {
          id: '3',
          type: 'customer_registration',
          title: 'New Customer Registration',
          description: 'John Doe has joined the platform',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
          entityId: 'customer-456',
          entityType: 'customer',
        },
        {
          id: '4',
          type: 'booking_created',
          title: 'New Booking Created',
          description: 'Appointment scheduled for Hair Salon DEF',
          timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
          entityId: 'booking-789',
          entityType: 'booking',
        },
        {
          id: '5',
          type: 'booking_completed',
          title: 'Booking Completed',
          description: 'Massage therapy session completed successfully',
          timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // 1 hour ago
          entityId: 'booking-788',
          entityType: 'booking',
        },
      ]);
    } finally {
      loading.stopLoading();
    }
  };

  if (loading.isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        {showHeader && (
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Recent Activity
            </h3>
          </div>
        )}
        <div className="p-6 space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-start space-x-3 animate-pulse">
              <div className="flex-shrink-0 w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      {showHeader && (
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Recent Activity
              </h3>
              {autoRefresh && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Last updated: {lastRefresh.toLocaleTimeString()}
                </p>
              )}
            </div>

            <div className="flex items-center space-x-3">
              {/* Auto-refresh toggle */}
              <div className="flex items-center">
                <input
                  id="auto-refresh"
                  type="checkbox"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="auto-refresh" className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                  Auto-refresh
                </label>
              </div>

              {/* Manual refresh button */}
              <button
                onClick={() => {
                  fetchActivities();
                  setLastRefresh(new Date());
                }}
                className="inline-flex items-center px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Refresh
              </button>
            </div>
          </div>
        </div>
      )}
      
      <div className="p-6">
        {activities.length === 0 ? (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <p className="text-gray-500 dark:text-gray-400">No recent activity</p>
          </div>
        ) : (
          <div className="space-y-1">
            {activities.map((activity) => (
              <ActivityItem
                key={activity.id}
                activity={activity}
                onClick={onActivityClick}
              />
            ))}
          </div>
        )}
      </div>
      
      {activities.length > 0 && (
        <div className="px-6 py-3 border-t border-gray-200 dark:border-gray-700">
          <button className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium">
            View all activity
          </button>
        </div>
      )}
    </div>
  );
}

// Compact version for smaller spaces
export function CompactActivityFeed({ 
  limit = 5,
  className = '' 
}: { 
  limit?: number; 
  className?: string; 
}) {
  return (
    <RecentActivityFeed
      limit={limit}
      showHeader={false}
      className={className}
    />
  );
}

// Activity summary component
export function ActivitySummary() {
  const [summary, setSummary] = useState({
    todayCount: 0,
    weekCount: 0,
    pendingCount: 0,
  });

  useEffect(() => {
    // This would fetch activity summary data
    setSummary({
      todayCount: 12,
      weekCount: 89,
      pendingCount: 3,
    });
  }, []);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Activity Summary
      </h3>
      <div className="grid grid-cols-3 gap-4">
        <div className="text-center">
          <p className="text-2xl font-semibold text-blue-600 dark:text-blue-400">
            {summary.todayCount}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">Today</p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-semibold text-green-600 dark:text-green-400">
            {summary.weekCount}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">This Week</p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-semibold text-yellow-600 dark:text-yellow-400">
            {summary.pendingCount}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">Pending</p>
        </div>
      </div>
    </div>
  );
}

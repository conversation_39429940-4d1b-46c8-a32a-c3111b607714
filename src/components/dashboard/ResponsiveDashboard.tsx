import React from 'react';
import { useScreenSize } from '../../utils/responsive';
import ResponsiveCardGrid, { ResponsiveStatsCard, ResponsiveChartCard } from '../common/ResponsiveCardGrid';
import { ResponsivePageHeader, ResponsiveSection, ResponsiveTwoColumn } from '../layout/ResponsiveLayout';
import { AdminBreadcrumb } from '../navigation/Breadcrumb';
import { DashboardIcon, ProvidersIcon, CustomersIcon, BookingsIcon } from '../icons/DaltiIcons';

interface ResponsiveDashboardProps {
  className?: string;
}

export default function ResponsiveDashboard({ className = '' }: ResponsiveDashboardProps) {
  const { isMobile, isTablet, isDesktop } = useScreenSize();

  // Mock data for demonstration
  const statsData = [
    {
      title: 'Total Providers',
      value: '2,847',
      change: { value: '+12.5%', trend: 'up' as const },
      icon: <ProvidersIcon />,
      color: 'blue' as const,
    },
    {
      title: 'Active Customers',
      value: '18,392',
      change: { value: '****%', trend: 'up' as const },
      icon: <CustomersIcon />,
      color: 'green' as const,
    },
    {
      title: 'Total Bookings',
      value: '45,621',
      change: { value: '+15.3%', trend: 'up' as const },
      icon: <BookingsIcon />,
      color: 'purple' as const,
    },
    {
      title: 'Revenue',
      value: '$284,750',
      change: { value: '-2.1%', trend: 'down' as const },
      icon: <DashboardIcon />,
      color: 'yellow' as const,
    },
  ];

  const recentActivity = [
    {
      id: '1',
      type: 'provider_registration',
      title: 'New Provider Registration',
      description: 'Dr. Sarah Johnson registered as a healthcare provider',
      time: '2 minutes ago',
      status: 'pending',
    },
    {
      id: '2',
      type: 'booking_completed',
      title: 'Booking Completed',
      description: 'Customer John Doe completed appointment with Dr. Smith',
      time: '15 minutes ago',
      status: 'completed',
    },
    {
      id: '3',
      type: 'payment_received',
      title: 'Payment Received',
      description: 'Payment of $150 received for booking #12345',
      time: '1 hour ago',
      status: 'completed',
    },
    {
      id: '4',
      type: 'provider_verified',
      title: 'Provider Verified',
      description: 'Dr. Michael Brown has been verified and approved',
      time: '2 hours ago',
      status: 'completed',
    },
  ];

  const pendingApprovals = [
    {
      id: '1',
      name: 'Dr. Emily Davis',
      type: 'Healthcare Provider',
      submitted: '2 days ago',
      status: 'pending',
    },
    {
      id: '2',
      name: 'FitLife Gym',
      type: 'Fitness Center',
      submitted: '1 day ago',
      status: 'pending',
    },
    {
      id: '3',
      name: 'Beauty Salon Pro',
      type: 'Beauty & Wellness',
      submitted: '3 hours ago',
      status: 'pending',
    },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'provider_registration':
        return '👥';
      case 'booking_completed':
        return '📅';
      case 'payment_received':
        return '💰';
      case 'provider_verified':
        return '✅';
      default:
        return '📋';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'failed':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div className={className}>
      {/* Page Header */}
      <ResponsivePageHeader
        title="Dashboard"
        subtitle="Welcome to the Dalti Admin Panel"
        breadcrumb={<AdminBreadcrumb />}
        actions={
          <div className="flex flex-col sm:flex-row gap-3">
            <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
              Export Report
            </button>
            <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
              Add Provider
            </button>
          </div>
        }
      />

      {/* Stats Cards */}
      <ResponsiveSection padding="md">
        <ResponsiveCardGrid
          gridType="dashboardCards"
          gap="md"
          className="mb-8"
        >
          {statsData.map((stat, index) => (
            <ResponsiveStatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              change={stat.change}
              icon={stat.icon}
              color={stat.color}
            />
          ))}
        </ResponsiveCardGrid>

        {/* Main Content */}
        <ResponsiveTwoColumn
          leftWidth="wide"
          gap="lg"
          left={
            <div className="space-y-6">
              {/* Chart Card */}
              <ResponsiveChartCard
                title="Booking Trends"
                subtitle="Monthly booking statistics"
                actions={
                  <select className="text-sm border border-gray-300 rounded-lg px-3 py-1 dark:bg-gray-700 dark:border-gray-600">
                    <option>Last 30 days</option>
                    <option>Last 90 days</option>
                    <option>Last year</option>
                  </select>
                }
              >
                <div className="h-64 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500 dark:text-gray-400">Chart placeholder</p>
                </div>
              </ResponsiveChartCard>

              {/* Recent Activity */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Recent Activity
                </h3>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className="flex-shrink-0 text-2xl">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {activity.title}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {activity.description}
                        </p>
                        <p className={`text-xs ${getStatusColor(activity.status)} mt-1`}>
                          {activity.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          }
          right={
            <div className="space-y-6">
              {/* Pending Approvals */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Pending Approvals
                  </h3>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                    {pendingApprovals.length} pending
                  </span>
                </div>
                <div className="space-y-3">
                  {pendingApprovals.map((approval) => (
                    <div key={approval.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {approval.name}
                        </p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {approval.type} • {approval.submitted}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 ml-3">
                        <button className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 text-sm">
                          Approve
                        </button>
                        <button className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm">
                          Reject
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
                <button className="w-full mt-4 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30">
                  View All Pending
                </button>
              </div>

              {/* Quick Actions */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Quick Actions
                </h3>
                <div className="space-y-3">
                  <button className="w-full flex items-center justify-between p-3 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                    <div className="flex items-center">
                      <ProvidersIcon className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        Add New Provider
                      </span>
                    </div>
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                  
                  <button className="w-full flex items-center justify-between p-3 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                    <div className="flex items-center">
                      <CustomersIcon className="w-5 h-5 text-green-600 dark:text-green-400 mr-3" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        View Customer Reports
                      </span>
                    </div>
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                  
                  <button className="w-full flex items-center justify-between p-3 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                    <div className="flex items-center">
                      <BookingsIcon className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-3" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        System Maintenance
                      </span>
                    </div>
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          }
        />
      </ResponsiveSection>
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    tension?: number;
  }[];
}

interface AnalyticsChartsProps {
  className?: string;
}

export default function AnalyticsCharts({ className = '' }: AnalyticsChartsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [providerGrowthData, setProviderGrowthData] = useState<ChartData | null>(null);
  const [customerAcquisitionData, setCustomerAcquisitionData] = useState<ChartData | null>(null);
  const [bookingTrendsData, setBookingTrendsData] = useState<ChartData | null>(null);
  const loading = useLoading();

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedPeriod]);

  const fetchAnalyticsData = async () => {
    try {
      loading.startLoading({ message: 'Loading analytics...' });
      const response = await adminApi.dashboard.getAnalytics(selectedPeriod);
      
      if (response.success && response.data) {
        // Transform API data to chart format
        transformAnalyticsData(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch analytics');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_analytics' });
      // Set fallback data for development
      setFallbackData();
    } finally {
      loading.stopLoading();
    }
  };

  const setFallbackData = () => {
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    });

    // Provider Growth Data
    setProviderGrowthData({
      labels: last30Days,
      datasets: [
        {
          label: 'New Providers',
          data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 10) + 1),
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
        },
        {
          label: 'Approved Providers',
          data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 8) + 1),
          borderColor: '#10B981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          tension: 0.4,
        },
      ],
    });

    // Customer Acquisition Data
    setCustomerAcquisitionData({
      labels: last30Days,
      datasets: [
        {
          label: 'New Customers',
          data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 25) + 5),
          borderColor: '#8B5CF6',
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          tension: 0.4,
        },
        {
          label: 'Verified Customers',
          data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 20) + 3),
          borderColor: '#F59E0B',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          tension: 0.4,
        },
      ],
    });

    // Booking Trends Data
    setBookingTrendsData({
      labels: last30Days,
      datasets: [
        {
          label: 'Total Bookings',
          data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 50) + 10),
          borderColor: '#EF4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          tension: 0.4,
        },
        {
          label: 'Completed Bookings',
          data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 40) + 8),
          borderColor: '#06B6D4',
          backgroundColor: 'rgba(6, 182, 212, 0.1)',
          tension: 0.4,
        },
      ],
    });
  };

  const transformAnalyticsData = (data: any) => {
    // Transform API response to chart data format
    // This would be implemented based on actual API response structure
    setFallbackData(); // For now, use fallback data
  };

  const periods = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 3 Months' },
    { value: '1y', label: 'Last Year' },
  ];

  const SimpleLineChart = ({ data, title }: { data: ChartData | null; title: string }) => {
    if (!data) return null;

    const maxValue = Math.max(...data.datasets.flatMap(d => d.data));
    const chartHeight = 200;

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">{title}</h3>
        
        <div className="relative" style={{ height: chartHeight }}>
          <svg width="100%" height={chartHeight} className="overflow-visible">
            {/* Grid lines */}
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, i) => (
              <line
                key={i}
                x1="0"
                y1={chartHeight * ratio}
                x2="100%"
                y2={chartHeight * ratio}
                stroke="#E5E7EB"
                strokeWidth="1"
                className="dark:stroke-gray-600"
              />
            ))}
            
            {/* Data lines */}
            {data.datasets.map((dataset, datasetIndex) => {
              const points = dataset.data.map((value, index) => ({
                x: (index / (dataset.data.length - 1)) * 100,
                y: chartHeight - (value / maxValue) * chartHeight,
              }));

              const pathData = points.reduce((path, point, index) => {
                if (index === 0) {
                  return `M ${point.x} ${point.y}`;
                }
                const prevPoint = points[index - 1];
                const cpx1 = prevPoint.x + (point.x - prevPoint.x) * 0.5;
                const cpx2 = point.x - (point.x - prevPoint.x) * 0.5;
                return `${path} C ${cpx1} ${prevPoint.y} ${cpx2} ${point.y} ${point.x} ${point.y}`;
              }, '');

              return (
                <g key={datasetIndex}>
                  {/* Area fill */}
                  <path
                    d={`${pathData} L ${points[points.length - 1].x} ${chartHeight} L 0 ${chartHeight} Z`}
                    fill={dataset.backgroundColor}
                  />
                  {/* Line */}
                  <path
                    d={pathData}
                    fill="none"
                    stroke={dataset.borderColor}
                    strokeWidth="2"
                  />
                  {/* Data points */}
                  {points.map((point, index) => (
                    <circle
                      key={index}
                      cx={`${point.x}%`}
                      cy={point.y}
                      r="3"
                      fill={dataset.borderColor}
                      className="hover:r-4 transition-all"
                    />
                  ))}
                </g>
              );
            })}
          </svg>
          
          {/* Legend */}
          <div className="flex items-center justify-center mt-4 space-x-6">
            {data.datasets.map((dataset, index) => (
              <div key={index} className="flex items-center">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: dataset.borderColor }}
                ></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {dataset.label}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const StatCard = ({ title, value, change, icon }: { 
    title: string; 
    value: string; 
    change: string; 
    icon: React.ReactNode;
  }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
          {icon}
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-semibold text-gray-900 dark:text-white">{value}</p>
          <p className="text-sm text-green-600 dark:text-green-400">{change}</p>
        </div>
      </div>
    </div>
  );

  if (loading.isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Platform Analytics
        </h2>
        
        <select
          value={selectedPeriod}
          onChange={(e) => setSelectedPeriod(e.target.value)}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        >
          {periods.map((period) => (
            <option key={period.value} value={period.value}>
              {period.label}
            </option>
          ))}
        </select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title="Total Providers"
          value="1,234"
          change="+12% from last month"
          icon={
            <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          }
        />
        
        <StatCard
          title="Active Customers"
          value="5,678"
          change="+18% from last month"
          icon={
            <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          }
        />
        
        <StatCard
          title="Monthly Bookings"
          value="2,890"
          change="+25% from last month"
          icon={
            <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          }
        />
        
        <StatCard
          title="Revenue"
          value="$45,678"
          change="+15% from last month"
          icon={
            <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          }
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <SimpleLineChart
          data={providerGrowthData}
          title="Provider Growth"
        />
        
        <SimpleLineChart
          data={customerAcquisitionData}
          title="Customer Acquisition"
        />
        
        <SimpleLineChart
          data={bookingTrendsData}
          title="Booking Trends"
        />
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Top Performing Categories
          </h3>
          <div className="space-y-3">
            {[
              { name: 'Beauty & Wellness', providers: 245, bookings: 1234 },
              { name: 'Healthcare', providers: 189, bookings: 987 },
              { name: 'Fitness', providers: 156, bookings: 756 },
              { name: 'Automotive', providers: 134, bookings: 543 },
            ].map((category, index) => (
              <div key={index} className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {category.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {category.providers} providers
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {category.bookings}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    bookings
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Platform Health
          </h3>
          <div className="space-y-3">
            {[
              { metric: 'Average Response Time', value: '245ms', status: 'good' },
              { metric: 'System Uptime', value: '99.9%', status: 'excellent' },
              { metric: 'Error Rate', value: '0.1%', status: 'good' },
              { metric: 'Active Sessions', value: '1,234', status: 'normal' },
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {item.metric}
                </span>
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-900 dark:text-white mr-2">
                    {item.value}
                  </span>
                  <div className={`w-2 h-2 rounded-full ${
                    item.status === 'excellent' ? 'bg-green-500' :
                    item.status === 'good' ? 'bg-blue-500' :
                    item.status === 'warning' ? 'bg-yellow-500' : 'bg-gray-500'
                  }`}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import toast from 'react-hot-toast';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  count?: number;
  action: () => void;
  variant: 'primary' | 'secondary' | 'warning' | 'success';
  disabled?: boolean;
}

interface QuickActionsPanelProps {
  className?: string;
}

export default function QuickActionsPanel({ className = '' }: QuickActionsPanelProps) {
  const [pendingCounts, setPendingCounts] = useState({
    providers: 0,
    customers: 0,
    categories: 0,
    reports: 0,
  });
  const loading = useLoading();

  useEffect(() => {
    fetchPendingCounts();
  }, []);

  const fetchPendingCounts = async () => {
    try {
      loading.startLoading({ message: 'Loading pending items...' });
      const response = await adminApi.dashboard.getPendingCounts();
      
      if (response.success && response.data) {
        setPendingCounts(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch pending counts');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_pending_counts' });
      // Set fallback data for development
      setPendingCounts({
        providers: 5,
        customers: 12,
        categories: 2,
        reports: 3,
      });
    } finally {
      loading.stopLoading();
    }
  };

  const handleProviderApprovals = () => {
    // Navigate to providers page with pending filter
    window.location.href = '/providers?status=pending';
  };

  const handleCustomerVerifications = () => {
    // Navigate to customers page with unverified filter
    window.location.href = '/customers?verified=false';
  };

  const handleCategoryReview = () => {
    // Navigate to categories page
    window.location.href = '/categories';
  };

  const handleSystemReports = () => {
    // Navigate to system reports or open modal
    toast.info('System reports feature coming soon');
  };

  const handleBulkActions = () => {
    toast.info('Bulk actions panel coming soon');
  };

  const handleExportData = () => {
    toast.info('Data export feature coming soon');
  };

  const handleSystemMaintenance = () => {
    // Navigate to settings page
    window.location.href = '/settings';
  };

  const handleEmergencyActions = () => {
    toast.warning('Emergency actions require additional confirmation');
  };

  const quickActions: QuickAction[] = [
    {
      id: 'approve-providers',
      title: 'Approve Providers',
      description: 'Review and approve pending provider registrations',
      count: pendingCounts.providers,
      variant: 'primary',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      action: handleProviderApprovals,
    },
    {
      id: 'verify-customers',
      title: 'Verify Customers',
      description: 'Review customer verification requests',
      count: pendingCounts.customers,
      variant: 'secondary',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      action: handleCustomerVerifications,
    },
    {
      id: 'review-categories',
      title: 'Review Categories',
      description: 'Manage and organize service categories',
      count: pendingCounts.categories,
      variant: 'warning',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      action: handleCategoryReview,
    },
    {
      id: 'system-reports',
      title: 'System Reports',
      description: 'View system health and performance reports',
      count: pendingCounts.reports,
      variant: 'success',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      action: handleSystemReports,
    },
    {
      id: 'bulk-actions',
      title: 'Bulk Actions',
      description: 'Perform bulk operations on multiple items',
      variant: 'secondary',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
        </svg>
      ),
      action: handleBulkActions,
    },
    {
      id: 'export-data',
      title: 'Export Data',
      description: 'Export platform data for analysis',
      variant: 'secondary',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      action: handleExportData,
    },
    {
      id: 'system-maintenance',
      title: 'System Settings',
      description: 'Configure platform settings and maintenance',
      variant: 'warning',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      action: handleSystemMaintenance,
    },
    {
      id: 'emergency-actions',
      title: 'Emergency Actions',
      description: 'Critical system controls and emergency procedures',
      variant: 'warning',
      disabled: true,
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      ),
      action: handleEmergencyActions,
    },
  ];

  const getVariantClasses = (variant: string, disabled?: boolean) => {
    if (disabled) {
      return 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed';
    }

    switch (variant) {
      case 'primary':
        return 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/30 border-blue-200 dark:border-blue-800';
      case 'secondary':
        return 'bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border-gray-200 dark:border-gray-600';
      case 'warning':
        return 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 border-yellow-200 dark:border-yellow-800';
      case 'success':
        return 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-900/30 border-green-200 dark:border-green-800';
      default:
        return 'bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border-gray-200 dark:border-gray-600';
    }
  };

  if (loading.isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Array.from({ length: 8 }).map((_, index) => (
                <div key={index} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Quick Actions
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Common administrative tasks and shortcuts
            </p>
          </div>
          
          <button
            onClick={fetchPendingCounts}
            className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
          >
            Refresh
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <button
              key={action.id}
              onClick={action.action}
              disabled={action.disabled}
              className={`
                relative p-4 rounded-lg border transition-all duration-200 text-left
                ${getVariantClasses(action.variant, action.disabled)}
                ${!action.disabled ? 'transform hover:scale-105 hover:shadow-md' : ''}
              `}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <div className="mr-3">
                      {action.icon}
                    </div>
                    {action.count !== undefined && action.count > 0 && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                        {action.count}
                      </span>
                    )}
                  </div>
                  
                  <h4 className="font-medium text-sm mb-1">
                    {action.title}
                  </h4>
                  
                  <p className="text-xs opacity-75">
                    {action.description}
                  </p>
                </div>
              </div>
              
              {!action.disabled && (
                <div className="absolute top-2 right-2">
                  <svg className="w-4 h-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              )}
            </button>
          ))}
        </div>

        {/* Summary Stats */}
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {pendingCounts.providers}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Pending Providers
              </p>
            </div>
            <div>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {pendingCounts.customers}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Unverified Customers
              </p>
            </div>
            <div>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {pendingCounts.categories}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Category Updates
              </p>
            </div>
            <div>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {pendingCounts.reports}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                System Reports
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

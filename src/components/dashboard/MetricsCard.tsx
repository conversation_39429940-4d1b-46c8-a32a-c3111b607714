import React from 'react';
import { MetricWidget } from '../../types';

interface MetricsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'neutral';
    period: string;
  };
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
  loading?: boolean;
  onClick?: () => void;
  className?: string;
}

const colorClasses = {
  blue: {
    bg: 'bg-blue-100 dark:bg-blue-900',
    icon: 'text-blue-600 dark:text-blue-400',
    trend: 'text-blue-600 dark:text-blue-400',
  },
  green: {
    bg: 'bg-green-100 dark:bg-green-900',
    icon: 'text-green-600 dark:text-green-400',
    trend: 'text-green-600 dark:text-green-400',
  },
  yellow: {
    bg: 'bg-yellow-100 dark:bg-yellow-900',
    icon: 'text-yellow-600 dark:text-yellow-400',
    trend: 'text-yellow-600 dark:text-yellow-400',
  },
  red: {
    bg: 'bg-red-100 dark:bg-red-900',
    icon: 'text-red-600 dark:text-red-400',
    trend: 'text-red-600 dark:text-red-400',
  },
  purple: {
    bg: 'bg-purple-100 dark:bg-purple-900',
    icon: 'text-purple-600 dark:text-purple-400',
    trend: 'text-purple-600 dark:text-purple-400',
  },
  indigo: {
    bg: 'bg-indigo-100 dark:bg-indigo-900',
    icon: 'text-indigo-600 dark:text-indigo-400',
    trend: 'text-indigo-600 dark:text-indigo-400',
  },
};

export default function MetricsCard({
  title,
  value,
  icon,
  trend,
  color = 'blue',
  loading = false,
  onClick,
  className = '',
}: MetricsCardProps) {
  const colors = colorClasses[color];

  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 animate-pulse ${className}`}>
        <div className="flex items-center">
          <div className={`p-3 rounded-full ${colors.bg}`}>
            <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded" />
          </div>
          <div className="ml-4 flex-1">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2" />
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
          </div>
        </div>
        {trend && (
          <div className="mt-4">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3" />
          </div>
        )}
      </div>
    );
  }

  return (
    <div 
      className={`
        bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-all duration-200
        ${onClick ? 'cursor-pointer hover:shadow-lg hover:scale-105' : ''}
        ${className}
      `}
      onClick={onClick}
    >
      <div className="flex items-center">
        <div className={`p-3 rounded-full ${colors.bg}`}>
          <div className={`w-6 h-6 ${colors.icon}`}>
            {icon}
          </div>
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
            {title}
          </p>
          <p className="text-2xl font-semibold text-gray-900 dark:text-white">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
        </div>
      </div>
      
      {trend && (
        <div className="mt-4 flex items-center">
          <div className={`flex items-center ${colors.trend}`}>
            {trend.direction === 'up' && (
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
              </svg>
            )}
            {trend.direction === 'down' && (
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
              </svg>
            )}
            {trend.direction === 'neutral' && (
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            )}
            <span className="text-sm font-medium">
              {trend.value > 0 ? '+' : ''}{trend.value}%
            </span>
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
            vs {trend.period}
          </span>
        </div>
      )}
    </div>
  );
}

// Metrics grid component
interface MetricsGridProps {
  metrics: Array<{
    id: string;
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
    trend?: {
      value: number;
      direction: 'up' | 'down' | 'neutral';
      period: string;
    };
    onClick?: () => void;
  }>;
  loading?: boolean;
  className?: string;
}

export function MetricsGrid({ metrics, loading = false, className = '' }: MetricsGridProps) {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {metrics.map((metric) => (
        <MetricsCard
          key={metric.id}
          title={metric.title}
          value={metric.value}
          icon={metric.icon}
          color={metric.color}
          trend={metric.trend}
          loading={loading}
          onClick={metric.onClick}
        />
      ))}
    </div>
  );
}

// Quick stats component for smaller displays
interface QuickStatsProps {
  stats: Array<{
    label: string;
    value: string | number;
    color?: string;
  }>;
  loading?: boolean;
}

export function QuickStats({ stats, loading = false }: QuickStatsProps) {
  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="text-center animate-pulse">
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-1" />
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <div key={index} className="text-center">
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {stat.label}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

// Metric comparison component
interface MetricComparisonProps {
  current: {
    label: string;
    value: number;
    period: string;
  };
  previous: {
    label: string;
    value: number;
    period: string;
  };
  format?: 'number' | 'currency' | 'percentage';
  className?: string;
}

export function MetricComparison({ 
  current, 
  previous, 
  format = 'number',
  className = '' 
}: MetricComparisonProps) {
  const change = current.value - previous.value;
  const changePercent = previous.value > 0 ? (change / previous.value) * 100 : 0;
  const isPositive = change > 0;
  const isNeutral = change === 0;

  const formatValue = (value: number) => {
    switch (format) {
      case 'currency':
        return `$${value.toLocaleString()}`;
      case 'percentage':
        return `${value.toFixed(1)}%`;
      default:
        return value.toLocaleString();
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${className}`}>
      <div className="space-y-4">
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">{current.label}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatValue(current.value)}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-500">{current.period}</p>
        </div>
        
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">{previous.label}</p>
              <p className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                {formatValue(previous.value)}
              </p>
            </div>
            
            <div className={`text-right ${
              isNeutral ? 'text-gray-500' : isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              <div className="flex items-center">
                {!isNeutral && (
                  <svg 
                    className={`w-4 h-4 mr-1 ${isPositive ? 'rotate-0' : 'rotate-180'}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                  </svg>
                )}
                <span className="text-sm font-medium">
                  {isNeutral ? '0%' : `${isPositive ? '+' : ''}${changePercent.toFixed(1)}%`}
                </span>
              </div>
              <p className="text-xs">
                {isNeutral ? 'No change' : `${isPositive ? '+' : ''}${formatValue(Math.abs(change))}`}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

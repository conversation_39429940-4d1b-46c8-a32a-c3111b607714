import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';

interface HealthMetric {
  id: string;
  name: string;
  value: string | number;
  status: 'excellent' | 'good' | 'warning' | 'critical';
  description: string;
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'stable';
  lastUpdated?: string;
}

interface SystemHealthData {
  overall: 'healthy' | 'warning' | 'critical';
  uptime: string;
  responseTime: number;
  errorRate: number;
  activeUsers: number;
  databaseStatus: 'connected' | 'slow' | 'disconnected';
  cacheStatus: 'connected' | 'slow' | 'disconnected';
  storageUsage: number;
  memoryUsage: number;
  cpuUsage: number;
}

interface SystemHealthIndicatorsProps {
  className?: string;
  compact?: boolean;
}

export default function SystemHealthIndicators({ 
  className = '', 
  compact = false 
}: SystemHealthIndicatorsProps) {
  const [healthData, setHealthData] = useState<SystemHealthData | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const loading = useLoading();

  useEffect(() => {
    fetchHealthData();
    
    // Set up auto-refresh every 30 seconds
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        fetchHealthData();
        setLastRefresh(new Date());
      }, 30000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const fetchHealthData = async () => {
    try {
      loading.startLoading({ message: 'Checking system health...' });
      const response = await adminApi.system.getSystemHealth();
      
      if (response.success && response.data) {
        setHealthData(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch system health');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_system_health' });
      // Set fallback data for development
      setHealthData({
        overall: 'healthy',
        uptime: '15 days, 3 hours',
        responseTime: 245,
        errorRate: 0.1,
        activeUsers: 1234,
        databaseStatus: 'connected',
        cacheStatus: 'connected',
        storageUsage: 65,
        memoryUsage: 78,
        cpuUsage: 45,
      });
    } finally {
      loading.stopLoading();
    }
  };

  const getHealthMetrics = (data: SystemHealthData): HealthMetric[] => [
    {
      id: 'uptime',
      name: 'System Uptime',
      value: data.uptime,
      status: 'excellent',
      description: 'System has been running continuously',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      trend: 'stable',
    },
    {
      id: 'response-time',
      name: 'Response Time',
      value: `${data.responseTime}ms`,
      status: data.responseTime < 300 ? 'excellent' : data.responseTime < 500 ? 'good' : 'warning',
      description: 'Average API response time',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      trend: data.responseTime < 300 ? 'down' : 'stable',
    },
    {
      id: 'error-rate',
      name: 'Error Rate',
      value: `${data.errorRate}%`,
      status: data.errorRate < 1 ? 'excellent' : data.errorRate < 5 ? 'good' : 'warning',
      description: 'Percentage of failed requests',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      ),
      trend: 'down',
    },
    {
      id: 'active-users',
      name: 'Active Users',
      value: data.activeUsers.toLocaleString(),
      status: 'good',
      description: 'Currently active user sessions',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      trend: 'up',
    },
    {
      id: 'database',
      name: 'Database',
      value: data.databaseStatus,
      status: data.databaseStatus === 'connected' ? 'excellent' : data.databaseStatus === 'slow' ? 'warning' : 'critical',
      description: 'Database connection status',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
        </svg>
      ),
      trend: 'stable',
    },
    {
      id: 'cache',
      name: 'Cache',
      value: data.cacheStatus,
      status: data.cacheStatus === 'connected' ? 'excellent' : data.cacheStatus === 'slow' ? 'warning' : 'critical',
      description: 'Redis cache status',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      ),
      trend: 'stable',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900';
      case 'good':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900';
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900';
      case 'critical':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700';
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'up':
        return (
          <svg className="w-3 h-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
          </svg>
        );
      case 'down':
        return (
          <svg className="w-3 h-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
          </svg>
        );
      case 'stable':
        return (
          <svg className="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        );
      default:
        return null;
    }
  };

  const getOverallStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            All Systems Operational
          </span>
        );
      case 'warning':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            System Issues Detected
          </span>
        );
      case 'critical':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            Critical System Issues
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            Status Unknown
          </span>
        );
    }
  };

  if (loading.isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!healthData) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            Unable to load system health data
          </p>
        </div>
      </div>
    );
  }

  const metrics = getHealthMetrics(healthData);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              System Health
            </h3>
            {autoRefresh && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            {getOverallStatusBadge(healthData.overall)}
            
            <div className="flex items-center">
              <input
                id="health-auto-refresh"
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="health-auto-refresh" className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                Auto-refresh
              </label>
            </div>
            
            <button
              onClick={() => {
                fetchHealthData();
                setLastRefresh(new Date());
              }}
              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
            >
              Refresh
            </button>
          </div>
        </div>

        {/* Health Metrics Grid */}
        <div className={`grid gap-4 ${compact ? 'grid-cols-2 md:grid-cols-3' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}`}>
          {metrics.map((metric) => (
            <div
              key={metric.id}
              className={`p-4 rounded-lg border ${getStatusColor(metric.status)}`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className="mr-2">
                    {metric.icon}
                  </div>
                  <h4 className="font-medium text-sm">
                    {metric.name}
                  </h4>
                </div>
                {metric.trend && getTrendIcon(metric.trend)}
              </div>
              
              <p className="text-lg font-semibold mb-1">
                {metric.value}
              </p>
              
              <p className="text-xs opacity-75">
                {metric.description}
              </p>
            </div>
          ))}
        </div>

        {/* Resource Usage */}
        {!compact && (
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
              Resource Usage
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { name: 'Storage', value: healthData.storageUsage, max: 100, unit: '%' },
                { name: 'Memory', value: healthData.memoryUsage, max: 100, unit: '%' },
                { name: 'CPU', value: healthData.cpuUsage, max: 100, unit: '%' },
              ].map((resource) => (
                <div key={resource.name} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">{resource.name}</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {resource.value}{resource.unit}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        resource.value < 70 ? 'bg-green-500' :
                        resource.value < 85 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${(resource.value / resource.max) * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

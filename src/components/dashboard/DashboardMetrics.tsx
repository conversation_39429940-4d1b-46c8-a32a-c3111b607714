import React, { useEffect, useState } from 'react';
import { MetricsGrid } from './MetricsCard';
import { adminApi } from '../../services/adminApi';
import { DashboardMetrics as DashboardMetricsType } from '../../types';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';

export default function DashboardMetrics() {
  const [metrics, setMetrics] = useState<DashboardMetricsType | null>(null);
  const loading = useLoading('Loading dashboard metrics...');

  useEffect(() => {
    fetchMetrics();
  }, []);

  const fetchMetrics = async () => {
    try {
      loading.startLoading();
      const response = await adminApi.dashboard.getMetrics();
      
      if (response.success) {
        setMetrics(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch metrics');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_dashboard_metrics' });
      // Set fallback data for development
      setMetrics({
        totalProviders: 0,
        verifiedProviders: 0,
        pendingProviders: 0,
        totalCustomers: 0,
        verifiedCustomers: 0,
        totalBookings: 0,
        completedBookings: 0,
        totalRevenue: 0,
        growthMetrics: {
          providersGrowth: 0,
          customersGrowth: 0,
          bookingsGrowth: 0,
          revenueGrowth: 0,
        },
      });
    } finally {
      loading.stopLoading();
    }
  };

  const getMetricsData = () => {
    if (!metrics) return [];

    return [
      {
        id: 'total-providers',
        title: 'Total Providers',
        value: metrics.totalProviders,
        icon: (
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        ),
        color: 'blue' as const,
        trend: {
          value: metrics.growthMetrics.providersGrowth,
          direction: metrics.growthMetrics.providersGrowth > 0 ? 'up' as const : 
                   metrics.growthMetrics.providersGrowth < 0 ? 'down' as const : 'neutral' as const,
          period: 'last month',
        },
        onClick: () => {
          // Navigate to providers page
          console.log('Navigate to providers');
        },
      },
      {
        id: 'total-customers',
        title: 'Total Customers',
        value: metrics.totalCustomers,
        icon: (
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
        ),
        color: 'green' as const,
        trend: {
          value: metrics.growthMetrics.customersGrowth,
          direction: metrics.growthMetrics.customersGrowth > 0 ? 'up' as const : 
                   metrics.growthMetrics.customersGrowth < 0 ? 'down' as const : 'neutral' as const,
          period: 'last month',
        },
        onClick: () => {
          // Navigate to customers page
          console.log('Navigate to customers');
        },
      },
      {
        id: 'pending-approvals',
        title: 'Pending Approvals',
        value: metrics.pendingProviders,
        icon: (
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        ),
        color: metrics.pendingProviders > 10 ? 'red' : metrics.pendingProviders > 5 ? 'yellow' : 'green',
        onClick: () => {
          // Navigate to pending providers
          console.log('Navigate to pending providers');
        },
      },
      {
        id: 'total-bookings',
        title: 'Total Bookings',
        value: metrics.totalBookings,
        icon: (
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        ),
        color: 'purple' as const,
        trend: {
          value: metrics.growthMetrics.bookingsGrowth,
          direction: metrics.growthMetrics.bookingsGrowth > 0 ? 'up' as const : 
                   metrics.growthMetrics.bookingsGrowth < 0 ? 'down' as const : 'neutral' as const,
          period: 'last month',
        },
        onClick: () => {
          // Navigate to bookings
          console.log('Navigate to bookings');
        },
      },
    ];
  };

  return (
    <div className="space-y-6">
      {/* Main Metrics Grid */}
      <MetricsGrid 
        metrics={getMetricsData()} 
        loading={loading.isLoading}
      />

      {/* Additional Metrics Row */}
      {metrics && !loading.isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Verification Rate */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Provider Verification Rate
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {metrics.totalProviders > 0 
                    ? Math.round((metrics.verifiedProviders / metrics.totalProviders) * 100)
                    : 0}%
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-500">
                  {metrics.verifiedProviders} of {metrics.totalProviders} verified
                </p>
              </div>
              <div className="w-16 h-16">
                <svg className="w-full h-full" viewBox="0 0 36 36">
                  <path
                    className="text-gray-200 dark:text-gray-700"
                    stroke="currentColor"
                    strokeWidth="3"
                    fill="none"
                    d="M18 2.0845
                      a 15.9155 15.9155 0 0 1 0 31.831
                      a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    className="text-blue-600"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeDasharray={`${metrics.totalProviders > 0 ? (metrics.verifiedProviders / metrics.totalProviders) * 100 : 0}, 100`}
                    strokeLinecap="round"
                    fill="none"
                    d="M18 2.0845
                      a 15.9155 15.9155 0 0 1 0 31.831
                      a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Booking Completion Rate */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Booking Completion Rate
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {metrics.totalBookings > 0 
                    ? Math.round((metrics.completedBookings / metrics.totalBookings) * 100)
                    : 0}%
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-500">
                  {metrics.completedBookings} of {metrics.totalBookings} completed
                </p>
              </div>
              <div className="w-16 h-16">
                <svg className="w-full h-full" viewBox="0 0 36 36">
                  <path
                    className="text-gray-200 dark:text-gray-700"
                    stroke="currentColor"
                    strokeWidth="3"
                    fill="none"
                    d="M18 2.0845
                      a 15.9155 15.9155 0 0 1 0 31.831
                      a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    className="text-green-600"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeDasharray={`${metrics.totalBookings > 0 ? (metrics.completedBookings / metrics.totalBookings) * 100 : 0}, 100`}
                    strokeLinecap="round"
                    fill="none"
                    d="M18 2.0845
                      a 15.9155 15.9155 0 0 1 0 31.831
                      a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Revenue */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
                <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Revenue
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  ${metrics.totalRevenue.toLocaleString()}
                </p>
                {metrics.growthMetrics.revenueGrowth !== 0 && (
                  <div className={`flex items-center mt-1 ${
                    metrics.growthMetrics.revenueGrowth > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <svg className={`w-4 h-4 mr-1 ${metrics.growthMetrics.revenueGrowth < 0 ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                    </svg>
                    <span className="text-sm font-medium">
                      {metrics.growthMetrics.revenueGrowth > 0 ? '+' : ''}{metrics.growthMetrics.revenueGrowth}%
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Refresh Button */}
      <div className="flex justify-end">
        <button
          onClick={fetchMetrics}
          disabled={loading.isLoading}
          className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className={`w-4 h-4 mr-2 ${loading.isLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {loading.isLoading ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>
    </div>
  );
}

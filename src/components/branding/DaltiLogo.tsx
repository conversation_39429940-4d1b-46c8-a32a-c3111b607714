import React from 'react';

interface DaltiLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'full' | 'icon' | 'text';
  className?: string;
  showAdmin?: boolean;
}

export default function DaltiLogo({ 
  size = 'md', 
  variant = 'full', 
  className = '',
  showAdmin = false 
}: DaltiLogoProps) {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-12',
    xl: 'h-16'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl'
  };

  const DaltiIcon = () => (
    <svg 
      className={`${sizeClasses[size]} w-auto`}
      viewBox="0 0 40 40" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Dal<PERSON> Logo Icon - Modern, Professional Design */}
      <defs>
        <linearGradient id="dalti-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#19727F" />
          <stop offset="100%" stopColor="#155b68" />
        </linearGradient>
        <linearGradient id="dalti-accent" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#10b981" />
          <stop offset="100%" stopColor="#059669" />
        </linearGradient>
      </defs>
      
      {/* Main Circle Background */}
      <circle cx="20" cy="20" r="18" fill="url(#dalti-gradient)" />
      
      {/* Inner Design - Stylized 'D' */}
      <path 
        d="M12 10 L12 30 L20 30 C26 30 30 26 30 20 C30 14 26 10 20 10 L12 10 Z M16 14 L20 14 C23.5 14 26 16.5 26 20 C26 23.5 23.5 26 20 26 L16 26 L16 14 Z" 
        fill="white"
      />
      
      {/* Accent Dot */}
      <circle cx="28" cy="12" r="3" fill="url(#dalti-accent)" />
    </svg>
  );

  const DaltiText = () => (
    <span className={`font-bold ${textSizeClasses[size]} text-gray-900 dark:text-white`}>
      Dalti
      {showAdmin && (
        <span className="ml-2 text-sm font-medium text-brand-600 dark:text-brand-400 bg-brand-100 dark:bg-brand-900/30 px-2 py-1 rounded">
          Admin
        </span>
      )}
    </span>
  );

  if (variant === 'icon') {
    return (
      <div className={`flex items-center ${className}`}>
        <DaltiIcon />
      </div>
    );
  }

  if (variant === 'text') {
    return (
      <div className={`flex items-center ${className}`}>
        <DaltiText />
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <DaltiIcon />
      <DaltiText />
    </div>
  );
}

// Alternative logo variations for different contexts
export function DaltiLogoMinimal({ size = 'md', className = '' }: Pick<DaltiLogoProps, 'size' | 'className'>) {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-12',
    xl: 'h-16'
  };

  return (
    <svg 
      className={`${sizeClasses[size]} w-auto ${className}`}
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="minimal-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#19727F" />
          <stop offset="100%" stopColor="#155b68" />
        </linearGradient>
      </defs>
      
      {/* Simplified D shape */}
      <path 
        d="M3 3 L3 21 L12 21 C17.5 21 21 17.5 21 12 C21 6.5 17.5 3 12 3 L3 3 Z M6 6 L12 6 C15.5 6 18 8.5 18 12 C18 15.5 15.5 18 12 18 L6 18 L6 6 Z" 
        fill="url(#minimal-gradient)"
      />
    </svg>
  );
}

export function DaltiLogoDark({ size = 'md', variant = 'full', className = '', showAdmin = false }: DaltiLogoProps) {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-12',
    xl: 'h-16'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl'
  };

  const DaltiIconDark = () => (
    <svg 
      className={`${sizeClasses[size]} w-auto`}
      viewBox="0 0 40 40" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="dalti-gradient-dark" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#33b1bc" />
          <stop offset="100%" stopColor="#19727F" />
        </linearGradient>
        <linearGradient id="dalti-accent-dark" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#34d399" />
          <stop offset="100%" stopColor="#10b981" />
        </linearGradient>
      </defs>
      
      {/* Main Circle Background */}
      <circle cx="20" cy="20" r="18" fill="url(#dalti-gradient-dark)" />
      
      {/* Inner Design - Stylized 'D' */}
      <path 
        d="M12 10 L12 30 L20 30 C26 30 30 26 30 20 C30 14 26 10 20 10 L12 10 Z M16 14 L20 14 C23.5 14 26 16.5 26 20 C26 23.5 23.5 26 20 26 L16 26 L16 14 Z" 
        fill="white"
      />
      
      {/* Accent Dot */}
      <circle cx="28" cy="12" r="3" fill="url(#dalti-accent-dark)" />
    </svg>
  );

  const DaltiTextDark = () => (
    <span className={`font-bold ${textSizeClasses[size]} text-white`}>
      Dalti
      {showAdmin && (
        <span className="ml-2 text-sm font-medium text-brand-200 bg-brand-800/30 px-2 py-1 rounded">
          Admin
        </span>
      )}
    </span>
  );

  if (variant === 'icon') {
    return (
      <div className={`flex items-center ${className}`}>
        <DaltiIconDark />
      </div>
    );
  }

  if (variant === 'text') {
    return (
      <div className={`flex items-center ${className}`}>
        <DaltiTextDark />
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <DaltiIconDark />
      <DaltiTextDark />
    </div>
  );
}

// Animated logo for loading states
export function DaltiLogoAnimated({ size = 'md', className = '' }: Pick<DaltiLogoProps, 'size' | 'className'>) {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-12',
    xl: 'h-16'
  };

  return (
    <div className={`flex items-center ${className}`}>
      <svg 
        className={`${sizeClasses[size]} w-auto animate-pulse`}
        viewBox="0 0 40 40" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="dalti-gradient-animated" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#19727F">
              <animate attributeName="stop-color"
                values="#19727F;#155b68;#19727F"
                dur="2s"
                repeatCount="indefinite" />
            </stop>
            <stop offset="100%" stopColor="#155b68">
              <animate attributeName="stop-color"
                values="#155b68;#19727F;#155b68"
                dur="2s"
                repeatCount="indefinite" />
            </stop>
          </linearGradient>
          <linearGradient id="dalti-accent-animated" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#10b981">
              <animate attributeName="stop-color" 
                values="#10b981;#059669;#10b981" 
                dur="1.5s" 
                repeatCount="indefinite" />
            </stop>
            <stop offset="100%" stopColor="#059669">
              <animate attributeName="stop-color" 
                values="#059669;#10b981;#059669" 
                dur="1.5s" 
                repeatCount="indefinite" />
            </stop>
          </linearGradient>
        </defs>
        
        {/* Main Circle Background */}
        <circle cx="20" cy="20" r="18" fill="url(#dalti-gradient-animated)" />
        
        {/* Inner Design - Stylized 'D' */}
        <path 
          d="M12 10 L12 30 L20 30 C26 30 30 26 30 20 C30 14 26 10 20 10 L12 10 Z M16 14 L20 14 C23.5 14 26 16.5 26 20 C26 23.5 23.5 26 20 26 L16 26 L16 14 Z" 
          fill="white"
        />
        
        {/* Accent Dot */}
        <circle cx="28" cy="12" r="3" fill="url(#dalti-accent-animated)">
          <animateTransform
            attributeName="transform"
            type="rotate"
            values="0 28 12;360 28 12"
            dur="3s"
            repeatCount="indefinite"
          />
        </circle>
      </svg>
    </div>
  );
}

// Logo for favicons and small spaces
export function DaltiFavicon() {
  return (
    <svg 
      width="32" 
      height="32" 
      viewBox="0 0 32 32" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="favicon-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#19727F" />
          <stop offset="100%" stopColor="#155b68" />
        </linearGradient>
      </defs>
      
      {/* Simplified for small size */}
      <rect width="32" height="32" rx="8" fill="url(#favicon-gradient)" />
      <path 
        d="M8 8 L8 24 L16 24 C20 24 24 20 24 16 C24 12 20 8 16 8 L8 8 Z M12 12 L16 12 C18 12 20 14 20 16 C20 18 18 20 16 20 L12 20 L12 12 Z" 
        fill="white"
      />
      <circle cx="22" cy="10" r="2" fill="#10b981" />
    </svg>
  );
}

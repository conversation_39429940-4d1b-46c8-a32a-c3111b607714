import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import toast from 'react-hot-toast';

interface MaintenanceTask {
  id: string;
  name: string;
  description: string;
  type: 'cache_clear' | 'database_optimize' | 'log_cleanup' | 'service_restart' | 'health_check';
  status: 'idle' | 'running' | 'completed' | 'failed';
  lastRun?: string;
  duration?: number;
  automated: boolean;
  schedule?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface SystemStatus {
  maintenanceMode: boolean;
  lastMaintenance: string;
  uptime: number;
  systemLoad: number;
  diskSpace: {
    total: number;
    used: number;
    available: number;
  };
  services: {
    name: string;
    status: 'running' | 'stopped' | 'error';
    uptime: number;
  }[];
}

interface SystemMaintenanceDashboardProps {
  className?: string;
}

export default function SystemMaintenanceDashboard({ className = '' }: SystemMaintenanceDashboardProps) {
  const [maintenanceTasks, setMaintenanceTasks] = useState<MaintenanceTask[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [runningTasks, setRunningTasks] = useState<Set<string>>(new Set());
  const loading = useLoading();

  useEffect(() => {
    fetchMaintenanceData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchMaintenanceData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchMaintenanceData = async () => {
    try {
      const [tasksResponse, statusResponse] = await Promise.all([
        adminApi.system.getMaintenanceTasks?.(),
        adminApi.system.getSystemStatus?.(),
      ]);

      if (tasksResponse?.success) {
        setMaintenanceTasks(tasksResponse.data);
      } else {
        // Fallback data for development
        setMaintenanceTasks([
          {
            id: '1',
            name: 'Clear Application Cache',
            description: 'Clear Redis cache and application-level caches',
            type: 'cache_clear',
            status: 'idle',
            lastRun: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            duration: 30,
            automated: true,
            schedule: 'Daily at 3:00 AM',
            priority: 'medium',
          },
          {
            id: '2',
            name: 'Database Optimization',
            description: 'Optimize database tables and rebuild indexes',
            type: 'database_optimize',
            status: 'idle',
            lastRun: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            duration: 900,
            automated: true,
            schedule: 'Weekly on Sunday at 2:00 AM',
            priority: 'high',
          },
          {
            id: '3',
            name: 'Log Cleanup',
            description: 'Archive and clean up old system logs',
            type: 'log_cleanup',
            status: 'completed',
            lastRun: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
            duration: 120,
            automated: true,
            schedule: 'Daily at 1:00 AM',
            priority: 'low',
          },
          {
            id: '4',
            name: 'Service Health Check',
            description: 'Comprehensive health check of all system services',
            type: 'health_check',
            status: 'idle',
            lastRun: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            duration: 60,
            automated: true,
            schedule: 'Every 30 minutes',
            priority: 'critical',
          },
          {
            id: '5',
            name: 'Restart Payment Service',
            description: 'Restart payment processing service',
            type: 'service_restart',
            status: 'idle',
            automated: false,
            priority: 'high',
          },
        ]);
      }

      if (statusResponse?.success) {
        setSystemStatus(statusResponse.data);
      } else {
        // Fallback data for development
        setSystemStatus({
          maintenanceMode: false,
          lastMaintenance: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          uptime: 99.8,
          systemLoad: 45.2,
          diskSpace: {
            total: 1000 * 1024 * 1024 * 1024, // 1TB
            used: 720 * 1024 * 1024 * 1024,   // 720GB
            available: 280 * 1024 * 1024 * 1024, // 280GB
          },
          services: [
            { name: 'API Server', status: 'running', uptime: 99.9 },
            { name: 'Database', status: 'running', uptime: 99.95 },
            { name: 'Redis Cache', status: 'running', uptime: 99.8 },
            { name: 'File Storage', status: 'running', uptime: 98.5 },
            { name: 'Email Service', status: 'running', uptime: 99.2 },
            { name: 'Payment Gateway', status: 'error', uptime: 95.1 },
          ],
        });
      }
    } catch (error) {
      handleError(error, { action: 'fetch_maintenance_data', silent: true });
    }
  };

  const runMaintenanceTask = async (taskId: string) => {
    const task = maintenanceTasks.find(t => t.id === taskId);
    if (!task) return;

    try {
      setRunningTasks(prev => new Set(prev).add(taskId));
      setMaintenanceTasks(prev => prev.map(t => 
        t.id === taskId ? { ...t, status: 'running' } : t
      ));

      const response = await adminApi.system.runMaintenanceTask?.(taskId);
      
      if (response?.success) {
        toast.success(`${task.name} completed successfully`);
        setMaintenanceTasks(prev => prev.map(t => 
          t.id === taskId ? { 
            ...t, 
            status: 'completed', 
            lastRun: new Date().toISOString(),
            duration: response.data?.duration || t.duration 
          } : t
        ));
      } else {
        throw new Error(response?.message || 'Task failed');
      }
    } catch (error) {
      handleError(error, { action: 'run_maintenance_task' });
      setMaintenanceTasks(prev => prev.map(t => 
        t.id === taskId ? { ...t, status: 'failed' } : t
      ));
    } finally {
      setRunningTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });
    }
  };

  const toggleMaintenanceMode = async () => {
    if (!systemStatus) return;

    try {
      const newMode = !systemStatus.maintenanceMode;
      const response = await adminApi.system.setMaintenanceMode?.(newMode);
      
      if (response?.success) {
        setSystemStatus(prev => prev ? { ...prev, maintenanceMode: newMode } : null);
        toast.success(`Maintenance mode ${newMode ? 'enabled' : 'disabled'}`);
      }
    } catch (error) {
      handleError(error, { action: 'toggle_maintenance_mode' });
    }
  };

  const restartService = async (serviceName: string) => {
    if (!confirm(`Are you sure you want to restart ${serviceName}? This may cause temporary service interruption.`)) {
      return;
    }

    try {
      const response = await adminApi.system.restartService(serviceName);
      if (response.success) {
        toast.success(`${serviceName} restart initiated`);
        fetchMaintenanceData(); // Refresh status
      }
    } catch (error) {
      handleError(error, { action: 'restart_service' });
    }
  };

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'cache_clear':
        return '🗑️';
      case 'database_optimize':
        return '🗃️';
      case 'log_cleanup':
        return '📋';
      case 'service_restart':
        return '🔄';
      case 'health_check':
        return '🏥';
      default:
        return '⚙️';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return (
          <svg className="w-4 h-4 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        );
      case 'completed':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'failed':
        return (
          <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              System Maintenance
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Manage system maintenance tasks and monitor system health
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {systemStatus && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700 dark:text-gray-300">Maintenance Mode:</span>
                <button
                  onClick={toggleMaintenanceMode}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    systemStatus.maintenanceMode ? 'bg-red-600' : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      systemStatus.maintenanceMode ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            )}
            
            <button
              onClick={fetchMaintenanceData}
              className="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* System Status Overview */}
        {systemStatus && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <div className="flex items-center">
                <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900">
                  <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400">System Uptime</p>
                  <p className="text-lg font-semibold text-blue-900 dark:text-blue-100">{systemStatus.uptime.toFixed(2)}%</p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <div className="flex items-center">
                <div className="p-2 rounded-full bg-green-100 dark:bg-green-900">
                  <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-600 dark:text-green-400">System Load</p>
                  <p className="text-lg font-semibold text-green-900 dark:text-green-100">{systemStatus.systemLoad.toFixed(1)}%</p>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <div className="flex items-center">
                <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900">
                  <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Disk Usage</p>
                  <p className="text-lg font-semibold text-purple-900 dark:text-purple-100">
                    {((systemStatus.diskSpace.used / systemStatus.diskSpace.total) * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
              <div className="flex items-center">
                <div className="p-2 rounded-full bg-yellow-100 dark:bg-yellow-900">
                  <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Last Maintenance</p>
                  <p className="text-sm font-semibold text-yellow-900 dark:text-yellow-100">
                    {new Date(systemStatus.lastMaintenance).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Maintenance Tasks */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Maintenance Tasks
          </h3>
          <div className="space-y-4">
            {maintenanceTasks.map((task) => (
              <div key={task.id} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <span className="text-2xl">{getTaskIcon(task.type)}</span>
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {task.name}
                        </h4>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getPriorityColor(task.priority)}`}>
                          {task.priority}
                        </span>
                        {task.automated && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            Automated
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {task.description}
                      </p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                        {task.lastRun && (
                          <span>Last run: {new Date(task.lastRun).toLocaleString()}</span>
                        )}
                        {task.duration && (
                          <span>Duration: {formatDuration(task.duration)}</span>
                        )}
                        {task.schedule && (
                          <span>Schedule: {task.schedule}</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    {getStatusIcon(task.status)}
                    <button
                      onClick={() => runMaintenanceTask(task.id)}
                      disabled={runningTasks.has(task.id) || task.status === 'running'}
                      className="px-3 py-1 text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {runningTasks.has(task.id) || task.status === 'running' ? 'Running...' : 'Run Now'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Services Status */}
        {systemStatus && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              System Services
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {systemStatus.services.map((service) => (
                <div key={service.name} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {service.name}
                    </h4>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      service.status === 'running'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : service.status === 'stopped'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {service.status}
                    </span>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Uptime</span>
                      <span className="text-gray-900 dark:text-white font-medium">
                        {service.uptime.toFixed(2)}%
                      </span>
                    </div>

                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          service.uptime > 99 ? 'bg-green-500' :
                          service.uptime > 95 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${service.uptime}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                    <button
                      onClick={() => restartService(service.name)}
                      disabled={service.status === 'running'}
                      className="w-full px-3 py-1 text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Restart Service
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Disk Space Details */}
        {systemStatus && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Storage Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {formatBytes(systemStatus.diskSpace.total)}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Space</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                  {formatBytes(systemStatus.diskSpace.used)}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Used Space</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {formatBytes(systemStatus.diskSpace.available)}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Available Space</p>
              </div>
            </div>

            <div className="mt-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-900 dark:text-white">Disk Usage</span>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {((systemStatus.diskSpace.used / systemStatus.diskSpace.total) * 100).toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-3">
                <div
                  className={`h-3 rounded-full ${
                    (systemStatus.diskSpace.used / systemStatus.diskSpace.total) > 0.9 ? 'bg-red-500' :
                    (systemStatus.diskSpace.used / systemStatus.diskSpace.total) > 0.8 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${(systemStatus.diskSpace.used / systemStatus.diskSpace.total) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* Maintenance Mode Warning */}
        {systemStatus?.maintenanceMode && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-red-500 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div>
                <h4 className="font-medium text-red-900 dark:text-red-100">
                  Maintenance Mode Active
                </h4>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  The system is currently in maintenance mode. Users may experience limited functionality or service interruptions.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

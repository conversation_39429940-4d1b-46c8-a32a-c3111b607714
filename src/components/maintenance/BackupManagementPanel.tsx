import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { formatDistanceToNow } from 'date-fns';
import toast from 'react-hot-toast';

interface BackupRecord {
  id: string;
  type: 'full' | 'incremental' | 'database' | 'files';
  status: 'completed' | 'in_progress' | 'failed' | 'scheduled';
  startTime: string;
  endTime?: string;
  size: number;
  location: string;
  description?: string;
  metadata: {
    tables?: string[];
    fileCount?: number;
    compressionRatio?: number;
    checksum?: string;
  };
}

interface BackupSchedule {
  id: string;
  name: string;
  type: 'full' | 'incremental' | 'database' | 'files';
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string;
  enabled: boolean;
  retentionDays: number;
  lastRun?: string;
  nextRun: string;
}

interface BackupManagementPanelProps {
  className?: string;
}

export default function BackupManagementPanel({ className = '' }: BackupManagementPanelProps) {
  const [backupHistory, setBackupHistory] = useState<BackupRecord[]>([]);
  const [backupSchedules, setBackupSchedules] = useState<BackupSchedule[]>([]);
  const [activeTab, setActiveTab] = useState<'history' | 'schedules' | 'create'>('history');
  const [selectedBackup, setSelectedBackup] = useState<BackupRecord | null>(null);
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);
  const loading = useLoading();

  useEffect(() => {
    fetchBackupData();
  }, []);

  const fetchBackupData = async () => {
    try {
      loading.startLoading({ message: 'Loading backup data...' });
      
      const [historyResponse, schedulesResponse] = await Promise.all([
        adminApi.system.getBackupHistory(),
        adminApi.system.getBackupSchedules?.(),
      ]);

      if (historyResponse.success) {
        setBackupHistory(historyResponse.data);
      } else {
        // Fallback data for development
        setBackupHistory([
          {
            id: '1',
            type: 'full',
            status: 'completed',
            startTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            endTime: new Date(Date.now() - 24 * 60 * 60 * 1000 + 45 * 60 * 1000).toISOString(),
            size: 2.5 * 1024 * 1024 * 1024, // 2.5 GB
            location: 's3://dalti-backups/full/2024-01-15-full-backup.tar.gz',
            description: 'Scheduled full system backup',
            metadata: {
              tables: ['users', 'providers', 'customers', 'bookings', 'categories'],
              fileCount: 15420,
              compressionRatio: 0.65,
              checksum: 'sha256:a1b2c3d4e5f6...',
            },
          },
          {
            id: '2',
            type: 'database',
            status: 'completed',
            startTime: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
            endTime: new Date(Date.now() - 12 * 60 * 60 * 1000 + 15 * 60 * 1000).toISOString(),
            size: 850 * 1024 * 1024, // 850 MB
            location: 's3://dalti-backups/database/2024-01-15-db-backup.sql.gz',
            description: 'Daily database backup',
            metadata: {
              tables: ['users', 'providers', 'customers', 'bookings'],
              compressionRatio: 0.78,
              checksum: 'sha256:f6e5d4c3b2a1...',
            },
          },
          {
            id: '3',
            type: 'incremental',
            status: 'in_progress',
            startTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            size: 0,
            location: 's3://dalti-backups/incremental/2024-01-15-incremental.tar.gz',
            description: 'Incremental backup in progress',
            metadata: {
              fileCount: 0,
            },
          },
          {
            id: '4',
            type: 'files',
            status: 'failed',
            startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            endTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 10 * 60 * 1000).toISOString(),
            size: 0,
            location: 's3://dalti-backups/files/2024-01-13-files-backup.tar.gz',
            description: 'File backup failed due to storage quota',
            metadata: {
              fileCount: 0,
            },
          },
        ]);
      }

      if (schedulesResponse?.success) {
        setBackupSchedules(schedulesResponse.data);
      } else {
        // Fallback data for development
        setBackupSchedules([
          {
            id: '1',
            name: 'Daily Database Backup',
            type: 'database',
            frequency: 'daily',
            time: '02:00',
            enabled: true,
            retentionDays: 30,
            lastRun: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
            nextRun: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '2',
            name: 'Weekly Full Backup',
            type: 'full',
            frequency: 'weekly',
            time: '01:00',
            enabled: true,
            retentionDays: 90,
            lastRun: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            nextRun: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '3',
            name: 'Hourly Incremental',
            type: 'incremental',
            frequency: 'daily',
            time: '00:00',
            enabled: false,
            retentionDays: 7,
            nextRun: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
          },
        ]);
      }
    } catch (error) {
      handleError(error, { action: 'fetch_backup_data' });
    } finally {
      loading.stopLoading();
    }
  };

  const createBackup = async (type: 'full' | 'incremental' | 'database' | 'files', description?: string) => {
    try {
      setIsCreatingBackup(true);
      const response = await adminApi.system.createBackup({ type, description });
      
      if (response.success) {
        toast.success('Backup started successfully');
        fetchBackupData(); // Refresh the list
      } else {
        throw new Error(response.message || 'Failed to start backup');
      }
    } catch (error) {
      handleError(error, { action: 'create_backup' });
    } finally {
      setIsCreatingBackup(false);
    }
  };

  const downloadBackup = async (backupId: string) => {
    try {
      const response = await adminApi.system.downloadBackup?.(backupId);
      if (response?.success) {
        // Create download link
        const blob = new Blob([response.data], { type: 'application/octet-stream' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `backup-${backupId}.tar.gz`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      handleError(error, { action: 'download_backup' });
    }
  };

  const deleteBackup = async (backupId: string) => {
    if (!confirm('Are you sure you want to delete this backup? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await adminApi.system.deleteBackup?.(backupId);
      if (response?.success) {
        toast.success('Backup deleted successfully');
        setBackupHistory(prev => prev.filter(backup => backup.id !== backupId));
      }
    } catch (error) {
      handleError(error, { action: 'delete_backup' });
    }
  };

  const toggleSchedule = async (scheduleId: string, enabled: boolean) => {
    try {
      const response = await adminApi.system.updateBackupSchedule?.(scheduleId, { enabled });
      if (response?.success) {
        setBackupSchedules(prev => prev.map(schedule => 
          schedule.id === scheduleId ? { ...schedule, enabled } : schedule
        ));
        toast.success(`Schedule ${enabled ? 'enabled' : 'disabled'} successfully`);
      }
    } catch (error) {
      handleError(error, { action: 'toggle_schedule' });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'in_progress':
        return (
          <svg className="w-5 h-5 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        );
      case 'failed':
        return (
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'scheduled':
        return (
          <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'full':
        return '🗄️';
      case 'incremental':
        return '📈';
      case 'database':
        return '🗃️';
      case 'files':
        return '📁';
      default:
        return '💾';
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    if (!endTime) return 'In progress...';
    const start = new Date(startTime);
    const end = new Date(endTime);
    const duration = end.getTime() - start.getTime();
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  const tabs = [
    { id: 'history', name: 'Backup History', icon: '📋' },
    { id: 'schedules', name: 'Schedules', icon: '⏰' },
    { id: 'create', name: 'Create Backup', icon: '➕' },
  ];

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Backup Management
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Manage system backups, schedules, and data recovery
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={fetchBackupData}
              disabled={loading.isLoading}
              className="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }
              `}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'history' && (
          <BackupHistoryTab
            backups={backupHistory}
            onDownload={downloadBackup}
            onDelete={deleteBackup}
            onViewDetails={setSelectedBackup}
            getStatusIcon={getStatusIcon}
            getTypeIcon={getTypeIcon}
            formatBytes={formatBytes}
            formatDuration={formatDuration}
          />
        )}
        
        {activeTab === 'schedules' && (
          <BackupSchedulesTab
            schedules={backupSchedules}
            onToggleSchedule={toggleSchedule}
            getTypeIcon={getTypeIcon}
          />
        )}
        
        {activeTab === 'create' && (
          <CreateBackupTab
            onCreateBackup={createBackup}
            isCreating={isCreatingBackup}
            getTypeIcon={getTypeIcon}
          />
        )}
      </div>

      {/* Backup Detail Modal */}
      {selectedBackup && (
        <BackupDetailModal
          backup={selectedBackup}
          isOpen={!!selectedBackup}
          onClose={() => setSelectedBackup(null)}
          formatBytes={formatBytes}
          formatDuration={formatDuration}
        />
      )}
    </div>
  );
}

// Backup History Tab Component
interface BackupHistoryTabProps {
  backups: BackupRecord[];
  onDownload: (id: string) => void;
  onDelete: (id: string) => void;
  onViewDetails: (backup: BackupRecord) => void;
  getStatusIcon: (status: string) => React.ReactNode;
  getTypeIcon: (type: string) => string;
  formatBytes: (bytes: number) => string;
  formatDuration: (start: string, end?: string) => string;
}

function BackupHistoryTab({
  backups,
  onDownload,
  onDelete,
  onViewDetails,
  getStatusIcon,
  getTypeIcon,
  formatBytes,
  formatDuration,
}: BackupHistoryTabProps) {
  return (
    <div className="space-y-4">
      {backups.length === 0 ? (
        <div className="text-center py-8">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Backups Found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            No backup records available. Create your first backup to get started.
          </p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Type & Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Size & Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {backups.map((backup) => (
                <tr key={backup.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-lg mr-3">{getTypeIcon(backup.type)}</span>
                      <div>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(backup.status)}
                          <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                            {backup.type} Backup
                          </span>
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                          {backup.status.replace('_', ' ')}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {formatBytes(backup.size)}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {formatDuration(backup.startTime, backup.endTime)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {new Date(backup.startTime).toLocaleDateString()}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {formatDistanceToNow(new Date(backup.startTime), { addSuffix: true })}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 dark:text-white font-mono truncate max-w-xs">
                      {backup.location}
                    </div>
                    {backup.description && (
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {backup.description}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => onViewDetails(backup)}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        Details
                      </button>
                      {backup.status === 'completed' && (
                        <button
                          onClick={() => onDownload(backup.id)}
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                        >
                          Download
                        </button>
                      )}
                      <button
                        onClick={() => onDelete(backup.id)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}

// Backup Schedules Tab Component
interface BackupSchedulesTabProps {
  schedules: BackupSchedule[];
  onToggleSchedule: (id: string, enabled: boolean) => void;
  getTypeIcon: (type: string) => string;
}

function BackupSchedulesTab({ schedules, onToggleSchedule, getTypeIcon }: BackupSchedulesTabProps) {
  return (
    <div className="space-y-4">
      {schedules.length === 0 ? (
        <div className="text-center py-8">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Schedules Configured
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Set up automated backup schedules to ensure regular data protection.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {schedules.map((schedule) => (
            <div key={schedule.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getTypeIcon(schedule.type)}</span>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {schedule.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                      {schedule.type} • {schedule.frequency}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => onToggleSchedule(schedule.id, !schedule.enabled)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    schedule.enabled ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      schedule.enabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Schedule Time</span>
                  <span className="text-gray-900 dark:text-white font-medium">
                    {schedule.time}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Retention</span>
                  <span className="text-gray-900 dark:text-white font-medium">
                    {schedule.retentionDays} days
                  </span>
                </div>
                {schedule.lastRun && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Last Run</span>
                    <span className="text-gray-900 dark:text-white font-medium">
                      {formatDistanceToNow(new Date(schedule.lastRun), { addSuffix: true })}
                    </span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Next Run</span>
                  <span className="text-gray-900 dark:text-white font-medium">
                    {formatDistanceToNow(new Date(schedule.nextRun), { addSuffix: true })}
                  </span>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                <div className="flex items-center justify-between">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    schedule.enabled
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                  }`}>
                    {schedule.enabled ? 'Active' : 'Disabled'}
                  </span>
                  <button className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm font-medium">
                    Edit Schedule
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Create Backup Tab Component
interface CreateBackupTabProps {
  onCreateBackup: (type: 'full' | 'incremental' | 'database' | 'files', description?: string) => void;
  isCreating: boolean;
  getTypeIcon: (type: string) => string;
}

function CreateBackupTab({ onCreateBackup, isCreating, getTypeIcon }: CreateBackupTabProps) {
  const [selectedType, setSelectedType] = useState<'full' | 'incremental' | 'database' | 'files'>('database');
  const [description, setDescription] = useState('');

  const backupTypes = [
    {
      type: 'full' as const,
      name: 'Full System Backup',
      description: 'Complete backup of all system data, files, and configurations',
      estimatedTime: '30-60 minutes',
      estimatedSize: '2-5 GB',
    },
    {
      type: 'database' as const,
      name: 'Database Backup',
      description: 'Backup of all database tables and data',
      estimatedTime: '10-20 minutes',
      estimatedSize: '500 MB - 2 GB',
    },
    {
      type: 'incremental' as const,
      name: 'Incremental Backup',
      description: 'Backup of changes since the last backup',
      estimatedTime: '5-15 minutes',
      estimatedSize: '100-500 MB',
    },
    {
      type: 'files' as const,
      name: 'Files Backup',
      description: 'Backup of uploaded files and media',
      estimatedTime: '15-30 minutes',
      estimatedSize: '1-3 GB',
    },
  ];

  const handleCreateBackup = () => {
    onCreateBackup(selectedType, description || undefined);
    setDescription('');
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h4 className="font-medium text-blue-900 dark:text-blue-100">
              Backup Information
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
              Creating a backup will temporarily impact system performance. Schedule backups during low-traffic periods when possible.
            </p>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Select Backup Type
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {backupTypes.map((type) => (
            <div
              key={type.type}
              className={`relative rounded-lg border-2 p-4 cursor-pointer transition-colors ${
                selectedType === type.type
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
              onClick={() => setSelectedType(type.type)}
            >
              <div className="flex items-start">
                <span className="text-2xl mr-3">{getTypeIcon(type.type)}</span>
                <div className="flex-1">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      name="backupType"
                      value={type.type}
                      checked={selectedType === type.type}
                      onChange={() => setSelectedType(type.type)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <label className="ml-3 text-sm font-medium text-gray-900 dark:text-white">
                      {type.name}
                    </label>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 ml-7">
                    {type.description}
                  </p>
                  <div className="flex items-center space-x-4 mt-2 ml-7 text-xs text-gray-500 dark:text-gray-400">
                    <span>⏱️ {type.estimatedTime}</span>
                    <span>💾 {type.estimatedSize}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Description (Optional)
        </label>
        <textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder="Add a description for this backup..."
        />
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Backup will be stored in the configured backup location
        </div>
        <button
          onClick={handleCreateBackup}
          disabled={isCreating}
          className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isCreating ? (
            <>
              <svg className="w-4 h-4 mr-2 inline animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating Backup...
            </>
          ) : (
            'Create Backup'
          )}
        </button>
      </div>
    </div>
  );
}

// Backup Detail Modal Component
interface BackupDetailModalProps {
  backup: BackupRecord;
  isOpen: boolean;
  onClose: () => void;
  formatBytes: (bytes: number) => string;
  formatDuration: (start: string, end?: string) => string;
}

function BackupDetailModal({ backup, isOpen, onClose, formatBytes, formatDuration }: BackupDetailModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Backup Details
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Basic Information */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Basic Information
            </h4>
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <dt className="text-sm text-gray-500 dark:text-gray-400">Type</dt>
                <dd className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                  {backup.type} Backup
                </dd>
              </div>
              <div>
                <dt className="text-sm text-gray-500 dark:text-gray-400">Status</dt>
                <dd className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                  {backup.status.replace('_', ' ')}
                </dd>
              </div>
              <div>
                <dt className="text-sm text-gray-500 dark:text-gray-400">Size</dt>
                <dd className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatBytes(backup.size)}
                </dd>
              </div>
              <div>
                <dt className="text-sm text-gray-500 dark:text-gray-400">Duration</dt>
                <dd className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatDuration(backup.startTime, backup.endTime)}
                </dd>
              </div>
              <div>
                <dt className="text-sm text-gray-500 dark:text-gray-400">Started</dt>
                <dd className="text-sm font-medium text-gray-900 dark:text-white">
                  {new Date(backup.startTime).toLocaleString()}
                </dd>
              </div>
              {backup.endTime && (
                <div>
                  <dt className="text-sm text-gray-500 dark:text-gray-400">Completed</dt>
                  <dd className="text-sm font-medium text-gray-900 dark:text-white">
                    {new Date(backup.endTime).toLocaleString()}
                  </dd>
                </div>
              )}
            </dl>
          </div>

          {/* Location */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Storage Location
            </h4>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <code className="text-sm text-gray-900 dark:text-white break-all">
                {backup.location}
              </code>
            </div>
          </div>

          {/* Description */}
          {backup.description && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Description
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {backup.description}
              </p>
            </div>
          )}

          {/* Metadata */}
          {backup.metadata && Object.keys(backup.metadata).length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Metadata
              </h4>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <pre className="text-sm text-gray-900 dark:text-white overflow-x-auto">
                  {JSON.stringify(backup.metadata, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Color Test Component
 * Displays the new teal color palette to verify the color changes
 */

import React from 'react';

export function ColorTest() {
  const colorShades = [
    { name: '50', class: 'bg-brand-50', hex: '#f0f9fa' },
    { name: '100', class: 'bg-brand-100', hex: '#ccecef' },
    { name: '200', class: 'bg-brand-200', hex: '#99d8de' },
    { name: '300', class: 'bg-brand-300', hex: '#66c5cd' },
    { name: '400', class: 'bg-brand-400', hex: '#33b1bc' },
    { name: '500', class: 'bg-brand-500', hex: '#1a8a96' },
    { name: '600', class: 'bg-brand-600', hex: '#19727F' },
    { name: '700', class: 'bg-brand-700', hex: '#155b68' },
    { name: '800', class: 'bg-brand-800', hex: '#114451' },
    { name: '900', class: 'bg-brand-900', hex: '#0d2d3a' },
  ];

  const blueToTealTest = [
    { name: 'bg-blue-500', class: 'bg-blue-500' },
    { name: 'bg-blue-600', class: 'bg-blue-600' },
    { name: 'text-blue-600', class: 'text-blue-600' },
    { name: 'border-blue-500', class: 'border-blue-500 border-2' },
  ];

  return (
    <div className="p-8 space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
          Dalti Teal Color Palette
        </h2>
        <div className="grid grid-cols-5 gap-4">
          {colorShades.map((color) => (
            <div key={color.name} className="text-center">
              <div
                className={`${color.class} h-20 w-full rounded-lg border border-gray-200 dark:border-gray-700 mb-2`}
              />
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {color.name}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {color.hex}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
          Blue to Teal Mapping Test
        </h3>
        <div className="grid grid-cols-2 gap-4">
          {blueToTealTest.map((test) => (
            <div key={test.name} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                {test.name}
              </div>
              <div className={`${test.class} h-12 w-full rounded`}>
                {test.name.includes('text') && (
                  <div className="h-full flex items-center justify-center font-medium">
                    Sample Text
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
          Interactive Elements Test
        </h3>
        <div className="space-y-4">
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
            Primary Button (should be teal)
          </button>
          
          <button className="border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-4 py-2 rounded-lg transition-colors">
            Outline Button (should be teal)
          </button>
          
          <input 
            type="text" 
            placeholder="Focus me to see teal ring"
            className="border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-lg px-3 py-2"
          />
          
          <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full inline-block">
            Status Badge (should be teal)
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
          Dalti Specific Classes
        </h3>
        <div className="space-y-4">
          <button className="dalti-btn-primary">
            Dalti Primary Button
          </button>
          
          <button className="dalti-btn-outline">
            Dalti Outline Button
          </button>
          
          <input 
            type="text" 
            placeholder="Dalti input style"
            className="dalti-input"
          />
          
          <div className="dalti-status-active">
            Active Status
          </div>
        </div>
      </div>
    </div>
  );
}

export default ColorTest;

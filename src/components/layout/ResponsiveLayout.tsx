import React, { useState } from 'react';
import { useScreenSize, responsive, responsiveSpacing, getResponsiveClasses } from '../../utils/responsive';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: boolean;
}

export default function ResponsiveLayout({
  children,
  className = '',
  maxWidth = 'full',
  padding = true,
}: ResponsiveLayoutProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-none',
  };

  return (
    <div className={`
      w-full mx-auto
      ${maxWidthClasses[maxWidth]}
      ${padding ? getResponsiveClasses(responsiveSpacing.container) : ''}
      ${className}
    `}>
      {children}
    </div>
  );
}

// Page Header Component
interface ResponsivePageHeaderProps {
  title: string;
  subtitle?: string;
  actions?: React.ReactNode;
  breadcrumb?: React.ReactNode;
  className?: string;
}

export function ResponsivePageHeader({
  title,
  subtitle,
  actions,
  breadcrumb,
  className = '',
}: ResponsivePageHeaderProps) {
  const { isMobile } = useScreenSize();

  return (
    <div className={`bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 ${className}`}>
      <div className={getResponsiveClasses(responsiveSpacing.container)}>
        {breadcrumb && (
          <div className="py-3">
            {breadcrumb}
          </div>
        )}
        
        <div className={`${responsive.stackOnMobile} ${responsive.paddingResponsive} items-start justify-between`}>
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">
              {title}
            </h1>
            {subtitle && (
              <p className="mt-1 text-sm md:text-base text-gray-600 dark:text-gray-400">
                {subtitle}
              </p>
            )}
          </div>
          
          {actions && (
            <div className={`${isMobile ? 'mt-4 w-full' : 'ml-6'} flex-shrink-0`}>
              {actions}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Section Container
interface ResponsiveSectionProps {
  children: React.ReactNode;
  className?: string;
  background?: 'white' | 'gray' | 'transparent';
  padding?: 'sm' | 'md' | 'lg';
}

export function ResponsiveSection({
  children,
  className = '',
  background = 'transparent',
  padding = 'md',
}: ResponsiveSectionProps) {
  const backgroundClasses = {
    white: 'bg-white dark:bg-gray-800',
    gray: 'bg-gray-50 dark:bg-gray-900',
    transparent: '',
  };

  const paddingClasses = {
    sm: getResponsiveClasses({ sm: 'py-4', md: 'py-6', lg: 'py-8' }),
    md: getResponsiveClasses({ sm: 'py-6', md: 'py-8', lg: 'py-12' }),
    lg: getResponsiveClasses({ sm: 'py-8', md: 'py-12', lg: 'py-16' }),
  };

  return (
    <section className={`${backgroundClasses[background]} ${paddingClasses[padding]} ${className}`}>
      <ResponsiveLayout>
        {children}
      </ResponsiveLayout>
    </section>
  );
}

// Two Column Layout
interface ResponsiveTwoColumnProps {
  left: React.ReactNode;
  right: React.ReactNode;
  leftWidth?: 'narrow' | 'normal' | 'wide';
  gap?: 'sm' | 'md' | 'lg';
  stackOnMobile?: boolean;
  className?: string;
}

export function ResponsiveTwoColumn({
  left,
  right,
  leftWidth = 'normal',
  gap = 'md',
  stackOnMobile = true,
  className = '',
}: ResponsiveTwoColumnProps) {
  const { isMobile } = useScreenSize();

  const leftWidthClasses = {
    narrow: 'lg:w-1/4',
    normal: 'lg:w-1/3',
    wide: 'lg:w-2/3',
  };

  const rightWidthClasses = {
    narrow: 'lg:w-3/4',
    normal: 'lg:w-2/3',
    wide: 'lg:w-1/3',
  };

  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6 lg:gap-8',
    lg: 'gap-8 lg:gap-12',
  };

  const layoutClasses = stackOnMobile && isMobile 
    ? 'flex flex-col' 
    : 'flex flex-col lg:flex-row';

  return (
    <div className={`${layoutClasses} ${gapClasses[gap]} ${className}`}>
      <div className={leftWidthClasses[leftWidth]}>
        {left}
      </div>
      <div className={rightWidthClasses[leftWidth]}>
        {right}
      </div>
    </div>
  );
}

// Three Column Layout
interface ResponsiveThreeColumnProps {
  left: React.ReactNode;
  center: React.ReactNode;
  right: React.ReactNode;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ResponsiveThreeColumn({
  left,
  center,
  right,
  gap = 'md',
  className = '',
}: ResponsiveThreeColumnProps) {
  const { isMobile, isTablet } = useScreenSize();

  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8',
  };

  if (isMobile) {
    return (
      <div className={`flex flex-col ${gapClasses[gap]} ${className}`}>
        <div>{left}</div>
        <div>{center}</div>
        <div>{right}</div>
      </div>
    );
  }

  if (isTablet) {
    return (
      <div className={`space-y-${gap === 'sm' ? '4' : gap === 'md' ? '6' : '8'} ${className}`}>
        <div className={`grid grid-cols-2 ${gapClasses[gap]}`}>
          <div>{left}</div>
          <div>{right}</div>
        </div>
        <div>{center}</div>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-3 ${gapClasses[gap]} ${className}`}>
      <div>{left}</div>
      <div>{center}</div>
      <div>{right}</div>
    </div>
  );
}

// Responsive Content Container
interface ResponsiveContentProps {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  center?: boolean;
  className?: string;
}

export function ResponsiveContent({
  children,
  size = 'lg',
  center = false,
  className = '',
}: ResponsiveContentProps) {
  const sizeClasses = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-none',
  };

  return (
    <div className={`
      w-full
      ${sizeClasses[size]}
      ${center ? 'mx-auto' : ''}
      ${getResponsiveClasses(responsiveSpacing.container)}
      ${className}
    `}>
      {children}
    </div>
  );
}

// Responsive Sidebar Layout
interface ResponsiveSidebarLayoutProps {
  sidebar: React.ReactNode;
  content: React.ReactNode;
  sidebarWidth?: 'narrow' | 'normal' | 'wide';
  sidebarPosition?: 'left' | 'right';
  collapsible?: boolean;
  className?: string;
}

export function ResponsiveSidebarLayout({
  sidebar,
  content,
  sidebarWidth = 'normal',
  sidebarPosition = 'left',
  collapsible = true,
  className = '',
}: ResponsiveSidebarLayoutProps) {
  const { isMobile } = useScreenSize();
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);

  const sidebarWidthClasses = {
    narrow: 'w-64',
    normal: 'w-80',
    wide: 'w-96',
  };

  if (isMobile) {
    return (
      <div className={`relative ${className}`}>
        {/* Mobile Sidebar Overlay */}
        {sidebarOpen && (
          <div className="fixed inset-0 z-50 lg:hidden">
            <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setSidebarOpen(false)} />
            <div className={`fixed inset-y-0 ${sidebarPosition}-0 ${sidebarWidthClasses[sidebarWidth]} bg-white dark:bg-gray-800 shadow-xl`}>
              {sidebar}
            </div>
          </div>
        )}
        
        {/* Mobile Content */}
        <div className="flex-1">
          {collapsible && (
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden fixed top-4 left-4 z-40 p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          )}
          {content}
        </div>
      </div>
    );
  }

  // Desktop Layout
  return (
    <div className={`flex ${className}`}>
      {sidebarPosition === 'left' && (
        <div className={`flex-shrink-0 ${sidebarWidthClasses[sidebarWidth]}`}>
          {sidebar}
        </div>
      )}
      
      <div className="flex-1 min-w-0">
        {content}
      </div>
      
      {sidebarPosition === 'right' && (
        <div className={`flex-shrink-0 ${sidebarWidthClasses[sidebarWidth]}`}>
          {sidebar}
        </div>
      )}
    </div>
  );
}

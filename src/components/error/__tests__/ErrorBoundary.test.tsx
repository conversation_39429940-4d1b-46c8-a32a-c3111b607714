/**
 * Unit Tests for Error Boundary Components
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, testHelpers } from '../../../utils/testUtils';
import ErrorBoundary, { 
  withErrorBoundary, 
  useErrorHandler,
  PageErrorBoundary,
  SectionErrorBoundary,
  ComponentErrorBoundary 
} from '../ErrorBoundary';
import { ErrorFallback } from '../ErrorFallback';

// Mock console.error to avoid noise in tests
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

// Test component that throws an error
const ThrowError: React.FC<{ shouldThrow?: boolean; errorMessage?: string }> = ({ 
  shouldThrow = true, 
  errorMessage = 'Test error' 
}) => {
  if (shouldThrow) {
    throw new Error(errorMessage);
  }
  return <div>No error</div>;
};

// Test component for async errors
const AsyncThrowError: React.FC<{ shouldThrow?: boolean }> = ({ shouldThrow = true }) => {
  React.useEffect(() => {
    if (shouldThrow) {
      setTimeout(() => {
        throw new Error('Async test error');
      }, 100);
    }
  }, [shouldThrow]);

  return <div>Async component</div>;
};

describe('ErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    testHelpers.clearAuth();
  });

  describe('Basic Error Catching', () => {
    it('should catch and display errors', () => {
      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByText(/test error/i)).toBeInTheDocument();
    });

    it('should render children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByText('No error')).toBeInTheDocument();
      expect(screen.queryByText(/something went wrong/i)).not.toBeInTheDocument();
    });

    it('should display custom error message', () => {
      render(
        <ErrorBoundary>
          <ThrowError errorMessage="Custom error message" />
        </ErrorBoundary>
      );

      expect(screen.getByText(/custom error message/i)).toBeInTheDocument();
    });
  });

  describe('Error Boundary Levels', () => {
    it('should handle page-level errors', () => {
      render(
        <ErrorBoundary level="page">
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText(/page error/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /reload page/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /go to dashboard/i })).toBeInTheDocument();
    });

    it('should handle section-level errors', () => {
      render(
        <ErrorBoundary level="section">
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText(/section error/i)).toBeInTheDocument();
    });

    it('should handle component-level errors with isolation', () => {
      render(
        <ErrorBoundary level="component" isolate={true}>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText(/component failed to load/i)).toBeInTheDocument();
    });
  });

  describe('Retry Functionality', () => {
    it('should allow retrying after error', async () => {
      const user = userEvent.setup();
      let shouldThrow = true;

      const RetryableComponent = () => {
        if (shouldThrow) {
          throw new Error('Retryable error');
        }
        return <div>Success after retry</div>;
      };

      render(
        <ErrorBoundary maxRetries={2}>
          <RetryableComponent />
        </ErrorBoundary>
      );

      expect(screen.getByText(/retryable error/i)).toBeInTheDocument();

      // Fix the error condition
      shouldThrow = false;

      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText('Success after retry')).toBeInTheDocument();
      });
    });

    it('should limit retry attempts', async () => {
      const user = userEvent.setup();

      render(
        <ErrorBoundary maxRetries={1}>
          <ThrowError />
        </ErrorBoundary>
      );

      const retryButton = screen.getByRole('button', { name: /try again/i });
      
      // First retry
      await user.click(retryButton);
      expect(screen.getByText(/0 attempts left/i)).toBeInTheDocument();

      // Second retry should not be available
      await user.click(retryButton);
      expect(screen.queryByRole('button', { name: /try again/i })).not.toBeInTheDocument();
    });
  });

  describe('Custom Error Handlers', () => {
    it('should call custom error handler', () => {
      const mockErrorHandler = jest.fn();

      render(
        <ErrorBoundary onError={mockErrorHandler}>
          <ThrowError errorMessage="Custom handler test" />
        </ErrorBoundary>
      );

      expect(mockErrorHandler).toHaveBeenCalledWith(
        expect.any(Error),
        expect.any(Object),
        expect.any(String)
      );
    });

    it('should use custom fallback component', () => {
      const CustomFallback = ({ error }: { error: Error }) => (
        <div>Custom fallback: {error.message}</div>
      );

      render(
        <ErrorBoundary fallback={CustomFallback}>
          <ThrowError errorMessage="Custom fallback test" />
        </ErrorBoundary>
      );

      expect(screen.getByText(/custom fallback: custom fallback test/i)).toBeInTheDocument();
    });
  });

  describe('Reset Functionality', () => {
    it('should reset on props change when enabled', () => {
      const { rerender } = render(
        <ErrorBoundary resetOnPropsChange={true} resetKeys={['key1']}>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText(/test error/i)).toBeInTheDocument();

      // Change reset key
      rerender(
        <ErrorBoundary resetOnPropsChange={true} resetKeys={['key2']}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByText('No error')).toBeInTheDocument();
    });
  });

  describe('Error Context', () => {
    it('should include context information', () => {
      const mockErrorHandler = jest.fn();

      render(
        <ErrorBoundary context="Test Context" onError={mockErrorHandler}>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(mockErrorHandler).toHaveBeenCalledWith(
        expect.any(Error),
        expect.any(Object),
        expect.stringContaining('error_')
      );
    });
  });

  describe('Specialized Error Boundaries', () => {
    it('should render PageErrorBoundary correctly', () => {
      render(
        <PageErrorBoundary>
          <ThrowError />
        </PageErrorBoundary>
      );

      expect(screen.getByText(/page error/i)).toBeInTheDocument();
    });

    it('should render SectionErrorBoundary correctly', () => {
      render(
        <SectionErrorBoundary section="Test Section">
          <ThrowError />
        </SectionErrorBoundary>
      );

      expect(screen.getByText(/section error/i)).toBeInTheDocument();
    });

    it('should render ComponentErrorBoundary correctly', () => {
      render(
        <ComponentErrorBoundary component="Test Component">
          <ThrowError />
        </ComponentErrorBoundary>
      );

      expect(screen.getByText(/component failed to load/i)).toBeInTheDocument();
    });
  });

  describe('withErrorBoundary HOC', () => {
    it('should wrap component with error boundary', () => {
      const WrappedComponent = withErrorBoundary(ThrowError, {
        level: 'component',
        context: 'HOC Test',
      });

      render(<WrappedComponent />);

      expect(screen.getByText(/component error/i)).toBeInTheDocument();
    });

    it('should preserve component display name', () => {
      const TestComponent = () => <div>Test</div>;
      TestComponent.displayName = 'TestComponent';

      const WrappedComponent = withErrorBoundary(TestComponent);

      expect(WrappedComponent.displayName).toBe('withErrorBoundary(TestComponent)');
    });
  });

  describe('useErrorHandler Hook', () => {
    it('should provide error handling functionality', () => {
      let captureError: (error: Error) => void;
      let resetError: () => void;

      const TestComponent = () => {
        const { captureError: capture, resetError: reset } = useErrorHandler();
        captureError = capture;
        resetError = reset;
        return <div>Test component</div>;
      };

      render(
        <ErrorBoundary>
          <TestComponent />
        </ErrorBoundary>
      );

      expect(screen.getByText('Test component')).toBeInTheDocument();

      // Trigger error
      act(() => {
        captureError(new Error('Hook error'));
      });

      expect(screen.getByText(/hook error/i)).toBeInTheDocument();
    });
  });

  describe('Error Reporting', () => {
    it('should report errors to monitoring service', () => {
      const mockSentry = {
        withScope: jest.fn((callback) => {
          const scope = {
            setTag: jest.fn(),
            setContext: jest.fn(),
          };
          callback(scope);
        }),
        captureException: jest.fn(),
      };

      (window as any).Sentry = mockSentry;

      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(mockSentry.withScope).toHaveBeenCalled();
      expect(mockSentry.captureException).toHaveBeenCalled();

      delete (window as any).Sentry;
    });

    it('should handle reporting errors gracefully', () => {
      // Mock fetch to simulate reporting endpoint
      global.fetch = jest.fn().mockRejectedValue(new Error('Reporting failed'));

      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      // Should still render error boundary even if reporting fails
      expect(screen.getByText(/test error/i)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      const errorContainer = screen.getByText(/test error/i).closest('div');
      expect(errorContainer).toBeInTheDocument();
    });

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup();

      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      const retryButton = screen.getByRole('button', { name: /try again/i });
      
      await user.tab();
      expect(retryButton).toHaveFocus();

      await user.keyboard('{Enter}');
      // Button should be activated
    });
  });

  describe('Theme Integration', () => {
    it('should work with dark theme', () => {
      renderWithProviders(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>,
        { theme: 'dark' }
      );

      expect(screen.getByText(/test error/i)).toBeInTheDocument();
    });

    it('should work with light theme', () => {
      renderWithProviders(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>,
        { theme: 'light' }
      );

      expect(screen.getByText(/test error/i)).toBeInTheDocument();
    });
  });
});

/**
 * Error Fallback Components
 * Provides user-friendly error displays and recovery options
 */

import React, { ErrorInfo } from 'react';
import { useThemeStyles } from '../../contexts/ThemeContext';

export interface ErrorFallbackProps {
  error: Error;
  errorInfo?: ErrorInfo | null;
  errorId: string;
  retryCount: number;
  maxRetries: number;
  onRetry: () => void;
  onReload: () => void;
  onGoHome: () => void;
  onReset: () => void;
  level: 'page' | 'section' | 'component';
  isolate?: boolean;
}

export function ErrorFallback({
  error,
  errorInfo,
  errorId,
  retryCount,
  maxRetries,
  onRetry,
  onReload,
  onGoHome,
  onReset,
  level,
  isolate = false,
}: ErrorFallbackProps) {
  const { themeClasses, isDark } = useThemeStyles();
  const [showDetails, setShowDetails] = React.useState(false);
  const [reportSent, setReportSent] = React.useState(false);

  const canRetry = retryCount < maxRetries;
  const isProduction = process.env.NODE_ENV === 'production';

  const handleSendReport = async () => {
    try {
      // Simulate sending error report
      await new Promise(resolve => setTimeout(resolve, 1000));
      setReportSent(true);
    } catch {
      // Handle report sending error
    }
  };

  const getErrorIcon = () => (
    <svg 
      className="w-16 h-16 text-red-500 mx-auto mb-4" 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={1.5} 
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" 
      />
    </svg>
  );

  const getErrorTitle = () => {
    switch (level) {
      case 'page':
        return 'Page Error';
      case 'section':
        return 'Section Error';
      case 'component':
        return 'Component Error';
      default:
        return 'Something went wrong';
    }
  };

  const getErrorMessage = () => {
    if (isProduction) {
      switch (level) {
        case 'page':
          return 'We encountered an error while loading this page. Please try refreshing or go back to the dashboard.';
        case 'section':
          return 'This section failed to load properly. You can try refreshing or continue using other parts of the application.';
        case 'component':
          return 'A component failed to load. This might be a temporary issue.';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
    
    return error.message || 'An unexpected error occurred';
  };

  const getContainerClasses = () => {
    const baseClasses = `
      ${themeClasses.bg.card} 
      ${themeClasses.border.primary} 
      ${themeClasses.text.primary}
      border rounded-lg shadow-sm
    `;

    switch (level) {
      case 'page':
        return `${baseClasses} min-h-screen flex items-center justify-center p-8`;
      case 'section':
        return `${baseClasses} p-8 m-4`;
      case 'component':
        return `${baseClasses} p-4 m-2`;
      default:
        return `${baseClasses} p-6`;
    }
  };

  if (level === 'component' && isolate) {
    return (
      <div className={`${themeClasses.bg.secondary} ${themeClasses.border.primary} border rounded p-3 m-1`}>
        <div className="flex items-center text-sm">
          <svg className="w-4 h-4 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className={themeClasses.text.secondary}>Component failed to load</span>
          {canRetry && (
            <button
              onClick={onRetry}
              className="ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"
            >
              Retry
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={getContainerClasses()}>
      <div className="max-w-md mx-auto text-center">
        {getErrorIcon()}
        
        <h1 className="text-2xl font-bold mb-2">
          {getErrorTitle()}
        </h1>
        
        <p className={`${themeClasses.text.secondary} mb-6`}>
          {getErrorMessage()}
        </p>

        <div className="space-y-3 mb-6">
          {canRetry && (
            <button
              onClick={onRetry}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              Try Again ({maxRetries - retryCount} attempts left)
            </button>
          )}
          
          {level === 'page' && (
            <>
              <button
                onClick={onReload}
                className={`w-full px-4 py-2 ${themeClasses.bg.secondary} ${themeClasses.text.primary} ${themeClasses.border.primary} border rounded-lg hover:${themeClasses.bg.tertiary} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors`}
              >
                Reload Page
              </button>
              
              <button
                onClick={onGoHome}
                className={`w-full px-4 py-2 ${themeClasses.bg.secondary} ${themeClasses.text.primary} ${themeClasses.border.primary} border rounded-lg hover:${themeClasses.bg.tertiary} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors`}
              >
                Go to Dashboard
              </button>
            </>
          )}
        </div>

        {/* Error Details */}
        {!isProduction && (
          <div className="text-left">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className={`text-sm ${themeClasses.text.secondary} hover:${themeClasses.text.primary} underline mb-2`}
            >
              {showDetails ? 'Hide' : 'Show'} Error Details
            </button>
            
            {showDetails && (
              <div className={`${themeClasses.bg.secondary} ${themeClasses.border.primary} border rounded p-3 text-xs font-mono overflow-auto max-h-40`}>
                <div className="mb-2">
                  <strong>Error ID:</strong> {errorId}
                </div>
                <div className="mb-2">
                  <strong>Error:</strong> {error.name}: {error.message}
                </div>
                {error.stack && (
                  <div className="mb-2">
                    <strong>Stack:</strong>
                    <pre className="whitespace-pre-wrap mt-1">{error.stack}</pre>
                  </div>
                )}
                {errorInfo?.componentStack && (
                  <div>
                    <strong>Component Stack:</strong>
                    <pre className="whitespace-pre-wrap mt-1">{errorInfo.componentStack}</pre>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Error Reporting */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          {!reportSent ? (
            <button
              onClick={handleSendReport}
              className={`text-sm ${themeClasses.text.secondary} hover:${themeClasses.text.primary} underline`}
            >
              Send Error Report
            </button>
          ) : (
            <p className="text-sm text-green-600 dark:text-green-400">
              ✓ Error report sent. Thank you for helping us improve!
            </p>
          )}
        </div>

        {/* Additional Help */}
        <div className="mt-4 text-xs text-gray-500 dark:text-gray-400">
          If this problem persists, please contact support with error ID: {errorId}
        </div>
      </div>
    </div>
  );
}

// Specialized fallback for network errors
export function NetworkErrorFallback({
  onRetry,
  retryCount,
  maxRetries,
}: {
  onRetry: () => void;
  retryCount: number;
  maxRetries: number;
}) {
  const { themeClasses } = useThemeStyles();
  const canRetry = retryCount < maxRetries;

  return (
    <div className={`${themeClasses.bg.card} ${themeClasses.border.primary} border rounded-lg p-6 text-center`}>
      <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
      </svg>
      
      <h3 className="text-lg font-medium mb-2">Connection Problem</h3>
      <p className={`${themeClasses.text.secondary} mb-4`}>
        Unable to connect to the server. Please check your internet connection.
      </p>
      
      {canRetry && (
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Try Again ({maxRetries - retryCount} attempts left)
        </button>
      )}
    </div>
  );
}

// Fallback for authentication errors
export function AuthErrorFallback() {
  const { themeClasses } = useThemeStyles();

  const handleLogin = () => {
    window.location.href = '/login';
  };

  return (
    <div className={`${themeClasses.bg.card} ${themeClasses.border.primary} border rounded-lg p-6 text-center`}>
      <svg className="w-12 h-12 text-yellow-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      </svg>
      
      <h3 className="text-lg font-medium mb-2">Authentication Required</h3>
      <p className={`${themeClasses.text.secondary} mb-4`}>
        Your session has expired. Please log in again to continue.
      </p>
      
      <button
        onClick={handleLogin}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        Go to Login
      </button>
    </div>
  );
}

// Fallback for permission errors
export function PermissionErrorFallback() {
  const { themeClasses } = useThemeStyles();

  const handleGoHome = () => {
    window.location.href = '/';
  };

  return (
    <div className={`${themeClasses.bg.card} ${themeClasses.border.primary} border rounded-lg p-6 text-center`}>
      <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
      </svg>
      
      <h3 className="text-lg font-medium mb-2">Access Denied</h3>
      <p className={`${themeClasses.text.secondary} mb-4`}>
        You don't have permission to access this resource.
      </p>
      
      <button
        onClick={handleGoHome}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        Go to Dashboard
      </button>
    </div>
  );
}

export default ErrorFallback;

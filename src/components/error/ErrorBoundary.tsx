/**
 * React Error Boundary Components
 * Provides comprehensive error handling and recovery for the Dalti admin panel
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { ErrorFallback, ErrorFallbackProps } from './ErrorFallback';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  retryCount: number;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  isolate?: boolean;
  maxRetries?: number;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
  level?: 'page' | 'section' | 'component';
  context?: string;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, context = 'Unknown' } = this.props;
    const { errorId } = this.state;

    // Enhanced error info
    const enhancedError = {
      ...error,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId(),
      componentStack: errorInfo.componentStack,
    };

    this.setState({
      errorInfo,
    });

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 Error Boundary Caught Error [${errorId}]`);
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Enhanced Error:', enhancedError);
      console.groupEnd();
    }

    // Report error to monitoring service
    this.reportError(enhancedError, errorInfo, errorId);

    // Call custom error handler
    if (onError) {
      onError(error, errorInfo, errorId);
    }
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;

    if (hasError && resetOnPropsChange) {
      if (resetKeys) {
        const hasResetKeyChanged = resetKeys.some(
          (key, index) => prevProps.resetKeys?.[index] !== key
        );
        if (hasResetKeyChanged) {
          this.resetErrorBoundary();
        }
      }
    }
  }

  private getCurrentUserId(): string | null {
    try {
      const userStr = localStorage.getItem('dalti_admin_user');
      if (userStr) {
        const user = JSON.parse(userStr);
        return user.id || null;
      }
    } catch {
      // Ignore errors when getting user ID
    }
    return null;
  }

  private reportError = async (error: any, errorInfo: ErrorInfo, errorId: string) => {
    try {
      // Report to error monitoring service (e.g., Sentry, LogRocket, etc.)
      if (window.Sentry) {
        window.Sentry.withScope((scope) => {
          scope.setTag('errorBoundary', true);
          scope.setTag('errorId', errorId);
          scope.setContext('errorInfo', errorInfo);
          scope.setContext('props', this.props);
          window.Sentry.captureException(error);
        });
      }

      // Report to custom error tracking
      if (process.env.REACT_APP_ERROR_REPORTING_URL) {
        await fetch(process.env.REACT_APP_ERROR_REPORTING_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            errorId,
            error: {
              name: error.name,
              message: error.message,
              stack: error.stack,
            },
            errorInfo,
            context: this.props.context,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            userId: this.getCurrentUserId(),
          }),
        });
      }
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
    });
  };

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    const { retryCount } = this.state;

    if (retryCount < maxRetries) {
      this.setState(
        (prevState) => ({
          hasError: false,
          error: null,
          errorInfo: null,
          errorId: '',
          retryCount: prevState.retryCount + 1,
        })
      );
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    const { hasError, error, errorInfo, errorId, retryCount } = this.state;
    const { 
      children, 
      fallback: CustomFallback, 
      maxRetries = 3, 
      level = 'component',
      isolate = false 
    } = this.props;

    if (hasError && error) {
      const FallbackComponent = CustomFallback || ErrorFallback;
      
      return (
        <FallbackComponent
          error={error}
          errorInfo={errorInfo}
          errorId={errorId}
          retryCount={retryCount}
          maxRetries={maxRetries}
          onRetry={this.handleRetry}
          onReload={this.handleReload}
          onGoHome={this.handleGoHome}
          onReset={this.resetErrorBoundary}
          level={level}
          isolate={isolate}
        />
      );
    }

    return children;
  }
}

// Higher-order component for wrapping components with error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

// Hook for error boundary integration
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const captureError = React.useCallback((error: Error, context?: string) => {
    console.error('Error captured by useErrorHandler:', error);
    
    // Report error
    if (window.Sentry) {
      window.Sentry.withScope((scope) => {
        scope.setTag('errorHandler', true);
        scope.setContext('context', context);
        window.Sentry.captureException(error);
      });
    }

    setError(error);
  }, []);

  // Throw error to be caught by error boundary
  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { captureError, resetError };
}

// Async error boundary for handling async errors
export function AsyncErrorBoundary({ children, ...props }: ErrorBoundaryProps) {
  const { captureError } = useErrorHandler();

  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      captureError(new Error(event.reason), 'Unhandled Promise Rejection');
    };

    const handleError = (event: ErrorEvent) => {
      captureError(new Error(event.message), 'Global Error Handler');
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, [captureError]);

  return (
    <ErrorBoundary {...props}>
      {children}
    </ErrorBoundary>
  );
}

// Specialized error boundaries for different parts of the app
export const PageErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary
    level="page"
    context="Page"
    fallback={ErrorFallback}
    maxRetries={2}
    resetOnPropsChange={true}
  >
    {children}
  </ErrorBoundary>
);

export const SectionErrorBoundary: React.FC<{ children: ReactNode; section: string }> = ({ 
  children, 
  section 
}) => (
  <ErrorBoundary
    level="section"
    context={`Section: ${section}`}
    isolate={true}
    maxRetries={3}
  >
    {children}
  </ErrorBoundary>
);

export const ComponentErrorBoundary: React.FC<{ children: ReactNode; component: string }> = ({ 
  children, 
  component 
}) => (
  <ErrorBoundary
    level="component"
    context={`Component: ${component}`}
    isolate={true}
    maxRetries={1}
  >
    {children}
  </ErrorBoundary>
);

// Error boundary for route components
export const RouteErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary
    level="page"
    context="Route"
    resetOnPropsChange={true}
    resetKeys={[window.location.pathname]}
  >
    {children}
  </ErrorBoundary>
);

export default ErrorBoundary;

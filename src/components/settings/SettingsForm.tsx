import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { LoadingButton } from '../../components/common/LoadingOverlay';
import toast from 'react-hot-toast';

// Settings validation schema
const settingsSchema = z.object({
  // Platform Settings
  platformName: z.string().min(1, 'Platform name is required'),
  platformDescription: z.string().optional(),
  supportEmail: z.string().email('Invalid email format'),
  supportPhone: z.string().optional(),
  
  // Provider Settings
  providerAutoApproval: z.boolean(),
  providerVerificationRequired: z.boolean(),
  maxProvidersPerCategory: z.number().min(1).max(1000),
  
  // Customer Settings
  customerEmailVerificationRequired: z.boolean(),
  customerPhoneVerificationRequired: z.boolean(),
  defaultCustomerCredits: z.number().min(0).max(10000),
  
  // Booking Settings
  maxBookingDaysInAdvance: z.number().min(1).max(365),
  cancellationDeadlineHours: z.number().min(1).max(168),
  noShowPenaltyCredits: z.number().min(0).max(1000),
  
  // Notification Settings
  emailNotificationsEnabled: z.boolean(),
  smsNotificationsEnabled: z.boolean(),
  pushNotificationsEnabled: z.boolean(),
  
  // Security Settings
  sessionTimeoutMinutes: z.number().min(15).max(1440),
  maxLoginAttempts: z.number().min(3).max(10),
  passwordMinLength: z.number().min(6).max(32),
});

type SettingsFormData = z.infer<typeof settingsSchema>;

interface SettingsFormProps {
  onSettingsUpdate?: () => void;
}

export default function SettingsForm({ onSettingsUpdate }: SettingsFormProps) {
  const [activeTab, setActiveTab] = useState('platform');
  const loading = useLoading();

  const form = useForm({
    resolver: zodResolver(settingsSchema),
    defaultValues: {
      // Platform defaults
      platformName: 'Dalti',
      platformDescription: 'Professional service booking platform',
      supportEmail: '<EMAIL>',
      supportPhone: '******-DALTI',
      
      // Provider defaults
      providerAutoApproval: false,
      providerVerificationRequired: true,
      maxProvidersPerCategory: 100,
      
      // Customer defaults
      customerEmailVerificationRequired: true,
      customerPhoneVerificationRequired: false,
      defaultCustomerCredits: 0,
      
      // Booking defaults
      maxBookingDaysInAdvance: 30,
      cancellationDeadlineHours: 24,
      noShowPenaltyCredits: 10,
      
      // Notification defaults
      emailNotificationsEnabled: true,
      smsNotificationsEnabled: true,
      pushNotificationsEnabled: true,
      
      // Security defaults
      sessionTimeoutMinutes: 480,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
    },
  });

  const { register, handleSubmit, formState: { errors }, reset } = form;

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      loading.startLoading({ message: 'Loading settings...' });
      const response = await adminApi.system.getSettings();
      
      if (response.success && response.data) {
        reset(response.data);
      }
    } catch (error) {
      handleError(error, { action: 'fetch_settings' });
    } finally {
      loading.stopLoading();
    }
  };

  const onSubmit = async (data: SettingsFormData) => {
    try {
      loading.startLoading({ message: 'Updating settings...' });
      const response = await adminApi.system.updateSettings(data);
      
      if (response.success) {
        toast.success('Settings updated successfully');
        onSettingsUpdate?.();
      } else {
        throw new Error(response.message || 'Failed to update settings');
      }
    } catch (error) {
      handleError(error, { action: 'update_settings' });
    } finally {
      loading.stopLoading();
    }
  };

  const tabs = [
    { id: 'platform', name: 'Platform', icon: '🏢' },
    { id: 'providers', name: 'Providers', icon: '🏪' },
    { id: 'customers', name: 'Customers', icon: '👥' },
    { id: 'bookings', name: 'Bookings', icon: '📅' },
    { id: 'notifications', name: 'Notifications', icon: '🔔' },
    { id: 'security', name: 'Security', icon: '🔒' },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }
              `}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="p-6">
        {/* Platform Settings */}
        {activeTab === 'platform' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Platform Configuration</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Platform Name
                </label>
                <input
                  {...register('platformName')}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                {errors.platformName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.platformName.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Support Email
                </label>
                <input
                  {...register('supportEmail')}
                  type="email"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                {errors.supportEmail && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.supportEmail.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Platform Description
                </label>
                <textarea
                  {...register('platformDescription')}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Support Phone
                </label>
                <input
                  {...register('supportPhone')}
                  type="tel"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>
          </div>
        )}

        {/* Provider Settings */}
        {activeTab === 'providers' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Provider Configuration</h3>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  {...register('providerAutoApproval')}
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Enable automatic provider approval
                </label>
              </div>

              <div className="flex items-center">
                <input
                  {...register('providerVerificationRequired')}
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Require provider verification
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Maximum Providers per Category
                </label>
                <input
                  {...register('maxProvidersPerCategory', { valueAsNumber: true })}
                  type="number"
                  min="1"
                  max="1000"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                {errors.maxProvidersPerCategory && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.maxProvidersPerCategory.message}</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Customer Settings */}
        {activeTab === 'customers' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Customer Configuration</h3>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  {...register('customerEmailVerificationRequired')}
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Require email verification for customers
                </label>
              </div>

              <div className="flex items-center">
                <input
                  {...register('customerPhoneVerificationRequired')}
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Require phone verification for customers
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Default Customer Credits
                </label>
                <input
                  {...register('defaultCustomerCredits', { valueAsNumber: true })}
                  type="number"
                  min="0"
                  max="10000"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                {errors.defaultCustomerCredits && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.defaultCustomerCredits.message}</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Booking Settings */}
        {activeTab === 'bookings' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Booking Configuration</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max Booking Days in Advance
                </label>
                <input
                  {...register('maxBookingDaysInAdvance', { valueAsNumber: true })}
                  type="number"
                  min="1"
                  max="365"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                {errors.maxBookingDaysInAdvance && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.maxBookingDaysInAdvance.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Cancellation Deadline (Hours)
                </label>
                <input
                  {...register('cancellationDeadlineHours', { valueAsNumber: true })}
                  type="number"
                  min="1"
                  max="168"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                {errors.cancellationDeadlineHours && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.cancellationDeadlineHours.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  No-Show Penalty (Credits)
                </label>
                <input
                  {...register('noShowPenaltyCredits', { valueAsNumber: true })}
                  type="number"
                  min="0"
                  max="1000"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                {errors.noShowPenaltyCredits && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.noShowPenaltyCredits.message}</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Notification Settings */}
        {activeTab === 'notifications' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Notification Configuration</h3>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  {...register('emailNotificationsEnabled')}
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Enable email notifications
                </label>
              </div>

              <div className="flex items-center">
                <input
                  {...register('smsNotificationsEnabled')}
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Enable SMS notifications
                </label>
              </div>

              <div className="flex items-center">
                <input
                  {...register('pushNotificationsEnabled')}
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Enable push notifications
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Security Settings */}
        {activeTab === 'security' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Security Configuration</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Session Timeout (Minutes)
                </label>
                <input
                  {...register('sessionTimeoutMinutes', { valueAsNumber: true })}
                  type="number"
                  min="15"
                  max="1440"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                {errors.sessionTimeoutMinutes && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.sessionTimeoutMinutes.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max Login Attempts
                </label>
                <input
                  {...register('maxLoginAttempts', { valueAsNumber: true })}
                  type="number"
                  min="3"
                  max="10"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                {errors.maxLoginAttempts && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.maxLoginAttempts.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Minimum Password Length
                </label>
                <input
                  {...register('passwordMinLength', { valueAsNumber: true })}
                  type="number"
                  min="6"
                  max="32"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                {errors.passwordMinLength && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.passwordMinLength.message}</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={() => reset()}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Reset
          </button>
          
          <LoadingButton
            loading={loading.isLoading}
            loadingText="Saving..."
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Save Settings
          </LoadingButton>
        </div>
      </form>
    </div>
  );
}

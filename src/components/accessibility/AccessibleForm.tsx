import React, { useId, useState, useRef, useEffect } from 'react';
import { useAccessibility, AriaUtils, KeyboardNavigation } from '../../utils/accessibility';

interface AccessibleFormFieldProps {
  label: string;
  children: React.ReactNode;
  error?: string;
  description?: string;
  required?: boolean;
  className?: string;
}

export function AccessibleFormField({
  label,
  children,
  error,
  description,
  required = false,
  className = '',
}: AccessibleFormFieldProps) {
  const fieldId = useId();
  const errorId = useId();
  const descriptionId = useId();
  const { announce } = useAccessibility();

  // Announce errors to screen readers
  useEffect(() => {
    if (error) {
      announce(`Error in ${label}: ${error}`, 'assertive');
    }
  }, [error, label, announce]);

  return (
    <div className={`space-y-2 ${className}`}>
      <label
        htmlFor={fieldId}
        className="block text-sm font-medium text-gray-700 dark:text-gray-300"
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
      
      {description && (
        <p id={descriptionId} className="text-sm text-gray-600 dark:text-gray-400">
          {description}
        </p>
      )}
      
      <div>
        {React.cloneElement(children as React.ReactElement, {
          id: fieldId,
          'aria-describedby': AriaUtils.createDescription([
            description ? descriptionId : '',
            error ? errorId : '',
          ]),
          'aria-invalid': error ? 'true' : 'false',
          'aria-required': required,
        })}
      </div>
      
      {error && (
        <p
          id={errorId}
          className="text-sm text-red-600 dark:text-red-400"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
    </div>
  );
}

interface AccessibleInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

export function AccessibleInput({
  error = false,
  icon,
  iconPosition = 'left',
  className = '',
  ...props
}: AccessibleInputProps) {
  const baseClasses = `
    w-full px-3 py-2 text-sm md:text-base
    border rounded-lg
    focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none
    dark:bg-gray-700 dark:text-white
    transition-colors
    ${error 
      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500' 
      : 'border-gray-300 dark:border-gray-600'
    }
    ${icon ? (iconPosition === 'left' ? 'pl-10' : 'pr-10') : ''}
  `;

  if (icon) {
    return (
      <div className="relative">
        {iconPosition === 'left' && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div className="text-gray-400" aria-hidden="true">{icon}</div>
          </div>
        )}
        <input className={`${baseClasses} ${className}`} {...props} />
        {iconPosition === 'right' && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <div className="text-gray-400" aria-hidden="true">{icon}</div>
          </div>
        )}
      </div>
    );
  }

  return <input className={`${baseClasses} ${className}`} {...props} />;
}

interface AccessibleSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: boolean;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  placeholder?: string;
}

export function AccessibleSelect({
  error = false,
  options,
  placeholder,
  className = '',
  ...props
}: AccessibleSelectProps) {
  const baseClasses = `
    w-full px-3 py-2 text-sm md:text-base
    border rounded-lg
    focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none
    dark:bg-gray-700 dark:text-white
    transition-colors
    ${error 
      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500' 
      : 'border-gray-300 dark:border-gray-600'
    }
  `;

  return (
    <select className={`${baseClasses} ${className}`} {...props}>
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {options.map((option) => (
        <option key={option.value} value={option.value} disabled={option.disabled}>
          {option.label}
        </option>
      ))}
    </select>
  );
}

interface AccessibleComboboxProps {
  label: string;
  options: Array<{ value: string; label: string }>;
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  error?: string;
  required?: boolean;
  className?: string;
}

export function AccessibleCombobox({
  label,
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  error,
  required = false,
  className = '',
}: AccessibleComboboxProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const comboboxRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const listboxRef = useRef<HTMLUListElement>(null);
  
  const comboboxId = useId();
  const listboxId = useId();
  const { announce } = useAccessibility();

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedOption = options.find(option => option.value === value);

  useEffect(() => {
    if (isOpen && listboxRef.current) {
      const focusedElement = listboxRef.current.children[focusedIndex] as HTMLElement;
      if (focusedElement) {
        focusedElement.scrollIntoView({ block: 'nearest' });
      }
    }
  }, [focusedIndex, isOpen]);

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
          setFocusedIndex(0);
        } else {
          setFocusedIndex(prev => (prev + 1) % filteredOptions.length);
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (isOpen) {
          setFocusedIndex(prev => prev === 0 ? filteredOptions.length - 1 : prev - 1);
        }
        break;
      case 'Enter':
        e.preventDefault();
        if (isOpen && focusedIndex >= 0) {
          const selectedOption = filteredOptions[focusedIndex];
          onChange(selectedOption.value);
          setSearchTerm(selectedOption.label);
          setIsOpen(false);
          announce(`${selectedOption.label} selected`);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setFocusedIndex(-1);
        break;
      case 'Tab':
        setIsOpen(false);
        break;
    }
  };

  const handleOptionClick = (option: { value: string; label: string }) => {
    onChange(option.value);
    setSearchTerm(option.label);
    setIsOpen(false);
    announce(`${option.label} selected`);
    inputRef.current?.focus();
  };

  return (
    <AccessibleFormField
      label={label}
      error={error}
      required={required}
      className={className}
    >
      <div ref={comboboxRef} className="relative">
        <input
          ref={inputRef}
          type="text"
          role="combobox"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          aria-controls={listboxId}
          aria-autocomplete="list"
          value={searchTerm || selectedOption?.label || ''}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setIsOpen(true);
            setFocusedIndex(-1);
          }}
          onKeyDown={handleInputKeyDown}
          onFocus={() => setIsOpen(true)}
          onBlur={(e) => {
            // Don't close if clicking on an option
            if (!comboboxRef.current?.contains(e.relatedTarget as Node)) {
              setIsOpen(false);
            }
          }}
          placeholder={placeholder}
          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        />
        
        <button
          type="button"
          className="absolute inset-y-0 right-0 flex items-center pr-2"
          onClick={() => setIsOpen(!isOpen)}
          aria-label="Toggle options"
          tabIndex={-1}
        >
          <svg
            className={`w-5 h-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {isOpen && (
          <ul
            ref={listboxRef}
            id={listboxId}
            role="listbox"
            className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-auto"
          >
            {filteredOptions.length === 0 ? (
              <li className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                No options found
              </li>
            ) : (
              filteredOptions.map((option, index) => (
                <li
                  key={option.value}
                  role="option"
                  aria-selected={option.value === value}
                  className={`px-3 py-2 text-sm cursor-pointer ${
                    index === focusedIndex
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100'
                      : option.value === value
                      ? 'bg-blue-50 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                      : 'text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                  onClick={() => handleOptionClick(option)}
                  onMouseEnter={() => setFocusedIndex(index)}
                >
                  {option.label}
                  {option.value === value && (
                    <svg
                      className="inline w-4 h-4 ml-2 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </li>
              ))
            )}
          </ul>
        )}
      </div>
    </AccessibleFormField>
  );
}

interface AccessibleCheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  description?: string;
  error?: string;
}

export function AccessibleCheckbox({
  label,
  description,
  error,
  className = '',
  ...props
}: AccessibleCheckboxProps) {
  const checkboxId = useId();
  const descriptionId = useId();
  const errorId = useId();

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-start">
        <input
          id={checkboxId}
          type="checkbox"
          className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700"
          aria-describedby={AriaUtils.createDescription([
            description ? descriptionId : '',
            error ? errorId : '',
          ])}
          aria-invalid={error ? 'true' : 'false'}
          {...props}
        />
        <div className="ml-3">
          <label htmlFor={checkboxId} className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {label}
          </label>
          {description && (
            <p id={descriptionId} className="text-sm text-gray-600 dark:text-gray-400">
              {description}
            </p>
          )}
        </div>
      </div>
      {error && (
        <p
          id={errorId}
          className="text-sm text-red-600 dark:text-red-400"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
    </div>
  );
}

interface AccessibleRadioGroupProps {
  name: string;
  label: string;
  options: Array<{ value: string; label: string; description?: string }>;
  value?: string;
  onChange: (value: string) => void;
  error?: string;
  required?: boolean;
  className?: string;
}

export function AccessibleRadioGroup({
  name,
  label,
  options,
  value,
  onChange,
  error,
  required = false,
  className = '',
}: AccessibleRadioGroupProps) {
  const groupId = useId();
  const errorId = useId();
  const { announce } = useAccessibility();

  const handleChange = (newValue: string) => {
    onChange(newValue);
    const selectedOption = options.find(option => option.value === newValue);
    if (selectedOption) {
      announce(`${selectedOption.label} selected`);
    }
  };

  return (
    <fieldset className={`space-y-3 ${className}`}>
      <legend className="text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="required">
            *
          </span>
        )}
      </legend>
      
      <div
        role="radiogroup"
        aria-labelledby={groupId}
        aria-describedby={error ? errorId : undefined}
        aria-invalid={error ? 'true' : 'false'}
        className="space-y-2"
      >
        {options.map((option) => {
          const optionId = useId();
          const descriptionId = useId();
          
          return (
            <div key={option.value} className="flex items-start">
              <input
                id={optionId}
                name={name}
                type="radio"
                value={option.value}
                checked={value === option.value}
                onChange={() => handleChange(option.value)}
                className="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700"
                aria-describedby={option.description ? descriptionId : undefined}
              />
              <div className="ml-3">
                <label htmlFor={optionId} className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {option.label}
                </label>
                {option.description && (
                  <p id={descriptionId} className="text-sm text-gray-600 dark:text-gray-400">
                    {option.description}
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>
      
      {error && (
        <p
          id={errorId}
          className="text-sm text-red-600 dark:text-red-400"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
    </fieldset>
  );
}

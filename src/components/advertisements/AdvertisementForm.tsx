import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Advertisement, CreateAdvertisementRequest, UpdateAdvertisementRequest } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { LoadingButton } from '../common/LoadingOverlay';
import ImageUpload from '../common/ImageUpload';
import toast from 'react-hot-toast';

// Validation schema
const advertisementSchema = z.object({
  title: z.string()
    .min(2, 'Title must be at least 2 characters')
    .max(100, 'Title must be less than 100 characters'),
  subtitle: z.string()
    .max(150, 'Subtitle must be less than 150 characters')
    .optional(),
  description: z.string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  callToActionText: z.string()
    .min(1, 'Call to action text is required')
    .max(50, 'Call to action text must be less than 50 characters'),
  callToActionLink: z.string()
    .url('Please enter a valid URL')
    .min(1, 'Call to action link is required'),
  isExternal: z.boolean().default(false),
  isActive: z.boolean().default(true),
  sortOrder: z.number().min(0, 'Sort order must be positive').max(999, 'Sort order must be less than 1000').default(0),
});

type AdvertisementFormData = z.infer<typeof advertisementSchema>;

interface AdvertisementFormProps {
  advertisement?: Advertisement;
  onSubmit: (advertisement: Advertisement) => void;
  onCancel: () => void;
  mode: 'create' | 'edit';
}

export default function AdvertisementForm({
  advertisement,
  onSubmit,
  onCancel,
  mode,
}: AdvertisementFormProps) {
  const [backgroundImageUploading, setBackgroundImageUploading] = useState(false);
  const [pngImageUploading, setPngImageUploading] = useState(false);
  const [currentBackgroundImage, setCurrentBackgroundImage] = useState<string | undefined>(
    advertisement?.backgroundImage?.uploadUrl
  );
  const [currentPngImage, setCurrentPngImage] = useState<string | undefined>(
    advertisement?.pngImage?.uploadUrl
  );
  const loading = useLoading();

  const form = useForm<AdvertisementFormData>({
    resolver: zodResolver(advertisementSchema),
    defaultValues: {
      title: advertisement?.title || '',
      subtitle: advertisement?.subtitle || '',
      description: advertisement?.description || '',
      callToActionText: advertisement?.callToActionText || '',
      callToActionLink: advertisement?.callToActionLink || '',
      isExternal: advertisement?.isExternal ?? false,
      isActive: advertisement?.isActive ?? true,
      sortOrder: advertisement?.sortOrder || 0,
    },
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = form;

  // Reset form values when advertisement changes (important for edit mode)
  useEffect(() => {
    if (advertisement && mode === 'edit') {
      reset({
        title: advertisement.title || '',
        subtitle: advertisement.subtitle || '',
        description: advertisement.description || '',
        callToActionText: advertisement.callToActionText || '',
        callToActionLink: advertisement.callToActionLink || '',
        isExternal: advertisement.isExternal ?? false,
        isActive: advertisement.isActive ?? true,
        sortOrder: advertisement.sortOrder || 0,
      });

      setCurrentBackgroundImage(advertisement.backgroundImage?.uploadUrl);
      setCurrentPngImage(advertisement.pngImage?.uploadUrl);
    }
  }, [advertisement, mode, reset]);

  // Background image upload handlers
  const handleBackgroundImageUpload = async (file: File) => {
    if (!advertisement?.id && mode === 'edit') {
      throw new Error('Advertisement ID is required for image upload');
    }

    setBackgroundImageUploading(true);
    try {
      if (mode === 'edit' && advertisement?.id) {
        const uploadResponse = await adminApi.advertisements.uploadBackgroundImage(String(advertisement.id), {
          fileName: file.name,
          fileType: file.type,
        });

        if (!uploadResponse.success) {
          throw new Error(uploadResponse.message || 'Failed to get upload URL');
        }

        await adminApi.advertisements.uploadFileToS3(
          uploadResponse.data.uploadUrl,
          uploadResponse.data.uploadFields,
          file
        );

        setCurrentBackgroundImage(uploadResponse.data.file.uploadUrl);
        toast.success('Background image uploaded successfully');
      } else {
        toast('Background image will be uploaded after advertisement is created', { icon: 'ℹ️' });
      }
    } catch (error) {
      console.error('Background image upload error:', error);
      throw error;
    } finally {
      setBackgroundImageUploading(false);
    }
  };

  const handleBackgroundImageRemove = async () => {
    if (!advertisement?.id || mode !== 'edit') {
      setCurrentBackgroundImage(undefined);
      return;
    }

    try {
      const response = await adminApi.advertisements.removeBackgroundImage(String(advertisement.id));
      if (response.success) {
        setCurrentBackgroundImage(undefined);
        toast.success('Background image removed successfully');
      }
    } catch (error) {
      handleError(error, { action: 'remove_background_image' });
    }
  };

  // PNG image upload handlers
  const handlePngImageUpload = async (file: File) => {
    if (!advertisement?.id && mode === 'edit') {
      throw new Error('Advertisement ID is required for image upload');
    }

    setPngImageUploading(true);
    try {
      if (mode === 'edit' && advertisement?.id) {
        const uploadResponse = await adminApi.advertisements.uploadPngImage(String(advertisement.id), {
          fileName: file.name,
          fileType: file.type,
        });

        if (!uploadResponse.success) {
          throw new Error(uploadResponse.message || 'Failed to get upload URL');
        }

        await adminApi.advertisements.uploadFileToS3(
          uploadResponse.data.uploadUrl,
          uploadResponse.data.uploadFields,
          file
        );

        setCurrentPngImage(uploadResponse.data.file.uploadUrl);
        toast.success('PNG image uploaded successfully');
      } else {
        toast('PNG image will be uploaded after advertisement is created', { icon: 'ℹ️' });
      }
    } catch (error) {
      console.error('PNG image upload error:', error);
      throw error;
    } finally {
      setPngImageUploading(false);
    }
  };

  const handlePngImageRemove = async () => {
    if (!advertisement?.id || mode !== 'edit') {
      setCurrentPngImage(undefined);
      return;
    }

    try {
      const response = await adminApi.advertisements.removePngImage(String(advertisement.id));
      if (response.success) {
        setCurrentPngImage(undefined);
        toast.success('PNG image removed successfully');
      }
    } catch (error) {
      handleError(error, { action: 'remove_png_image' });
    }
  };

  const onFormSubmit = async (data: AdvertisementFormData) => {
    try {
      loading.startLoading({ message: mode === 'create' ? 'Creating advertisement...' : 'Updating advertisement...' });

      let response;
      if (mode === 'create') {
        response = await adminApi.advertisements.createAdvertisement(data);
      } else if (advertisement?.id) {
        response = await adminApi.advertisements.updateAdvertisement(String(advertisement.id), data);
      } else {
        throw new Error('Advertisement ID is required for update');
      }

      if (response.success) {
        toast.success(`Advertisement ${mode === 'create' ? 'created' : 'updated'} successfully`);
        onSubmit(response.data);
      } else {
        throw new Error(response.message || `Failed to ${mode} advertisement`);
      }
    } catch (error) {
      handleError(error, { action: `${mode}_advertisement` });
    } finally {
      loading.stopLoading();
    }
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Basic Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Title */}
          <div className="md:col-span-2">
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Title *
            </label>
            <input
              type="text"
              id="title"
              {...register('title')}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="Enter advertisement title"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.title.message}</p>
            )}
          </div>

          {/* Subtitle */}
          <div className="md:col-span-2">
            <label htmlFor="subtitle" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Subtitle
            </label>
            <input
              type="text"
              id="subtitle"
              {...register('subtitle')}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="Enter advertisement subtitle"
            />
            {errors.subtitle && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.subtitle.message}</p>
            )}
          </div>

          {/* Description */}
          <div className="md:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              id="description"
              rows={3}
              {...register('description')}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="Enter advertisement description"
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.description.message}</p>
            )}
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Call to Action
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* CTA Text */}
          <div>
            <label htmlFor="callToActionText" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Button Text *
            </label>
            <input
              type="text"
              id="callToActionText"
              {...register('callToActionText')}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="e.g., Book Now, Learn More"
            />
            {errors.callToActionText && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.callToActionText.message}</p>
            )}
          </div>

          {/* CTA Link */}
          <div>
            <label htmlFor="callToActionLink" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Link URL *
            </label>
            <input
              type="url"
              id="callToActionLink"
              {...register('callToActionLink')}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="https://example.com"
            />
            {errors.callToActionLink && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.callToActionLink.message}</p>
            )}
          </div>

          {/* External Link Toggle */}
          <div className="md:col-span-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isExternal"
                {...register('isExternal')}
                className="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded"
              />
              <label htmlFor="isExternal" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Open link in new tab/window
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Settings
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Sort Order */}
          <div>
            <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Sort Order
            </label>
            <input
              type="number"
              id="sortOrder"
              min="0"
              max="999"
              {...register('sortOrder', { valueAsNumber: true })}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="0"
            />
            {errors.sortOrder && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.sortOrder.message}</p>
            )}
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Lower numbers appear first
            </p>
          </div>

          {/* Active Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                {...register('isActive')}
                className="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Active (visible to users)
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Images */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Images
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Background Image */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Background Image
            </label>
            <ImageUpload
              currentImage={currentBackgroundImage}
              onImageUpload={handleBackgroundImageUpload}
              onImageRemove={handleBackgroundImageRemove}
              loading={backgroundImageUploading}
              placeholder="Upload background image"
              className="h-32"
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Recommended: 800x400px, JPG or PNG
            </p>
          </div>

          {/* PNG Image */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              PNG Image/Icon
            </label>
            <ImageUpload
              currentImage={currentPngImage}
              onImageUpload={handlePngImageUpload}
              onImageRemove={handlePngImageRemove}
              loading={pngImageUploading}
              placeholder="Upload PNG image or icon"
              className="h-32"
              acceptedTypes={['image/png']}
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              PNG format only, transparent background supported
            </p>
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-4 pt-6">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          Cancel
        </button>
        <LoadingButton
          type="submit"
          loading={loading.isLoading}
          className="px-4 py-2 text-sm font-medium text-white bg-brand-600 border border-transparent rounded-lg hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"
        >
          {mode === 'create' ? 'Create Advertisement' : 'Update Advertisement'}
        </LoadingButton>
      </div>
    </form>
  );
}

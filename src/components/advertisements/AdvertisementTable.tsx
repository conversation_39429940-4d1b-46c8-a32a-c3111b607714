import React, { useState } from 'react';
import { Advertisement } from '../../types';
import ResponsiveTable from '../common/ResponsiveTable';
import { TableColumn } from '../../types';
import { formatDate } from '../../utils/dateUtils';

interface AdvertisementTableProps {
  advertisements: Advertisement[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number) => void;
  };
  onAdvertisementSelect: (advertisement: Advertisement) => void;
  onAdvertisementEdit: (advertisement: Advertisement) => void;
  onAdvertisementDelete: (advertisement: Advertisement) => void;
  onToggleStatus: (advertisement: Advertisement) => void;
  onSearch: (search: string) => void;
  onFilterChange: (key: string, value: any) => void;
  filters: {
    search: string;
    isActive?: boolean;
  };
}

export default function AdvertisementTable({
  advertisements,
  loading,
  pagination,
  onAdvertisementSelect,
  onAdvertisementEdit,
  onAdvertisementDelete,
  onToggleStatus,
  onSearch,
  onFilterChange,
  filters,
}: AdvertisementTableProps) {
  const [searchTerm, setSearchTerm] = useState(filters.search);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchTerm);
  };

  const tableColumns: TableColumn<Advertisement>[] = [
    {
      key: 'title',
      title: 'Title',
      sortable: true,
      render: (value, record) => (
        <div className="flex items-center space-x-3">
          {record.backgroundImage && (
            <img
              src={record.backgroundImage.uploadUrl}
              alt={record.title}
              className="w-12 h-8 object-cover rounded"
            />
          )}
          <div>
            <div className="font-medium text-gray-900 dark:text-white">
              {record.title}
            </div>
            {record.subtitle && (
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {record.subtitle}
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'callToActionText',
      title: 'CTA',
      render: (value, record) => (
        <div className="flex items-center space-x-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-100 text-brand-800 dark:bg-brand-900 dark:text-brand-200">
            {record.callToActionText}
          </span>
          {record.isExternal && (
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          )}
        </div>
      ),
    },
    {
      key: 'isActive',
      title: 'Status',
      render: (value, record) => (
        <button
          onClick={() => onToggleStatus(record)}
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors ${
            record.isActive
              ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-200'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
          }`}
        >
          <span className={`w-2 h-2 rounded-full mr-1.5 ${
            record.isActive ? 'bg-green-400' : 'bg-gray-400'
          }`} />
          {record.isActive ? 'Active' : 'Inactive'}
        </button>
      ),
    },
    {
      key: 'sortOrder',
      title: 'Order',
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: 'createdAt',
      title: 'Created',
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {formatDate(value)}
        </span>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onAdvertisementSelect(record)}
            className="text-brand-600 hover:text-brand-900 dark:text-brand-400 dark:hover:text-brand-300"
            title="View Details"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
          <button
            onClick={() => onAdvertisementEdit(record)}
            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
            title="Edit"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </button>
          <button
            onClick={() => onAdvertisementDelete(record)}
            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
            title="Delete"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      ),
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      {/* Search and Filters */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <form onSubmit={handleSearchSubmit} className="flex-1 max-w-lg">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search advertisements..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-brand-500 focus:border-brand-500"
              />
            </div>
          </form>

          <div className="flex items-center space-x-4">
            <select
              value={filters.isActive === undefined ? 'all' : filters.isActive ? 'active' : 'inactive'}
              onChange={(e) => {
                const value = e.target.value;
                onFilterChange('isActive', value === 'all' ? undefined : value === 'active');
              }}
              className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-brand-500 focus:border-brand-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Table */}
      <ResponsiveTable
        data={advertisements}
        columns={tableColumns}
        loading={loading}
        pagination={pagination}
        onRowClick={onAdvertisementSelect}
        emptyText="No advertisements found"
        rowKey="id"
      />
    </div>
  );
}

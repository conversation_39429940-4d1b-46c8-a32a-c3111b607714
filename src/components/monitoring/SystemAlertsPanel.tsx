import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';

interface SystemAlert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  source: string;
  acknowledged: boolean;
  resolvedAt?: string;
  metadata?: Record<string, any>;
}

interface SystemAlertsPanel {
  className?: string;
  maxAlerts?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export default function SystemAlertsPanel({
  className = '',
  maxAlerts = 10,
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
}: SystemAlertsPanel) {
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [filter, setFilter] = useState<'all' | 'critical' | 'warning' | 'info'>('all');
  const [showResolved, setShowResolved] = useState(false);
  const loading = useLoading();

  useEffect(() => {
    fetchAlerts();
    
    if (autoRefresh) {
      const interval = setInterval(fetchAlerts, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const fetchAlerts = async () => {
    try {
      const response = await adminApi.system.getSystemAlerts?.();
      
      if (response?.success) {
        setAlerts(response.data);
      } else {
        // Fallback data for development
        setAlerts([
          {
            id: '1',
            type: 'critical',
            title: 'Database Connection Pool Exhausted',
            message: 'Database connection pool has reached maximum capacity. New connections are being queued.',
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            source: 'database',
            acknowledged: false,
            metadata: { connectionCount: 100, maxConnections: 100 },
          },
          {
            id: '2',
            type: 'warning',
            title: 'High Memory Usage',
            message: 'System memory usage has exceeded 80% threshold.',
            timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
            source: 'system',
            acknowledged: false,
            metadata: { memoryUsage: 85.2, threshold: 80 },
          },
          {
            id: '3',
            type: 'warning',
            title: 'Storage Service Degraded',
            message: 'File upload service is experiencing increased response times.',
            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            source: 'storage',
            acknowledged: true,
            metadata: { avgResponseTime: 2500, normalResponseTime: 500 },
          },
          {
            id: '4',
            type: 'info',
            title: 'Scheduled Maintenance Completed',
            message: 'Database maintenance window completed successfully.',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            source: 'maintenance',
            acknowledged: true,
            resolvedAt: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
          },
          {
            id: '5',
            type: 'critical',
            title: 'Payment Gateway Timeout',
            message: 'Payment processing service is not responding to health checks.',
            timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
            source: 'payment',
            acknowledged: false,
            metadata: { lastSuccessfulCheck: new Date(Date.now() - 45 * 60 * 1000).toISOString() },
          },
        ]);
      }
    } catch (error) {
      handleError(error, { action: 'fetch_system_alerts', silent: true });
    }
  };

  const acknowledgeAlert = async (alertId: string) => {
    try {
      await adminApi.system.acknowledgeAlert?.(alertId);
      setAlerts(prev => prev.map(alert => 
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      ));
    } catch (error) {
      handleError(error, { action: 'acknowledge_alert' });
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      await adminApi.system.resolveAlert?.(alertId);
      setAlerts(prev => prev.map(alert => 
        alert.id === alertId ? { ...alert, resolvedAt: new Date().toISOString() } : alert
      ));
    } catch (error) {
      handleError(error, { action: 'resolve_alert' });
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return (
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const getAlertBorderColor = (type: string) => {
    switch (type) {
      case 'critical':
        return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
      case 'warning':
        return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'info':
        return 'border-l-blue-500 bg-blue-50 dark:bg-blue-900/20';
      default:
        return 'border-l-gray-500 bg-gray-50 dark:bg-gray-700';
    }
  };

  const filteredAlerts = alerts
    .filter(alert => {
      if (filter !== 'all' && alert.type !== filter) return false;
      if (!showResolved && alert.resolvedAt) return false;
      return true;
    })
    .slice(0, maxAlerts);

  const unacknowledgedCount = alerts.filter(alert => !alert.acknowledged && !alert.resolvedAt).length;
  const criticalCount = alerts.filter(alert => alert.type === 'critical' && !alert.resolvedAt).length;

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              System Alerts
            </h3>
            {unacknowledgedCount > 0 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                {unacknowledgedCount} unacknowledged
              </span>
            )}
            {criticalCount > 0 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-600 text-white">
                {criticalCount} critical
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Alerts</option>
              <option value="critical">Critical</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
            </select>
            
            <button
              onClick={() => setShowResolved(!showResolved)}
              className={`px-3 py-1 text-sm rounded ${
                showResolved
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              {showResolved ? 'Hide Resolved' : 'Show Resolved'}
            </button>
            
            <button
              onClick={fetchAlerts}
              className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              title="Refresh alerts"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Alerts List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredAlerts.length === 0 ? (
          <div className="p-6 text-center">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Alerts
            </h4>
            <p className="text-gray-600 dark:text-gray-400">
              {filter === 'all' ? 'All systems are operating normally' : `No ${filter} alerts found`}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredAlerts.map((alert) => (
              <div
                key={alert.id}
                className={`p-4 border-l-4 ${getAlertBorderColor(alert.type)} ${
                  alert.acknowledged ? 'opacity-75' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {alert.title}
                        </h4>
                        {alert.acknowledged && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Acknowledged
                          </span>
                        )}
                        {alert.resolvedAt && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                            Resolved
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                        {alert.message}
                      </p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        <span>Source: {alert.source}</span>
                        <span>{new Date(alert.timestamp).toLocaleString()}</span>
                        {alert.resolvedAt && (
                          <span>Resolved: {new Date(alert.resolvedAt).toLocaleString()}</span>
                        )}
                      </div>
                      {alert.metadata && (
                        <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                          <pre className="text-gray-600 dark:text-gray-400">
                            {JSON.stringify(alert.metadata, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {!alert.resolvedAt && (
                    <div className="flex items-center space-x-2 ml-4">
                      {!alert.acknowledged && (
                        <button
                          onClick={() => acknowledgeAlert(alert.id)}
                          className="px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                        >
                          Acknowledge
                        </button>
                      )}
                      <button
                        onClick={() => resolveAlert(alert.id)}
                        className="px-2 py-1 text-xs font-medium text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
                      >
                        Resolve
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {alerts.length > maxAlerts && (
        <div className="px-6 py-3 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Showing {filteredAlerts.length} of {alerts.length} alerts
          </p>
        </div>
      )}
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { ProviderCategory } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import LoadingSpinner from '../common/LoadingSpinner';

interface CategoryTreeViewProps {
  onCategorySelect?: (category: ProviderCategory) => void;
  onCategoryEdit?: (category: ProviderCategory) => void;
  onCategoryDelete?: (category: ProviderCategory) => void;
  selectedCategoryId?: string;
  className?: string;
}

interface CategoryTreeNodeProps {
  category: ProviderCategory;
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  onToggle: (categoryId: string) => void;
  onSelect: (category: ProviderCategory) => void;
  onEdit?: (category: ProviderCategory) => void;
  onDelete?: (category: ProviderCategory) => void;
  children?: ProviderCategory[];
}

function CategoryTreeNode({
  category,
  level,
  isExpanded,
  isSelected,
  onToggle,
  onSelect,
  onEdit,
  onDelete,
  children = [],
}: CategoryTreeNodeProps) {
  const hasChildren = children.length > 0;
  const indentLevel = level * 20;

  const getCategoryIcon = (category: ProviderCategory) => {
    // Return appropriate icon based on category type or use default
    const categoryName = category?.title?.toLowerCase() || '';
    switch (categoryName) {
      case 'beauty':
      case 'beauty & wellness':
        return '💄';
      case 'health':
      case 'healthcare':
        return '🏥';
      case 'fitness':
      case 'sports':
        return '💪';
      case 'automotive':
      case 'car services':
        return '🚗';
      case 'home services':
      case 'cleaning':
        return '🏠';
      case 'education':
      case 'tutoring':
        return '📚';
      case 'food':
      case 'restaurants':
        return '🍽️';
      case 'technology':
      case 'it services':
        return '💻';
      default:
        return '📁';
    }
  };

  return (
    <div className="category-tree-node">
      <div
        className={`
          flex items-center py-2 px-3 rounded-lg cursor-pointer transition-colors
          ${isSelected 
            ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100' 
            : 'hover:bg-gray-100 dark:hover:bg-gray-700'
          }
        `}
        style={{ marginLeft: `${indentLevel}px` }}
        onClick={() => onSelect(category)}
      >
        {/* Expand/Collapse Button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            if (hasChildren) {
              onToggle(category.id);
            }
          }}
          className={`
            mr-2 w-4 h-4 flex items-center justify-center
            ${hasChildren ? 'text-gray-500 hover:text-gray-700' : 'invisible'}
          `}
        >
          {hasChildren && (
            <svg
              className={`w-3 h-3 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          )}
        </button>

        {/* Category Icon/Image */}
        <div className="mr-3 flex-shrink-0">
          {category.image?.uploadUrl ? (
            <img
              src={category.image.uploadUrl}
              alt={category.title}
              className="w-8 h-8 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
            />
          ) : category.metadata?.icon ? (
            <span className="text-lg">{category.metadata.icon}</span>
          ) : (
            <span className="text-lg">{getCategoryIcon(category)}</span>
          )}
        </div>

        {/* Category Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {category.title}
            </span>
            {category.isActive && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Active
              </span>
            )}
          </div>
          {category.description && (
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {category.description}
            </p>
          )}
        </div>

        {/* Provider Count */}
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mr-3">
          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
          {category._count?.providers || category.providerCount || 0}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {onEdit && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onEdit(category);
              }}
              className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
              title="Edit category"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
          )}
          {onDelete && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete(category);
              }}
              className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
              title="Delete category"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="category-children">
          {children.map((child) => (
            <CategoryTreeNode
              key={child.id}
              category={child}
              level={level + 1}
              isExpanded={false} // Children start collapsed
              isSelected={false} // Only one selection at a time
              onToggle={onToggle}
              onSelect={onSelect}
              onEdit={onEdit}
              onDelete={onDelete}
              children={[]} // Simplified for now - can be extended for deeper nesting
            />
          ))}
        </div>
      )}
    </div>
  );
}

export default function CategoryTreeView({
  onCategorySelect,
  onCategoryEdit,
  onCategoryDelete,
  selectedCategoryId,
  className = '',
}: CategoryTreeViewProps) {
  const [categories, setCategories] = useState<ProviderCategory[]>([]);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const loading = useLoading('Loading categories...');

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      loading.startLoading();
      const response = await adminApi.categories.getCategories();
      
      if (response.success) {
        setCategories(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch categories');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_categories' });
      // Set fallback data for development
      setCategories([
        {
          id: '1',
          title: 'Beauty & Wellness',
          description: 'Beauty salons, spas, and wellness services',
          isActive: true,
          parentId: undefined,
          _count: { providers: 45, children: 2 },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          title: 'Hair Services',
          description: 'Hair cutting, styling, and treatments',
          isActive: true,
          parentId: '1',
          _count: { providers: 25, children: 0 },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          title: 'Nail Services',
          description: 'Manicure, pedicure, and nail art',
          isActive: true,
          parentId: '1',
          _count: { providers: 15, children: 0 },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          title: 'Healthcare',
          description: 'Medical and healthcare services',
          isActive: true,
          parentId: undefined,
          _count: { providers: 32, children: 1 },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '5',
          title: 'Dental Care',
          description: 'Dental clinics and oral health services',
          isActive: true,
          parentId: '4',
          _count: { providers: 18, children: 0 },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '6',
          title: 'Fitness & Sports',
          description: 'Gyms, personal training, and sports facilities',
          isActive: true,
          parentId: undefined,
          _count: { providers: 28, children: 0 },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ]);
    } finally {
      loading.stopLoading();
    }
  };

  const handleToggleExpand = (categoryId: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  const handleCategorySelect = (category: ProviderCategory) => {
    onCategorySelect?.(category);
  };

  // Build hierarchical structure
  const buildCategoryTree = (categories: ProviderCategory[]): ProviderCategory[] => {
    const categoryMap = new Map<string, ProviderCategory & { children: ProviderCategory[] }>();
    const rootCategories: (ProviderCategory & { children: ProviderCategory[] })[] = [];

    // First pass: create map with children arrays
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // Second pass: build hierarchy
    categories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id)!;
      
      if (category.parentId && categoryMap.has(category.parentId)) {
        categoryMap.get(category.parentId)!.children.push(categoryWithChildren);
      } else {
        rootCategories.push(categoryWithChildren);
      }
    });

    return rootCategories;
  };

  // Filter categories based on search term
  const filteredCategories = categories.filter(category =>
    category?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category?.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const categoryTree = buildCategoryTree(searchTerm ? filteredCategories : categories);

  if (loading.isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${className}`}>
        <LoadingSpinner size="lg" message="Loading categories..." />
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Categories ({categories.length})
            </h3>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Manage provider service categories
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={fetchCategories}
              className="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
            
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm">
              Add Category
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search categories..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          />
          <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* Tree View */}
      <div className="p-4 max-h-96 overflow-y-auto">
        {categoryTree.length === 0 ? (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm ? 'No categories found matching your search' : 'No categories available'}
            </p>
          </div>
        ) : (
          <div className="space-y-1 group">
            {categoryTree.map((category) => (
              <CategoryTreeNode
                key={category.id}
                category={category}
                level={0}
                isExpanded={expandedCategories.has(category.id)}
                isSelected={selectedCategoryId === category.id}
                onToggle={handleToggleExpand}
                onSelect={handleCategorySelect}
                onEdit={onCategoryEdit}
                onDelete={onCategoryDelete}
                children={(category as any).children || []}
              />
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-b-lg">
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
          <span>
            {categories.filter(c => c.isActive).length} active categories
          </span>
          <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
            Expand All
          </button>
        </div>
      </div>
    </div>
  );
}

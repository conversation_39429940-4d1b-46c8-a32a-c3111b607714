import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import ImageUpload from '../../common/ImageUpload';
import { adminApi } from '../../../services/adminApi';

// Mock the adminApi
vi.mock('../../../services/adminApi', () => ({
  adminApi: {
    categories: {
      uploadCategoryImage: vi.fn(),
      uploadFileToS3: vi.fn(),
      removeCategoryImage: vi.fn(),
    },
  },
}));

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock react-dropzone
vi.mock('react-dropzone', () => ({
  useDropzone: vi.fn(() => ({
    getRootProps: () => ({ 'data-testid': 'dropzone' }),
    getInputProps: () => ({ 'data-testid': 'file-input' }),
    isDragActive: false,
  })),
}));

describe('CategoryImageUpload', () => {
  const mockOnImageUpload = vi.fn();
  const mockOnImageRemove = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders upload placeholder when no image is provided', () => {
    render(
      <ImageUpload
        onImageUpload={mockOnImageUpload}
        onImageRemove={mockOnImageRemove}
      />
    );

    expect(screen.getByText(/click to upload or drag and drop/i)).toBeInTheDocument();
    expect(screen.getByText(/jpeg, png, webp up to/i)).toBeInTheDocument();
  });

  it('renders current image when provided', () => {
    const imageUrl = 'https://example.com/image.jpg';
    
    render(
      <ImageUpload
        currentImage={imageUrl}
        onImageUpload={mockOnImageUpload}
        onImageRemove={mockOnImageRemove}
      />
    );

    const image = screen.getByRole('img', { name: /preview/i });
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', imageUrl);
  });

  it('shows loading state during upload', () => {
    render(
      <ImageUpload
        onImageUpload={mockOnImageUpload}
        onImageRemove={mockOnImageRemove}
        loading={true}
      />
    );

    expect(screen.getByText(/uploading/i)).toBeInTheDocument();
  });

  it('calls onImageUpload when file is selected', async () => {
    const user = userEvent.setup();
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    render(
      <ImageUpload
        onImageUpload={mockOnImageUpload}
        onImageRemove={mockOnImageRemove}
      />
    );

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(mockOnImageUpload).toHaveBeenCalledWith(file);
    });
  });

  it('calls onImageRemove when remove button is clicked', async () => {
    const user = userEvent.setup();
    const imageUrl = 'https://example.com/image.jpg';

    render(
      <ImageUpload
        currentImage={imageUrl}
        onImageUpload={mockOnImageUpload}
        onImageRemove={mockOnImageRemove}
      />
    );

    // Hover over the image to show controls
    const imageContainer = screen.getByRole('img', { name: /preview/i }).parentElement;
    await user.hover(imageContainer!);

    const removeButton = screen.getByRole('button', { name: /remove/i });
    await user.click(removeButton);

    expect(mockOnImageRemove).toHaveBeenCalled();
  });

  it('validates file type and shows error for invalid files', async () => {
    const user = userEvent.setup();
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });

    // Mock console.error to avoid test output noise
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(
      <ImageUpload
        onImageUpload={mockOnImageUpload}
        onImageRemove={mockOnImageRemove}
      />
    );

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, invalidFile);

    // The component should not call onImageUpload for invalid files
    expect(mockOnImageUpload).not.toHaveBeenCalled();

    consoleSpy.mockRestore();
  });

  it('validates file size and shows error for large files', async () => {
    const user = userEvent.setup();
    // Create a file larger than the default max size (10MB)
    const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.jpg', { 
      type: 'image/jpeg' 
    });

    // Mock console.error to avoid test output noise
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(
      <ImageUpload
        onImageUpload={mockOnImageUpload}
        onImageRemove={mockOnImageRemove}
      />
    );

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, largeFile);

    // The component should not call onImageUpload for oversized files
    expect(mockOnImageUpload).not.toHaveBeenCalled();

    consoleSpy.mockRestore();
  });

  it('disables upload when disabled prop is true', () => {
    render(
      <ImageUpload
        onImageUpload={mockOnImageUpload}
        onImageRemove={mockOnImageRemove}
        disabled={true}
      />
    );

    const dropzone = screen.getByTestId('dropzone');
    expect(dropzone).toHaveClass('cursor-not-allowed');
  });

  it('shows custom placeholder text when provided', () => {
    const customPlaceholder = 'Upload your category image here';

    render(
      <ImageUpload
        onImageUpload={mockOnImageUpload}
        onImageRemove={mockOnImageRemove}
        placeholder={customPlaceholder}
      />
    );

    expect(screen.getByText(customPlaceholder)).toBeInTheDocument();
  });
});

describe('Category Image Upload Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('handles successful image upload flow', async () => {
    const mockUploadResponse = {
      success: true,
      data: {
        uploadUrl: 'https://s3.amazonaws.com/upload-url',
        uploadFields: {
          key: 'test-key',
          'Content-Type': 'image/jpeg',
        },
        file: {
          id: 'file-123',
          name: 'test.jpg',
          type: 'image/jpeg',
          key: 'test-key',
          uploadUrl: 'https://example.com/uploaded-image.jpg',
        },
        category: {
          id: 'cat-123',
          name: 'Test Category',
        },
      },
    };

    (adminApi.categories.uploadCategoryImage as any).mockResolvedValue(mockUploadResponse);
    (adminApi.categories.uploadFileToS3 as any).mockResolvedValue(undefined);

    const categoryId = 'cat-123';
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    // Simulate the upload process
    const uploadResponse = await adminApi.categories.uploadCategoryImage(categoryId, {
      fileName: file.name,
      fileType: file.type,
    });

    expect(uploadResponse.success).toBe(true);
    expect(uploadResponse.data.file.uploadUrl).toBe('https://example.com/uploaded-image.jpg');

    await adminApi.categories.uploadFileToS3(
      uploadResponse.data.uploadUrl,
      uploadResponse.data.uploadFields,
      file
    );

    expect(adminApi.categories.uploadFileToS3).toHaveBeenCalledWith(
      'https://s3.amazonaws.com/upload-url',
      { key: 'test-key', 'Content-Type': 'image/jpeg' },
      file
    );
  });

  it('handles image removal flow', async () => {
    const mockRemoveResponse = {
      success: true,
      data: {
        id: 'cat-123',
        name: 'Test Category',
        imageId: null,
      },
    };

    (adminApi.categories.removeCategoryImage as any).mockResolvedValue(mockRemoveResponse);

    const categoryId = 'cat-123';
    const response = await adminApi.categories.removeCategoryImage(categoryId);

    expect(response.success).toBe(true);
    expect(response.data.imageId).toBe(null);
  });
});

# Category Image Upload Implementation

This document describes the implementation of image upload functionality for provider categories in the Dalti admin panel.

## Overview

The category image upload feature allows administrators to upload, view, and manage images for provider categories. This enhances the visual appeal of categories and improves user experience.

## Components

### 1. ImageUpload Component (`src/components/common/ImageUpload.tsx`)

A reusable component that handles image upload functionality with the following features:

- **Drag & Drop Support**: Users can drag and drop images or click to select
- **File Validation**: Validates file type and size according to brand configuration
- **Image Preview**: Shows current image with hover controls for change/remove
- **Loading States**: Visual feedback during upload operations
- **Error Handling**: User-friendly error messages for failed uploads

#### Props:
- `currentImage?: string` - URL of the current image
- `onImageUpload: (file: File) => Promise<void>` - Callback for image upload
- `onImageRemove?: () => Promise<void>` - Callback for image removal
- `loading?: boolean` - Loading state
- `disabled?: boolean` - Disabled state
- `maxSize?: number` - Maximum file size (defaults to brand config)
- `acceptedTypes?: string[]` - Accepted file types (defaults to brand config)

### 2. CategoryForm Updates (`src/components/categories/CategoryForm.tsx`)

Enhanced the existing CategoryForm component to include image upload:

- **Image Upload Section**: Added after the description field
- **Mode-Aware Behavior**: 
  - In edit mode: Uploads images immediately
  - In create mode: Shows placeholder (images can be uploaded after creation)
- **State Management**: Tracks image upload state and current image URL

### 3. CategoryTreeView Updates (`src/components/categories/CategoryTreeView.tsx`)

Updated to display category images:

- **Image Display**: Shows category image if available, falls back to metadata icon or default icon
- **Responsive Design**: Images are properly sized and styled for the tree view

## API Integration

### New API Methods in `categoriesApi`:

1. **`uploadCategoryImage(categoryId, imageData)`**
   - Requests a pre-signed upload URL from the server
   - Returns upload URL, fields, and file metadata

2. **`getCategoryImage(categoryId)`**
   - Retrieves category image information
   - Returns category and image data

3. **`removeCategoryImage(categoryId)`**
   - Removes the image from a category
   - Returns updated category data

4. **`uploadFileToS3(uploadUrl, uploadFields, file)`**
   - Uploads file directly to S3 using pre-signed URL
   - Handles the actual file upload process

### Upload Process:

1. **Request Upload URL**: Client requests pre-signed URL from API
2. **Direct Upload**: File is uploaded directly to S3 using the pre-signed URL
3. **Database Update**: Server automatically updates the category record with image reference

## Type Definitions

### New Types Added:

```typescript
interface FileUploadResponse {
  id: string;
  name: string;
  type: string;
  key: string;
  uploadUrl?: string;
  createdAt?: string;
}

interface ImageUploadRequest {
  fileName: string;
  fileType: string;
}

interface CategoryImageUploadResponse {
  uploadUrl: string;
  uploadFields: Record<string, string>;
  file: FileUploadResponse;
  category: ProviderCategory;
}
```

### Updated ProviderCategory Type:

```typescript
interface ProviderCategory {
  // ... existing fields
  imageId?: string;
  image?: FileUploadResponse;
}
```

## Usage Examples

### Basic Image Upload in CategoryForm:

```tsx
<ImageUpload
  currentImage={currentImage}
  onImageUpload={handleImageUpload}
  onImageRemove={handleImageRemove}
  loading={imageUploading}
  disabled={loading.isLoading}
  placeholder="Upload a category image to make it more visually appealing"
/>
```

### Custom Image Upload Handler:

```tsx
const handleImageUpload = async (file: File) => {
  try {
    setImageUploading(true);
    
    // Request upload URL
    const uploadResponse = await adminApi.categories.uploadCategoryImage(categoryId, {
      fileName: file.name,
      fileType: file.type,
    });

    // Upload to S3
    await adminApi.categories.uploadFileToS3(
      uploadResponse.data.uploadUrl,
      uploadResponse.data.uploadFields,
      file
    );

    // Update UI
    setCurrentImage(uploadResponse.data.file.uploadUrl);
    toast.success('Image uploaded successfully');
  } catch (error) {
    toast.error('Failed to upload image');
    throw error;
  } finally {
    setImageUploading(false);
  }
};
```

## Configuration

### File Upload Limits (from `src/constants/brand.ts`):

```typescript
uploads: {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
}
```

## Best Practices

1. **Image Optimization**: Recommend 400x300px or larger with 4:3 aspect ratio
2. **File Size**: Keep images under 10MB for optimal performance
3. **Error Handling**: Always provide user feedback for upload operations
4. **Loading States**: Show loading indicators during upload operations
5. **Fallback Display**: Always provide fallback icons when images aren't available

## Future Enhancements

1. **Image Cropping**: Add image cropping functionality before upload
2. **Multiple Images**: Support for multiple images per category
3. **Image Optimization**: Automatic image compression and optimization
4. **Bulk Upload**: Upload images for multiple categories at once
5. **Image Gallery**: Browse and select from previously uploaded images

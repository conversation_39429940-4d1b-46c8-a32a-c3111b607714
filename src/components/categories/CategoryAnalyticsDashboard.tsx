import React, { useState, useEffect } from 'react';
import { ProviderCategory } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';

interface CategoryAnalytics {
  overview: {
    totalCategories: number;
    activeCategories: number;
    rootCategories: number;
    subcategories: number;
    averageProvidersPerCategory: number;
    mostPopularCategory: string;
    leastUsedCategory: string;
    categoryGrowthRate: number;
  };
  distribution: {
    categoryId: string;
    categoryName: string;
    providerCount: number;
    percentage: number;
    subcategoryCount: number;
    isActive: boolean;
    color?: string;
    icon?: string;
  }[];
  hierarchy: {
    depth: number;
    categoriesAtDepth: number;
    averageChildrenPerParent: number;
  }[];
  usage: {
    categoryId: string;
    categoryName: string;
    totalBookings: number;
    activeProviders: number;
    averageRating: number;
    revenueGenerated: number;
    growthTrend: 'up' | 'down' | 'stable';
    popularityScore: number;
  }[];
  trends: {
    date: string;
    newCategories: number;
    activatedCategories: number;
    deactivatedCategories: number;
    providerMigrations: number;
  }[];
  performance: {
    categoryId: string;
    categoryName: string;
    conversionRate: number;
    customerSatisfaction: number;
    averageBookingValue: number;
    repeatCustomerRate: number;
    seasonalTrends: { month: string; bookings: number }[];
  }[];
}

interface CategoryAnalyticsDashboardProps {
  categories: ProviderCategory[];
  className?: string;
}

export default function CategoryAnalyticsDashboard({
  categories,
  className = '',
}: CategoryAnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<CategoryAnalytics | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');
  const loading = useLoading();

  useEffect(() => {
    fetchAnalytics();
  }, [selectedPeriod, categories]);

  const fetchAnalytics = async () => {
    try {
      loading.startLoading({ message: 'Loading category analytics...' });
      const response = await adminApi.categories.getAnalytics(selectedPeriod);
      
      if (response.success) {
        setAnalytics(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch analytics');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_category_analytics' });
      // Set fallback data for development
      setAnalytics({
        overview: {
          totalCategories: categories.length,
          activeCategories: categories.filter(c => c.isActive).length,
          rootCategories: categories.filter(c => !c.parentId).length,
          subcategories: categories.filter(c => c.parentId).length,
          averageProvidersPerCategory: 12.5,
          mostPopularCategory: 'Beauty & Wellness',
          leastUsedCategory: 'Pet Services',
          categoryGrowthRate: 8.3,
        },
        distribution: categories.map((cat, index) => ({
          categoryId: cat.id,
          categoryName: cat.name,
          providerCount: Math.floor(Math.random() * 50) + 5,
          percentage: Math.floor(Math.random() * 25) + 5,
          subcategoryCount: categories.filter(c => c.parentId === cat.id).length,
          isActive: cat.isActive,
          color: cat.metadata?.color,
          icon: cat.metadata?.icon,
        })),
        hierarchy: [
          { depth: 0, categoriesAtDepth: categories.filter(c => !c.parentId).length, averageChildrenPerParent: 3.2 },
          { depth: 1, categoriesAtDepth: categories.filter(c => c.parentId).length, averageChildrenPerParent: 1.8 },
          { depth: 2, categoriesAtDepth: 0, averageChildrenPerParent: 0 },
        ],
        usage: categories.slice(0, 10).map(cat => ({
          categoryId: cat.id,
          categoryName: cat.name,
          totalBookings: Math.floor(Math.random() * 1000) + 100,
          activeProviders: Math.floor(Math.random() * 30) + 5,
          averageRating: 4.0 + Math.random() * 1.0,
          revenueGenerated: Math.floor(Math.random() * 50000) + 10000,
          growthTrend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as 'up' | 'down' | 'stable',
          popularityScore: Math.floor(Math.random() * 100) + 1,
        })),
        trends: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          newCategories: Math.floor(Math.random() * 3),
          activatedCategories: Math.floor(Math.random() * 2),
          deactivatedCategories: Math.floor(Math.random() * 1),
          providerMigrations: Math.floor(Math.random() * 5),
        })),
        performance: categories.slice(0, 8).map(cat => ({
          categoryId: cat.id,
          categoryName: cat.name,
          conversionRate: 0.15 + Math.random() * 0.25,
          customerSatisfaction: 4.0 + Math.random() * 1.0,
          averageBookingValue: Math.floor(Math.random() * 200) + 50,
          repeatCustomerRate: 0.3 + Math.random() * 0.4,
          seasonalTrends: Array.from({ length: 12 }, (_, i) => ({
            month: new Date(2024, i, 1).toLocaleDateString('en', { month: 'short' }),
            bookings: Math.floor(Math.random() * 100) + 20,
          })),
        })),
      });
    } finally {
      loading.stopLoading();
    }
  };

  const periods = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 3 Months' },
    { value: '1y', label: 'Last Year' },
  ];

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'distribution', name: 'Distribution', icon: '📈' },
    { id: 'usage', name: 'Usage', icon: '🎯' },
    { id: 'performance', name: 'Performance', icon: '⚡' },
    { id: 'trends', name: 'Trends', icon: '📉' },
  ];

  if (loading.isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            Unable to load category analytics
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Category Analytics
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Comprehensive insights into category performance, provider distribution, and usage patterns
            </p>
          </div>
          
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            {periods.map((period) => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }
              `}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <OverviewTab analytics={analytics} />
        )}
        {activeTab === 'distribution' && (
          <DistributionTab distribution={analytics.distribution} />
        )}
        {activeTab === 'usage' && (
          <UsageTab usage={analytics.usage} />
        )}
        {activeTab === 'performance' && (
          <PerformanceTab performance={analytics.performance} />
        )}
        {activeTab === 'trends' && (
          <TrendsTab trends={analytics.trends} />
        )}
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ analytics }: { analytics: CategoryAnalytics }) {
  const { overview, hierarchy } = analytics;
  
  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Categories</p>
              <p className="text-2xl font-semibold text-blue-900 dark:text-blue-100">{overview.totalCategories}</p>
              <p className="text-xs text-blue-700 dark:text-blue-300">+{overview.categoryGrowthRate}% growth</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
              <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-green-600 dark:text-green-400">Active Categories</p>
              <p className="text-2xl font-semibold text-green-900 dark:text-green-100">{overview.activeCategories}</p>
              <p className="text-xs text-green-700 dark:text-green-300">{((overview.activeCategories / overview.totalCategories) * 100).toFixed(1)}% of total</p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
              <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Avg Providers</p>
              <p className="text-2xl font-semibold text-purple-900 dark:text-purple-100">{overview.averageProvidersPerCategory}</p>
              <p className="text-xs text-purple-700 dark:text-purple-300">Per category</p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
              <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Most Popular</p>
              <p className="text-lg font-semibold text-yellow-900 dark:text-yellow-100">{overview.mostPopularCategory}</p>
              <p className="text-xs text-yellow-700 dark:text-yellow-300">Top category</p>
            </div>
          </div>
        </div>
      </div>

      {/* Category Hierarchy */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Category Hierarchy
          </h3>
          <div className="space-y-4">
            {hierarchy.map((level, index) => (
              <div key={level.depth} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
                    index === 0 ? 'bg-blue-500' : index === 1 ? 'bg-green-500' : 'bg-purple-500'
                  }`}>
                    {level.depth}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      Level {level.depth} {level.depth === 0 ? '(Root)' : '(Sub)'}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {level.categoriesAtDepth} categories
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {level.averageChildrenPerParent.toFixed(1)}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Avg children
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Category Insights
          </h3>
          <div className="space-y-4">
            <div className="border-l-4 border-blue-500 pl-4">
              <h4 className="font-medium text-gray-900 dark:text-white">Structure Health</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {overview.rootCategories} root categories with {overview.subcategories} subcategories provide good organization depth.
              </p>
            </div>
            
            <div className="border-l-4 border-green-500 pl-4">
              <h4 className="font-medium text-gray-900 dark:text-white">Provider Distribution</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Average of {overview.averageProvidersPerCategory} providers per category indicates balanced distribution.
              </p>
            </div>
            
            <div className="border-l-4 border-purple-500 pl-4">
              <h4 className="font-medium text-gray-900 dark:text-white">Growth Trend</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {overview.categoryGrowthRate}% growth rate shows healthy category expansion.
              </p>
            </div>
            
            <div className="border-l-4 border-yellow-500 pl-4">
              <h4 className="font-medium text-gray-900 dark:text-white">Optimization</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Consider promoting "{overview.leastUsedCategory}" category to increase provider adoption.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Distribution Tab Component
function DistributionTab({ distribution }: { distribution: CategoryAnalytics['distribution'] }) {
  const sortedDistribution = [...distribution].sort((a, b) => b.providerCount - a.providerCount);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Provider Distribution Chart */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Provider Distribution by Category
          </h3>
          <div className="space-y-3">
            {sortedDistribution.slice(0, 8).map((category, index) => (
              <div key={category.categoryId} className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <div className="flex items-center space-x-2">
                    {category.icon && (
                      <span className="text-lg">{category.icon}</span>
                    )}
                    <span className="font-medium text-gray-900 dark:text-white truncate">
                      {category.categoryName}
                    </span>
                    {!category.isActive && (
                      <span className="px-1.5 py-0.5 text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded">
                        Inactive
                      </span>
                    )}
                  </div>
                  <span className="text-gray-600 dark:text-gray-400">
                    {category.providerCount} ({category.percentage}%)
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                  <div
                    className="h-2 rounded-full"
                    style={{
                      width: `${category.percentage}%`,
                      backgroundColor: category.color || '#3B82F6'
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Category Statistics */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Category Statistics
          </h3>
          <div className="space-y-4">
            {sortedDistribution.slice(0, 6).map((category) => (
              <div key={category.categoryId} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
                    style={{ backgroundColor: category.color || '#3B82F6' }}
                  >
                    {category.icon || '📁'}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {category.categoryName}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {category.subcategoryCount} subcategories
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-white">
                    {category.providerCount}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    providers
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Usage Tab Component
function UsageTab({ usage }: { usage: CategoryAnalytics['usage'] }) {
  const sortedUsage = [...usage].sort((a, b) => b.totalBookings - a.totalBookings);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Categories */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Top Performing Categories
          </h3>
          <div className="space-y-4">
            {sortedUsage.slice(0, 5).map((category, index) => (
              <div key={category.categoryId} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-400' :
                    index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                  }`}>
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {category.categoryName}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {category.activeProviders} active providers
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-white">
                    {category.totalBookings.toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    bookings
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Revenue Performance */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Revenue Performance
          </h3>
          <div className="space-y-4">
            {sortedUsage
              .sort((a, b) => b.revenueGenerated - a.revenueGenerated)
              .slice(0, 5)
              .map((category) => (
                <div key={category.categoryId} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {category.categoryName}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-yellow-500">⭐</span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {category.averageRating.toFixed(1)} rating
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600 dark:text-green-400">
                      ${category.revenueGenerated.toLocaleString()}
                    </p>
                    <div className="flex items-center justify-end mt-1">
                      {category.growthTrend === 'up' && (
                        <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                      )}
                      {category.growthTrend === 'down' && (
                        <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                        </svg>
                      )}
                      {category.growthTrend === 'stable' && (
                        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14" />
                        </svg>
                      )}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Performance Tab Component
function PerformanceTab({ performance }: { performance: CategoryAnalytics['performance'] }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Conversion Rates */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Conversion Rates
          </h3>
          <div className="space-y-4">
            {performance
              .sort((a, b) => b.conversionRate - a.conversionRate)
              .slice(0, 6)
              .map((category) => (
                <div key={category.categoryId} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {category.categoryName}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Avg booking: ${category.averageBookingValue}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-blue-600 dark:text-blue-400">
                      {(category.conversionRate * 100).toFixed(1)}%
                    </p>
                    <div className="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1">
                      <div
                        className="h-2 bg-blue-500 rounded-full"
                        style={{ width: `${category.conversionRate * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Customer Satisfaction */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Customer Satisfaction
          </h3>
          <div className="space-y-4">
            {performance
              .sort((a, b) => b.customerSatisfaction - a.customerSatisfaction)
              .slice(0, 6)
              .map((category) => (
                <div key={category.categoryId} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {category.categoryName}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {(category.repeatCustomerRate * 100).toFixed(0)}% repeat customers
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-1">
                      <span className="text-yellow-500">⭐</span>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {category.customerSatisfaction.toFixed(1)}
                      </span>
                    </div>
                    <div className="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1">
                      <div
                        className="h-2 bg-yellow-500 rounded-full"
                        style={{ width: `${(category.customerSatisfaction / 5) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* Performance Metrics Table */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Detailed Performance Metrics
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-600">
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Category</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900 dark:text-white">Conversion</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900 dark:text-white">Satisfaction</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900 dark:text-white">Avg Booking</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900 dark:text-white">Repeat Rate</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
              {performance.map((category) => (
                <tr key={category.categoryId} className="hover:bg-gray-100 dark:hover:bg-gray-600">
                  <td className="py-3 px-4 font-medium text-gray-900 dark:text-white">
                    {category.categoryName}
                  </td>
                  <td className="py-3 px-4 text-center">
                    <span className="font-medium text-blue-600 dark:text-blue-400">
                      {(category.conversionRate * 100).toFixed(1)}%
                    </span>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center space-x-1">
                      <span className="text-yellow-500">⭐</span>
                      <span className="text-gray-900 dark:text-white">
                        {category.customerSatisfaction.toFixed(1)}
                      </span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center text-green-600 dark:text-green-400 font-medium">
                    ${category.averageBookingValue}
                  </td>
                  <td className="py-3 px-4 text-center text-purple-600 dark:text-purple-400 font-medium">
                    {(category.repeatCustomerRate * 100).toFixed(0)}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

// Trends Tab Component
function TrendsTab({ trends }: { trends: CategoryAnalytics['trends'] }) {
  const totalNewCategories = trends.reduce((sum, day) => sum + day.newCategories, 0);
  const totalActivations = trends.reduce((sum, day) => sum + day.activatedCategories, 0);
  const totalDeactivations = trends.reduce((sum, day) => sum + day.deactivatedCategories, 0);
  const totalMigrations = trends.reduce((sum, day) => sum + day.providerMigrations, 0);

  return (
    <div className="space-y-6">
      {/* Trend Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 text-center">
          <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{totalNewCategories}</p>
          <p className="text-sm text-blue-700 dark:text-blue-300">New Categories</p>
        </div>
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 text-center">
          <p className="text-3xl font-bold text-green-600 dark:text-green-400">{totalActivations}</p>
          <p className="text-sm text-green-700 dark:text-green-300">Activations</p>
        </div>
        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-6 text-center">
          <p className="text-3xl font-bold text-red-600 dark:text-red-400">{totalDeactivations}</p>
          <p className="text-sm text-red-700 dark:text-red-300">Deactivations</p>
        </div>
        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6 text-center">
          <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">{totalMigrations}</p>
          <p className="text-sm text-purple-700 dark:text-purple-300">Provider Migrations</p>
        </div>
      </div>

      {/* Trend Analysis */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Category Activity Trends
        </h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Key Insights</h4>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  {totalNewCategories > 0 ? `${totalNewCategories} new categories added` : 'No new categories added'} in the selected period
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  {totalActivations > totalDeactivations ? 'Net positive' : 'Net negative'} category activation trend
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">•</span>
                  {totalMigrations} provider migrations between categories
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-500 mr-2">•</span>
                  {((totalActivations / (totalActivations + totalDeactivations)) * 100).toFixed(0)}% activation success rate
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Recommendations</h4>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  {totalNewCategories === 0 ? 'Consider adding new categories to expand service offerings' : 'Monitor new category performance'}
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">→</span>
                  {totalDeactivations > 0 ? 'Review deactivated categories for potential reactivation' : 'Maintain current category health'}
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">→</span>
                  {totalMigrations > 10 ? 'High migration suggests category restructuring needs' : 'Category structure appears stable'}
                </li>
                <li className="flex items-start">
                  <span className="text-orange-500 mr-2">→</span>
                  Focus on categories with consistent growth patterns
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Daily Trends */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Daily Activity Breakdown
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-600">
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Date</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900 dark:text-white">New</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900 dark:text-white">Activated</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900 dark:text-white">Deactivated</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900 dark:text-white">Migrations</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900 dark:text-white">Net Change</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
              {trends.slice(-10).map((day) => {
                const netChange = day.newCategories + day.activatedCategories - day.deactivatedCategories;
                return (
                  <tr key={day.date} className="hover:bg-gray-100 dark:hover:bg-gray-600">
                    <td className="py-3 px-4 text-gray-900 dark:text-white">
                      {new Date(day.date).toLocaleDateString()}
                    </td>
                    <td className="py-3 px-4 text-center text-blue-600 dark:text-blue-400 font-medium">
                      {day.newCategories}
                    </td>
                    <td className="py-3 px-4 text-center text-green-600 dark:text-green-400 font-medium">
                      {day.activatedCategories}
                    </td>
                    <td className="py-3 px-4 text-center text-red-600 dark:text-red-400 font-medium">
                      {day.deactivatedCategories}
                    </td>
                    <td className="py-3 px-4 text-center text-purple-600 dark:text-purple-400 font-medium">
                      {day.providerMigrations}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className={`font-medium ${
                        netChange > 0 ? 'text-green-600 dark:text-green-400' :
                        netChange < 0 ? 'text-red-600 dark:text-red-400' :
                        'text-gray-600 dark:text-gray-400'
                      }`}>
                        {netChange > 0 ? '+' : ''}{netChange}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

import React, { useState, useRef } from 'react';
import { ProviderCategory } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import toast from 'react-hot-toast';

interface CategoryReorderListProps {
  categories: ProviderCategory[];
  onReorder: (categories: ProviderCategory[]) => void;
  className?: string;
}

interface DragState {
  draggedIndex: number | null;
  dragOverIndex: number | null;
}

export default function CategoryReorderList({
  categories,
  onReorder,
  className = '',
}: CategoryReorderListProps) {
  const [dragState, setDragState] = useState<DragState>({
    draggedIndex: null,
    dragOverIndex: null,
  });
  const [localCategories, setLocalCategories] = useState(categories);
  const dragCounter = useRef(0);
  const loading = useLoading();

  React.useEffect(() => {
    setLocalCategories(categories);
  }, [categories]);

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDragState({ draggedIndex: index, dragOverIndex: null });
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', '');
    
    // Add visual feedback
    const target = e.target as HTMLElement;
    target.style.opacity = '0.5';
  };

  const handleDragEnd = (e: React.DragEvent) => {
    const target = e.target as HTMLElement;
    target.style.opacity = '1';
    setDragState({ draggedIndex: null, dragOverIndex: null });
    dragCounter.current = 0;
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragEnter = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    dragCounter.current++;
    setDragState(prev => ({ ...prev, dragOverIndex: index }));
  };

  const handleDragLeave = (e: React.DragEvent) => {
    dragCounter.current--;
    if (dragCounter.current === 0) {
      setDragState(prev => ({ ...prev, dragOverIndex: null }));
    }
  };

  const handleDrop = async (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    const { draggedIndex } = dragState;
    
    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDragState({ draggedIndex: null, dragOverIndex: null });
      return;
    }

    const newCategories = [...localCategories];
    const draggedCategory = newCategories[draggedIndex];
    
    // Remove dragged item
    newCategories.splice(draggedIndex, 1);
    
    // Insert at new position
    const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
    newCategories.splice(insertIndex, 0, draggedCategory);
    
    // Update sort orders
    const updatedCategories = newCategories.map((category, index) => ({
      ...category,
      sortOrder: index,
    }));

    setLocalCategories(updatedCategories);
    setDragState({ draggedIndex: null, dragOverIndex: null });

    // Save to backend
    try {
      loading.startLoading({ message: 'Updating category order...' });
      
      const response = await adminApi.categories.reorderCategories(
        updatedCategories.map(cat => ({ id: cat.id, sortOrder: cat.sortOrder }))
      );

      if (response.success) {
        onReorder(updatedCategories);
        toast.success('Category order updated successfully');
      } else {
        throw new Error(response.message || 'Failed to update category order');
      }
    } catch (error) {
      handleError(error, { action: 'reorder_categories' });
      // Revert on error
      setLocalCategories(categories);
    } finally {
      loading.stopLoading();
    }
  };

  const moveCategory = (fromIndex: number, direction: 'up' | 'down') => {
    const toIndex = direction === 'up' ? fromIndex - 1 : fromIndex + 1;
    
    if (toIndex < 0 || toIndex >= localCategories.length) return;
    
    const newCategories = [...localCategories];
    const temp = newCategories[fromIndex];
    newCategories[fromIndex] = newCategories[toIndex];
    newCategories[toIndex] = temp;
    
    // Update sort orders
    const updatedCategories = newCategories.map((category, index) => ({
      ...category,
      sortOrder: index,
    }));

    setLocalCategories(updatedCategories);
    onReorder(updatedCategories);
  };

  const getCategoryIcon = (category: ProviderCategory) => {
    if (category.metadata?.icon) {
      return category.metadata.icon;
    }
    return '📁';
  };

  const getCategoryColor = (category: ProviderCategory) => {
    return category.metadata?.color || '#3B82F6';
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Reorder Categories
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Drag and drop categories to change their order, or use the arrow buttons
        </p>
      </div>

      <div className="p-6">
        {localCategories.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">No categories to reorder</p>
          </div>
        ) : (
          <div className="space-y-2">
            {localCategories.map((category, index) => (
              <div
                key={category.id}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDragEnter={(e) => handleDragEnter(e, index)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, index)}
                className={`
                  flex items-center justify-between p-4 border rounded-lg cursor-move transition-all duration-200
                  ${dragState.draggedIndex === index 
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }
                  ${dragState.dragOverIndex === index && dragState.draggedIndex !== index
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                    : ''
                  }
                `}
              >
                <div className="flex items-center space-x-4">
                  {/* Drag Handle */}
                  <div className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
                    </svg>
                  </div>

                  {/* Category Icon */}
                  <div 
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                    style={{ backgroundColor: getCategoryColor(category) }}
                  >
                    {getCategoryIcon(category)}
                  </div>

                  {/* Category Info */}
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {category.name}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Sort Order: {category.sortOrder || index}
                      {category.parentId && ' • Subcategory'}
                      {!category.isActive && ' • Inactive'}
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => moveCategory(index, 'up')}
                    disabled={index === 0 || loading.isLoading}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Move up"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                    </svg>
                  </button>
                  
                  <button
                    type="button"
                    onClick={() => moveCategory(index, 'down')}
                    disabled={index === localCategories.length - 1 || loading.isLoading}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Move down"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {/* Status Indicator */}
                  <div className={`w-2 h-2 rounded-full ${
                    category.isActive ? 'bg-green-500' : 'bg-red-500'
                  }`} title={category.isActive ? 'Active' : 'Inactive'} />
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            How to reorder:
          </h4>
          <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li>• Drag and drop categories to reorder them</li>
            <li>• Use the arrow buttons for precise movement</li>
            <li>• Changes are saved automatically</li>
            <li>• Lower sort order numbers appear first</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { ProviderCategory } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import CategorySafetyChecker from './CategorySafetyChecker';
import toast from 'react-hot-toast';

interface CategoryDeletionDialogProps {
  category: ProviderCategory;
  categories: ProviderCategory[];
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (category: ProviderCategory) => void;
  onCancel: () => void;
}

interface SafetyCheck {
  id: string;
  type: 'deletion' | 'circular' | 'orphan' | 'dependency' | 'integrity';
  severity: 'critical' | 'warning' | 'info';
  categoryId: string;
  categoryName: string;
  message: string;
  details: string;
  impact: string[];
  canProceed: boolean;
  autoFixable: boolean;
  fixAction?: string;
}

interface DeletionOptions {
  reassignProviders: boolean;
  targetCategoryId?: string;
  handleChildCategories: 'delete' | 'orphan' | 'reassign';
  childTargetCategoryId?: string;
  archiveInstead: boolean;
  confirmUnderstanding: boolean;
}

export default function CategoryDeletionDialog({
  category,
  categories,
  isOpen,
  onClose,
  onConfirm,
  onCancel,
}: CategoryDeletionDialogProps) {
  const [safetyChecks, setSafetyChecks] = useState<SafetyCheck[]>([]);
  const [canProceed, setCanProceed] = useState(false);
  const [showSafetyDetails, setShowSafetyDetails] = useState(false);
  const [deletionOptions, setDeletionOptions] = useState<DeletionOptions>({
    reassignProviders: true,
    handleChildCategories: 'reassign',
    archiveInstead: false,
    confirmUnderstanding: false,
  });
  const [step, setStep] = useState<'safety' | 'options' | 'confirm'>('safety');
  const loading = useLoading();

  const availableCategories = categories.filter(cat => 
    cat.id !== category.id && 
    cat.isActive && 
    !isDescendantOf(cat, category.id, categories)
  );

  useEffect(() => {
    if (isOpen) {
      setStep('safety');
      setDeletionOptions({
        reassignProviders: true,
        handleChildCategories: 'reassign',
        archiveInstead: false,
        confirmUnderstanding: false,
      });
    }
  }, [isOpen, category]);

  const isDescendantOf = (cat: ProviderCategory, ancestorId: string, allCategories: ProviderCategory[]): boolean => {
    if (cat.parentId === ancestorId) return true;
    if (!cat.parentId) return false;
    
    const parent = allCategories.find(c => c.id === cat.parentId);
    return parent ? isDescendantOf(parent, ancestorId, allCategories) : false;
  };

  const handleSafetyResult = (checks: SafetyCheck[], canProceedSafely: boolean) => {
    setSafetyChecks(checks);
    setCanProceed(canProceedSafely);
  };

  const handleAutoFix = async (check: SafetyCheck) => {
    try {
      switch (check.type) {
        case 'circular':
          // Remove parent assignment to break circular reference
          await adminApi.categories.updateCategory(check.categoryId, { parentId: null });
          break;
        case 'orphan':
          // Remove invalid parent reference
          await adminApi.categories.updateCategory(check.categoryId, { parentId: null });
          break;
        case 'dependency':
          if (check.id.includes('empty-active')) {
            // Deactivate empty category
            await adminApi.categories.updateCategory(check.categoryId, { isActive: false });
          } else if (check.id.includes('inactive-with-providers')) {
            // Activate category with providers
            await adminApi.categories.updateCategory(check.categoryId, { isActive: true });
          }
          break;
      }
      toast.success('Issue fixed automatically');
    } catch (error) {
      handleError(error, { action: 'auto_fix_category_issue' });
      throw error;
    }
  };

  const handleProceedToOptions = () => {
    if (canProceed || safetyChecks.every(check => check.severity !== 'critical')) {
      setStep('options');
    }
  };

  const handleProceedToConfirm = () => {
    if (validateOptions()) {
      setStep('confirm');
    }
  };

  const validateOptions = (): boolean => {
    if (deletionOptions.reassignProviders && !deletionOptions.targetCategoryId) {
      toast.error('Please select a target category for provider reassignment');
      return false;
    }

    if (deletionOptions.handleChildCategories === 'reassign' && !deletionOptions.childTargetCategoryId) {
      toast.error('Please select a target category for child categories');
      return false;
    }

    if (!deletionOptions.confirmUnderstanding) {
      toast.error('Please confirm that you understand the implications');
      return false;
    }

    return true;
  };

  const handleConfirmDeletion = async () => {
    try {
      loading.startLoading({ message: 'Processing category deletion...' });

      if (deletionOptions.archiveInstead) {
        // Archive instead of delete
        await adminApi.categories.updateCategory(category.id, { 
          isActive: false,
          title: `[ARCHIVED] ${category.title}`,
        });
        toast.success('Category archived successfully');
      } else {
        // Perform actual deletion with options
        await adminApi.categories.deleteCategory(category.id, {
          reassignProviders: deletionOptions.reassignProviders,
          targetCategoryId: deletionOptions.targetCategoryId,
          handleChildCategories: deletionOptions.handleChildCategories,
          childTargetCategoryId: deletionOptions.childTargetCategoryId,
        });
        toast.success('Category deleted successfully');
      }

      onConfirm(category);
      onClose();
    } catch (error) {
      handleError(error, { action: 'delete_category' });
    } finally {
      loading.stopLoading();
    }
  };

  const childCategories = categories.filter(cat => cat.parentId === category.id);
  const criticalIssues = safetyChecks.filter(check => check.severity === 'critical');

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Delete Category: {category.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {step === 'safety' && 'Analyzing safety implications'}
                {step === 'options' && 'Configure deletion options'}
                {step === 'confirm' && 'Confirm deletion'}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center mt-4 space-x-4">
            <div className={`flex items-center ${step === 'safety' ? 'text-blue-600' : 'text-green-600'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                step === 'safety' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'
              }`}>
                {step === 'safety' ? '1' : '✓'}
              </div>
              <span className="ml-2 text-sm font-medium">Safety Check</span>
            </div>
            <div className="flex-1 h-px bg-gray-300 dark:bg-gray-600"></div>
            <div className={`flex items-center ${
              step === 'options' ? 'text-blue-600' : 
              step === 'confirm' ? 'text-green-600' : 'text-gray-400'
            }`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                step === 'options' ? 'bg-blue-100 text-blue-600' : 
                step === 'confirm' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
              }`}>
                {step === 'confirm' ? '✓' : '2'}
              </div>
              <span className="ml-2 text-sm font-medium">Options</span>
            </div>
            <div className="flex-1 h-px bg-gray-300 dark:bg-gray-600"></div>
            <div className={`flex items-center ${step === 'confirm' ? 'text-blue-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                step === 'confirm' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-400'
              }`}>
                3
              </div>
              <span className="ml-2 text-sm font-medium">Confirm</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'safety' && (
            <div className="space-y-6">
              <CategorySafetyChecker
                categories={categories}
                targetCategory={category}
                operation="delete"
                onSafetyResult={handleSafetyResult}
                onAutoFix={handleAutoFix}
              />

              {safetyChecks.length > 0 && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                  <div className="flex items-start">
                    <svg className="w-5 h-5 text-yellow-500 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <div>
                      <h4 className="font-medium text-yellow-900 dark:text-yellow-100">
                        Safety Analysis Complete
                      </h4>
                      <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                        {criticalIssues.length > 0 
                          ? `${criticalIssues.length} critical issues must be resolved before deletion can proceed.`
                          : 'Safety checks passed. You can proceed with deletion configuration.'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {step === 'options' && (
            <div className="space-y-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  Deletion Configuration
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Configure how to handle the category's data and relationships before deletion.
                </p>
              </div>

              {/* Archive Option */}
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <label className="flex items-start">
                  <input
                    type="checkbox"
                    checked={deletionOptions.archiveInstead}
                    onChange={(e) => setDeletionOptions(prev => ({ ...prev, archiveInstead: e.target.checked }))}
                    className="mt-1 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  />
                  <div className="ml-3">
                    <span className="font-medium text-gray-900 dark:text-white">
                      Archive instead of delete
                    </span>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Recommended: Archive the category instead of permanently deleting it. This preserves historical data while making the category inactive.
                    </p>
                  </div>
                </label>
              </div>

              {!deletionOptions.archiveInstead && (
                <>
                  {/* Provider Reassignment */}
                  {category.providerCount && category.providerCount > 0 && (
                    <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <label className="flex items-start mb-3">
                        <input
                          type="checkbox"
                          checked={deletionOptions.reassignProviders}
                          onChange={(e) => setDeletionOptions(prev => ({ ...prev, reassignProviders: e.target.checked }))}
                          className="mt-1 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <div className="ml-3">
                          <span className="font-medium text-gray-900 dark:text-white">
                            Reassign {category.providerCount} providers
                          </span>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            Move all providers from this category to another category before deletion.
                          </p>
                        </div>
                      </label>

                      {deletionOptions.reassignProviders && (
                        <select
                          value={deletionOptions.targetCategoryId || ''}
                          onChange={(e) => setDeletionOptions(prev => ({ ...prev, targetCategoryId: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value="">Select target category</option>
                          {availableCategories.map((cat) => (
                            <option key={cat.id} value={cat.id}>
                              {cat.name}
                            </option>
                          ))}
                        </select>
                      )}
                    </div>
                  )}

                  {/* Child Categories */}
                  {childCategories.length > 0 && (
                    <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                        Handle {childCategories.length} child categories
                      </h4>
                      <div className="space-y-3">
                        <label className="flex items-start">
                          <input
                            type="radio"
                            name="childHandling"
                            value="reassign"
                            checked={deletionOptions.handleChildCategories === 'reassign'}
                            onChange={(e) => setDeletionOptions(prev => ({ ...prev, handleChildCategories: e.target.value as any }))}
                            className="mt-1 border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                          />
                          <div className="ml-3">
                            <span className="font-medium text-gray-900 dark:text-white">
                              Reassign to another category
                            </span>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Move child categories to become children of another category.
                            </p>
                          </div>
                        </label>

                        <label className="flex items-start">
                          <input
                            type="radio"
                            name="childHandling"
                            value="orphan"
                            checked={deletionOptions.handleChildCategories === 'orphan'}
                            onChange={(e) => setDeletionOptions(prev => ({ ...prev, handleChildCategories: e.target.value as any }))}
                            className="mt-1 border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                          />
                          <div className="ml-3">
                            <span className="font-medium text-gray-900 dark:text-white">
                              Make root categories
                            </span>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Convert child categories to root categories (remove parent relationship).
                            </p>
                          </div>
                        </label>

                        <label className="flex items-start">
                          <input
                            type="radio"
                            name="childHandling"
                            value="delete"
                            checked={deletionOptions.handleChildCategories === 'delete'}
                            onChange={(e) => setDeletionOptions(prev => ({ ...prev, handleChildCategories: e.target.value as any }))}
                            className="mt-1 border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                          />
                          <div className="ml-3">
                            <span className="font-medium text-red-600 dark:text-red-400">
                              Delete child categories too
                            </span>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Permanently delete all child categories along with this category.
                            </p>
                          </div>
                        </label>

                        {deletionOptions.handleChildCategories === 'reassign' && (
                          <select
                            value={deletionOptions.childTargetCategoryId || ''}
                            onChange={(e) => setDeletionOptions(prev => ({ ...prev, childTargetCategoryId: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          >
                            <option value="">Select target parent category</option>
                            {availableCategories.map((cat) => (
                              <option key={cat.id} value={cat.id}>
                                {cat.name}
                              </option>
                            ))}
                          </select>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Confirmation */}
                  <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <label className="flex items-start">
                      <input
                        type="checkbox"
                        checked={deletionOptions.confirmUnderstanding}
                        onChange={(e) => setDeletionOptions(prev => ({ ...prev, confirmUnderstanding: e.target.checked }))}
                        className="mt-1 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <div className="ml-3">
                        <span className="font-medium text-gray-900 dark:text-white">
                          I understand the implications
                        </span>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          I understand that this action cannot be undone and will affect the platform's category structure.
                        </p>
                      </div>
                    </label>
                  </div>
                </>
              )}
            </div>
          )}

          {step === 'confirm' && (
            <div className="space-y-6">
              <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-red-500 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <h4 className="font-medium text-red-900 dark:text-red-100">
                      Final Confirmation Required
                    </h4>
                    <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                      {deletionOptions.archiveInstead 
                        ? `You are about to archive the category "${category.title}". This will make it inactive but preserve all data.`
                        : `You are about to permanently delete the category "${category.name}". This action cannot be undone.`
                      }
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                  Summary of Actions
                </h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  {deletionOptions.archiveInstead ? (
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      Category will be archived and marked as inactive
                    </li>
                  ) : (
                    <>
                      <li className="flex items-start">
                        <span className="text-red-500 mr-2">•</span>
                        Category "{category.title}" will be permanently deleted
                      </li>
                      {deletionOptions.reassignProviders && (
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2">•</span>
                          {category.providerCount} providers will be moved to "{availableCategories.find(c => c.id === deletionOptions.targetCategoryId)?.name}"
                        </li>
                      )}
                      {childCategories.length > 0 && (
                        <li className="flex items-start">
                          <span className="text-purple-500 mr-2">•</span>
                          {childCategories.length} child categories will be {
                            deletionOptions.handleChildCategories === 'delete' ? 'deleted' :
                            deletionOptions.handleChildCategories === 'orphan' ? 'converted to root categories' :
                            `moved under "${availableCategories.find(c => c.id === deletionOptions.childTargetCategoryId)?.name}"`
                          }
                        </li>
                      )}
                    </>
                  )}
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Cancel
          </button>

          <div className="flex items-center space-x-3">
            {step === 'options' && (
              <button
                onClick={() => setStep('safety')}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Back
              </button>
            )}
            {step === 'confirm' && (
              <button
                onClick={() => setStep('options')}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Back
              </button>
            )}

            {step === 'safety' && (
              <button
                onClick={handleProceedToOptions}
                disabled={criticalIssues.length > 0}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Continue
              </button>
            )}
            {step === 'options' && (
              <button
                onClick={handleProceedToConfirm}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700"
              >
                Review
              </button>
            )}
            {step === 'confirm' && (
              <button
                onClick={handleConfirmDeletion}
                disabled={loading.isLoading}
                className={`px-4 py-2 text-sm font-medium text-white border border-transparent rounded-lg ${
                  deletionOptions.archiveInstead 
                    ? 'bg-blue-600 hover:bg-blue-700' 
                    : 'bg-red-600 hover:bg-red-700'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {loading.isLoading 
                  ? 'Processing...' 
                  : deletionOptions.archiveInstead 
                    ? 'Archive Category' 
                    : 'Delete Category'
                }
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

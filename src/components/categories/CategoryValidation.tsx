import React, { useState, useEffect } from 'react';
import { ProviderCategory } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';

interface CategoryValidationProps {
  categories: ProviderCategory[];
  onValidationComplete: (issues: ValidationIssue[]) => void;
  className?: string;
}

interface ValidationIssue {
  id: string;
  categoryId: string;
  categoryName: string;
  type: 'error' | 'warning' | 'info';
  severity: 'high' | 'medium' | 'low';
  message: string;
  suggestion?: string;
  autoFixable?: boolean;
}

interface ValidationRules {
  checkDuplicateNames: boolean;
  checkEmptyDescriptions: boolean;
  checkCircularReferences: boolean;
  checkInactiveParents: boolean;
  checkSortOrderGaps: boolean;
  checkSEOOptimization: boolean;
  checkKeywordDuplication: boolean;
}

export default function CategoryValidation({
  categories,
  onValidationComplete,
  className = '',
}: CategoryValidationProps) {
  const [validationIssues, setValidationIssues] = useState<ValidationIssue[]>([]);
  const [validationRules, setValidationRules] = useState<ValidationRules>({
    checkDuplicateNames: true,
    checkEmptyDescriptions: true,
    checkCircularReferences: true,
    checkInactiveParents: true,
    checkSortOrderGaps: true,
    checkSEOOptimization: true,
    checkKeywordDuplication: true,
  });
  const [isValidating, setIsValidating] = useState(false);
  const loading = useLoading();

  useEffect(() => {
    if (categories.length > 0) {
      validateCategories();
    }
  }, [categories, validationRules]);

  const validateCategories = async () => {
    setIsValidating(true);
    const issues: ValidationIssue[] = [];

    try {
      // Check for duplicate names
      if (validationRules.checkDuplicateNames) {
        const nameMap = new Map<string, ProviderCategory[]>();
        categories.forEach(cat => {
          const name = cat.title.toLowerCase().trim();
          if (!nameMap.has(name)) {
            nameMap.set(name, []);
          }
          nameMap.get(name)!.push(cat);
        });

        nameMap.forEach((cats, name) => {
          if (cats.length > 1) {
            cats.forEach(cat => {
              issues.push({
                id: `duplicate-name-${cat.id}`,
                categoryId: cat.id,
                categoryName: cat.title,
                type: 'error',
                severity: 'high',
                message: `Duplicate category title: "${cat.title}"`,
                suggestion: 'Rename this category to make it unique',
                autoFixable: false,
              });
            });
          }
        });
      }

      // Check for empty descriptions
      if (validationRules.checkEmptyDescriptions) {
        categories.forEach(cat => {
          if (!cat.description || cat.description.trim().length === 0) {
            issues.push({
              id: `empty-description-${cat.id}`,
              categoryId: cat.id,
              categoryName: cat.title,
              type: 'warning',
              severity: 'medium',
              message: 'Category has no description',
              suggestion: 'Add a description to help users understand this category',
              autoFixable: false,
            });
          } else if (cat.description.trim().length < 10) {
            issues.push({
              id: `short-description-${cat.id}`,
              categoryId: cat.id,
              categoryName: cat.title,
              type: 'warning',
              severity: 'low',
              message: 'Category description is very short',
              suggestion: 'Consider adding more detail to the description',
              autoFixable: false,
            });
          }
        });
      }

      // Check for circular references
      if (validationRules.checkCircularReferences) {
        categories.forEach(cat => {
          if (cat.parentId && hasCircularReference(cat, categories)) {
            issues.push({
              id: `circular-reference-${cat.id}`,
              categoryId: cat.id,
              categoryName: cat.name,
              type: 'error',
              severity: 'high',
              message: 'Circular reference detected in parent-child relationship',
              suggestion: 'Remove the parent assignment or restructure the hierarchy',
              autoFixable: true,
            });
          }
        });
      }

      // Check for inactive parents
      if (validationRules.checkInactiveParents) {
        categories.forEach(cat => {
          if (cat.parentId && cat.isActive) {
            const parent = categories.find(p => p.id === cat.parentId);
            if (parent && !parent.isActive) {
              issues.push({
                id: `inactive-parent-${cat.id}`,
                categoryId: cat.id,
                categoryName: cat.name,
                type: 'warning',
                severity: 'medium',
                message: 'Active category has inactive parent',
                suggestion: 'Either activate the parent category or move this to an active parent',
                autoFixable: false,
              });
            }
          }
        });
      }

      // Check for sort order gaps
      if (validationRules.checkSortOrderGaps) {
        const sortOrders = categories
          .map(cat => cat.sortOrder || 0)
          .sort((a, b) => a - b);
        
        for (let i = 1; i < sortOrders.length; i++) {
          if (sortOrders[i] - sortOrders[i - 1] > 10) {
            const category = categories.find(cat => (cat.sortOrder || 0) === sortOrders[i]);
            if (category) {
              issues.push({
                id: `sort-order-gap-${category.id}`,
                categoryId: category.id,
                categoryName: category.name,
                type: 'info',
                severity: 'low',
                message: 'Large gap in sort order sequence',
                suggestion: 'Consider reorganizing sort orders for better management',
                autoFixable: true,
              });
            }
          }
        }
      }

      // Check SEO optimization
      if (validationRules.checkSEOOptimization) {
        categories.forEach(cat => {
          if (!cat.metadata?.seoTitle) {
            issues.push({
              id: `missing-seo-title-${cat.id}`,
              categoryId: cat.id,
              categoryName: cat.name,
              type: 'info',
              severity: 'low',
              message: 'Missing SEO title',
              suggestion: 'Add an SEO title to improve search engine visibility',
              autoFixable: false,
            });
          }

          if (!cat.metadata?.seoDescription) {
            issues.push({
              id: `missing-seo-description-${cat.id}`,
              categoryId: cat.id,
              categoryName: cat.name,
              type: 'info',
              severity: 'low',
              message: 'Missing SEO description',
              suggestion: 'Add an SEO description to improve search engine visibility',
              autoFixable: false,
            });
          }

          if (!cat.metadata?.keywords || cat.metadata.keywords.length === 0) {
            issues.push({
              id: `missing-keywords-${cat.id}`,
              categoryId: cat.id,
              categoryName: cat.name,
              type: 'info',
              severity: 'low',
              message: 'No keywords defined',
              suggestion: 'Add relevant keywords to improve searchability',
              autoFixable: false,
            });
          }
        });
      }

      // Check for keyword duplication
      if (validationRules.checkKeywordDuplication) {
        const keywordMap = new Map<string, ProviderCategory[]>();
        categories.forEach(cat => {
          if (cat.metadata?.keywords) {
            cat.metadata.keywords.forEach(keyword => {
              const key = keyword.toLowerCase().trim();
              if (!keywordMap.has(key)) {
                keywordMap.set(key, []);
              }
              keywordMap.get(key)!.push(cat);
            });
          }
        });

        keywordMap.forEach((cats, keyword) => {
          if (cats.length > 3) {
            cats.forEach(cat => {
              issues.push({
                id: `keyword-overuse-${cat.id}-${keyword}`,
                categoryId: cat.id,
                categoryName: cat.name,
                type: 'warning',
                severity: 'low',
                message: `Keyword "${keyword}" is overused across categories`,
                suggestion: 'Consider using more specific keywords for better categorization',
                autoFixable: false,
              });
            });
          }
        });
      }

      setValidationIssues(issues);
      onValidationComplete(issues);
    } catch (error) {
      handleError(error, { action: 'validate_categories' });
    } finally {
      setIsValidating(false);
    }
  };

  const hasCircularReference = (category: ProviderCategory, allCategories: ProviderCategory[]): boolean => {
    const visited = new Set<string>();
    let current = category;

    while (current.parentId) {
      if (visited.has(current.id)) {
        return true; // Circular reference found
      }
      visited.add(current.id);
      
      const parent = allCategories.find(cat => cat.id === current.parentId);
      if (!parent) break;
      current = parent;
    }

    return false;
  };

  const autoFixIssue = async (issue: ValidationIssue) => {
    if (!issue.autoFixable) return;

    try {
      loading.startLoading({ message: 'Fixing issue...' });

      switch (issue.type) {
        case 'error':
          if (issue.id.startsWith('circular-reference-')) {
            // Remove parent assignment to break circular reference
            await adminApi.categories.updateCategory(issue.categoryId, { parentId: null });
          }
          break;
        case 'info':
          if (issue.id.startsWith('sort-order-gap-')) {
            // Reorganize sort orders
            const reorderedCategories = categories.map((cat, index) => ({
              id: cat.id,
              sortOrder: index * 10,
            }));
            await adminApi.categories.reorderCategories(reorderedCategories);
          }
          break;
      }

      // Re-validate after fix
      validateCategories();
    } catch (error) {
      handleError(error, { action: 'auto_fix_issue' });
    } finally {
      loading.stopLoading();
    }
  };

  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'error':
        return (
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'high':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            High
          </span>
        );
      case 'medium':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            Medium
          </span>
        );
      case 'low':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            Low
          </span>
        );
      default:
        return null;
    }
  };

  const issuesByType = {
    error: validationIssues.filter(issue => issue.type === 'error'),
    warning: validationIssues.filter(issue => issue.type === 'warning'),
    info: validationIssues.filter(issue => issue.type === 'info'),
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Category Validation
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Validate category structure and identify potential issues
            </p>
          </div>
          
          <button
            onClick={validateCategories}
            disabled={isValidating}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isValidating ? 'Validating...' : 'Re-validate'}
          </button>
        </div>
      </div>

      <div className="p-6">
        {/* Validation Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="w-6 h-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-red-900 dark:text-red-100">Errors</p>
                <p className="text-2xl font-semibold text-red-600 dark:text-red-400">{issuesByType.error.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="w-6 h-6 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-yellow-900 dark:text-yellow-100">Warnings</p>
                <p className="text-2xl font-semibold text-yellow-600 dark:text-yellow-400">{issuesByType.warning.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="w-6 h-6 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Info</p>
                <p className="text-2xl font-semibold text-blue-600 dark:text-blue-400">{issuesByType.info.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Validation Issues */}
        {validationIssues.length === 0 ? (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              All Good!
            </h4>
            <p className="text-gray-600 dark:text-gray-400">
              No validation issues found in your categories.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {validationIssues.map((issue) => (
              <div
                key={issue.id}
                className={`p-4 rounded-lg border-l-4 ${
                  issue.type === 'error' ? 'border-red-500 bg-red-50 dark:bg-red-900/20' :
                  issue.type === 'warning' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20' :
                  'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    {getIssueIcon(issue.type)}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {issue.categoryName}
                        </h4>
                        {getSeverityBadge(issue.severity)}
                      </div>
                      <p className="text-sm text-gray-700 dark:text-gray-300 mb-1">
                        {issue.message}
                      </p>
                      {issue.suggestion && (
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          💡 {issue.suggestion}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {issue.autoFixable && (
                    <button
                      onClick={() => autoFixIssue(issue)}
                      disabled={loading.isLoading}
                      className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Auto Fix
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

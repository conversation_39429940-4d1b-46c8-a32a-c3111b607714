import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ProviderCategory, CreateCategoryRequest, UpdateCategoryRequest } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { LoadingButton } from '../common/LoadingOverlay';
import ImageUpload from '../common/ImageUpload';
import toast from 'react-hot-toast';

// Validation schema
const categorySchema = z.object({
  title: z.string()
    .min(2, 'Category title must be at least 2 characters')
    .max(100, 'Category title must be less than 100 characters')
    .regex(/^[a-zA-Z0-9\s&-]+$/, 'Category title contains invalid characters'),
  description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(500, 'Description must be less than 500 characters'),
  parentId: z.string().optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().min(0, 'Sort order must be positive').max(999, 'Sort order must be less than 1000').optional(),
  metadata: z.object({
    icon: z.string().optional(),
    color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format').optional(),
    keywords: z.array(z.string().min(1).max(50)).max(10, 'Maximum 10 keywords allowed').optional(),
    seoTitle: z.string().max(60, 'SEO title must be less than 60 characters').optional(),
    seoDescription: z.string().max(160, 'SEO description must be less than 160 characters').optional(),
  }).optional(),
});

type CategoryFormData = z.infer<typeof categorySchema>;

interface CategoryFormProps {
  category?: ProviderCategory;
  parentCategories?: ProviderCategory[];
  onSubmit: (category: ProviderCategory) => void;
  onCancel: () => void;
  mode: 'create' | 'edit';
}

export default function CategoryForm({
  category,
  parentCategories = [],
  onSubmit,
  onCancel,
  mode,
}: CategoryFormProps) {
  const [availableParents, setAvailableParents] = useState<ProviderCategory[]>([]);
  const [keywordInput, setKeywordInput] = useState('');
  const [keywords, setKeywords] = useState<string[]>(category?.metadata?.keywords || []);
  const [previewColor, setPreviewColor] = useState(category?.metadata?.color || '#3B82F6');
  // const [selectedParentId, setSelectedParentId] = useState<string | undefined>(category?.parentId?.toString());
  const [imageUploading, setImageUploading] = useState(false);
  const [currentImage, setCurrentImage] = useState<string | undefined>(category?.image?.uploadUrl);
  const loading = useLoading();

  const form = useForm({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      title: category?.title || '',
      description: category?.description || '',
      parentId: category?.parentId ? String(category.parentId) : '',
      isActive: category?.isActive ?? true,
      sortOrder: category?.sortOrder || 0,
      metadata: {
        icon: category?.metadata?.icon || '',
        color: category?.metadata?.color || '#3B82F6',
        keywords: category?.metadata?.keywords || [],
        seoTitle: category?.metadata?.seoTitle || '',
        seoDescription: category?.metadata?.seoDescription || '',
      },
    },
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = form;

  useEffect(() => {
    // Filter out the current category and its descendants from parent options
    const filteredParents = parentCategories.filter(parent => {
      if (mode === 'edit' && category) {
        // Only filter out the current category itself, not its parent
        return String(parent.id) !== String(category.id);
      }
      return true;
    });

    setAvailableParents(filteredParents);
  }, [parentCategories, category, mode]);


  // Reset form values when category changes (important for edit mode)
  useEffect(() => {
    if (category && mode === 'edit') {
      form.reset({
        title: category.title || '',
        description: category.description || '',
        parentId: category.parentId ? String(category.parentId) : '',
        isActive: category.isActive ?? true,
        sortOrder: category.sortOrder || 0,
        metadata: {
          icon: category.metadata?.icon || '',
          color: category.metadata?.color || '#3B82F6',
          keywords: category.metadata?.keywords || [],
          seoTitle: category.metadata?.seoTitle || '',
          seoDescription: category.metadata?.seoDescription || '',
        },
      });

      // Update local state
      setKeywords(category.metadata?.keywords || []);
      setPreviewColor(category.metadata?.color || '#3B82F6');
      setCurrentImage(category.image?.uploadUrl);
    }
  }, [category, mode, form]);

  // Keyword management functions
  const addKeyword = () => {
    if (keywordInput.trim() && !keywords.includes(keywordInput.trim()) && keywords.length < 10) {
      const newKeywords = [...keywords, keywordInput.trim()];
      setKeywords(newKeywords);
      setValue('metadata.keywords', newKeywords);
      setKeywordInput('');
    }
  };

  const removeKeyword = (keyword: string) => {
    const newKeywords = keywords.filter(k => k !== keyword);
    setKeywords(newKeywords);
    setValue('metadata.keywords', newKeywords);
  };

  const handleKeywordKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addKeyword();
    }
  };

  // Color change handler
  const handleColorChange = (color: string) => {
    setPreviewColor(color);
    setValue('metadata.color', color);
  };

  // Image upload handlers
  const handleImageUpload = async (file: File) => {
    if (!category?.id && mode === 'edit') {
      throw new Error('Category ID is required for image upload');
    }

    setImageUploading(true);
    try {
      // For edit mode, upload immediately
      if (mode === 'edit' && category?.id) {
        // Step 1: Request upload URL
        const uploadResponse = await adminApi.categories.uploadCategoryImage(String(category.id), {
          fileName: file.name,
          fileType: file.type,
        });

        if (!uploadResponse.success) {
          throw new Error(uploadResponse.message || 'Failed to get upload URL');
        }

        // Step 2: Upload to S3
        await adminApi.categories.uploadFileToS3(
          uploadResponse.data.uploadUrl,
          uploadResponse.data.uploadFields,
          file
        );

        // Step 3: Update current image
        setCurrentImage(uploadResponse.data.file.uploadUrl);
        toast.success('Image uploaded successfully');
      } else {
        // For create mode, just store the file for later upload
        // We'll handle this after the category is created
        toast('Image will be uploaded after category is created', { icon: 'ℹ️' });
      }
    } catch (error) {
      console.error('Image upload error:', error);
      throw error;
    } finally {
      setImageUploading(false);
    }
  };

  const handleImageRemove = async () => {
    if (!category?.id || mode !== 'edit') {
      setCurrentImage(undefined);
      return;
    }

    setImageUploading(true);
    try {
      const response = await adminApi.categories.removeCategoryImage(String(category.id));
      if (response.success) {
        setCurrentImage(undefined);
        toast.success('Image removed successfully');
      } else {
        throw new Error(response.message || 'Failed to remove image');
      }
    } catch (error) {
      console.error('Image remove error:', error);
      throw error;
    } finally {
      setImageUploading(false);
    }
  };

  const onFormSubmit = async (data: CategoryFormData) => {
    try {
      loading.startLoading({ message: mode === 'create' ? 'Creating category...' : 'Updating category...' });

      let response;
      
      if (mode === 'create') {
        const createData: CreateCategoryRequest = {
          title: data.title,
          description: data.description,
          parentId: data.parentId || null,
          isActive: data.isActive,
          sortOrder: data.sortOrder,
          metadata: data.metadata,
        };
        response = await adminApi.categories.createCategory(createData);
      } else if (category) {
        const updateData: UpdateCategoryRequest = {
          title: data.title,
          description: data.description,
          parentId: data.parentId || null,
          isActive: data.isActive,
          sortOrder: data.sortOrder,
          metadata: data.metadata,
        };
        response = await adminApi.categories.updateCategory(String(category.id), updateData);
      }

      if (response?.success) {
        toast.success(mode === 'create' ? 'Category created successfully' : 'Category updated successfully');
        onSubmit(response.data);
        if (mode === 'create') {
          reset();
        }
      } else {
        throw new Error(response?.message || 'Operation failed');
      }
    } catch (error) {
      handleError(error, { action: `${mode}_category` });
    } finally {
      loading.stopLoading();
    }
  };

  const predefinedIcons = [
    { value: '💄', label: 'Beauty' },
    { value: '🏥', label: 'Healthcare' },
    { value: '💪', label: 'Fitness' },
    { value: '🚗', label: 'Automotive' },
    { value: '🏠', label: 'Home Services' },
    { value: '📚', label: 'Education' },
    { value: '🍽️', label: 'Food & Dining' },
    { value: '💻', label: 'Technology' },
    { value: '🎨', label: 'Arts & Crafts' },
    { value: '🧘', label: 'Wellness' },
    { value: '✂️', label: 'Hair Services' },
    { value: '💅', label: 'Nail Services' },
    { value: '🦷', label: 'Dental' },
    { value: '👁️', label: 'Eye Care' },
    { value: '🏋️', label: 'Gym' },
  ];

  const predefinedColors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
    '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16',
    '#F97316', '#6366F1', '#14B8A6', '#F43F5E',
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          {mode === 'create' ? 'Create New Category' : 'Edit Category'}
        </h3>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
          {mode === 'create' 
            ? 'Add a new service category to organize providers' 
            : 'Update category information and settings'
          }
        </p>
      </div>

      <form onSubmit={handleSubmit(onFormSubmit)} className="p-6 space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Category Name */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category Title *
            </label>
            <input
              {...register('title')}
              type="text"
              id="title"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter category title"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.title.message}</p>
            )}
          </div>

          {/* Parent Category */}
          <div>
            <label htmlFor="parentId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Parent Category
            </label>
            <select
              {...register('parentId')}
              id="parentId"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">No Parent (Root Category)</option>
              {availableParents.map((parent) => (
                <option key={String(parent.id)} value={String(parent.id)}>
                  {parent.title}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description
          </label>
          <textarea
            {...register('description')}
            id="description"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Enter category description"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.description.message}</p>
          )}
        </div>

        {/* Category Image */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Category Image
          </label>
          <ImageUpload
            currentImage={currentImage}
            onImageUpload={handleImageUpload}
            onImageRemove={handleImageRemove}
            loading={imageUploading}
            disabled={loading.isLoading}
            placeholder="Upload a category image to make it more visually appealing"
          />
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {mode === 'create'
              ? 'Image can be uploaded after creating the category'
              : 'Recommended size: 400x300px or larger with 4:3 aspect ratio'
            }
          </p>
        </div>

        {/* Visual Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Icon Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category Icon
            </label>
            <div className="grid grid-cols-5 gap-2">
              {predefinedIcons.map((icon) => (
                <button
                  key={icon.value}
                  type="button"
                  onClick={() => setValue('metadata.icon', icon.value)}
                  className={`
                    p-2 text-lg border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700
                    ${watch('metadata.icon') === icon.value 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900' 
                      : 'border-gray-300 dark:border-gray-600'
                    }
                  `}
                  title={icon.label}
                >
                  {icon.value}
                </button>
              ))}
            </div>
          </div>

          {/* Color Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category Color
            </label>
            <div className="grid grid-cols-6 gap-2">
              {predefinedColors.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setValue('metadata.color', color)}
                  className={`
                    w-8 h-8 rounded-lg border-2
                    ${watch('metadata.color') === color 
                      ? 'border-gray-900 dark:border-white' 
                      : 'border-gray-300 dark:border-gray-600'
                    }
                  `}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Sort Order */}
          <div>
            <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Sort Order
            </label>
            <input
              {...register('sortOrder', { valueAsNumber: true })}
              type="number"
              id="sortOrder"
              min="0"
              max="999"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="0"
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Lower numbers appear first
            </p>
          </div>

          {/* Active Status */}
          <div className="flex items-center">
            <div className="flex items-center h-5">
              <input
                {...register('isActive')}
                id="isActive"
                type="checkbox"
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="isActive" className="font-medium text-gray-700 dark:text-gray-300">
                Active Category
              </label>
              <p className="text-gray-500 dark:text-gray-400">
                Active categories are visible to providers and customers
              </p>
            </div>
          </div>
        </div>

        {/* Keywords */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Keywords
          </label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={keywordInput}
                onChange={(e) => setKeywordInput(e.target.value)}
                onKeyDown={handleKeywordKeyDown}
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Add keyword and press Enter"
                maxLength={50}
              />
              <button
                type="button"
                onClick={addKeyword}
                disabled={!keywordInput.trim() || keywords.length >= 10}
                className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add
              </button>
            </div>
            {keywords.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {keywords.map((keyword, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  >
                    {keyword}
                    <button
                      type="button"
                      onClick={() => removeKeyword(keyword)}
                      className="ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Add up to 10 keywords to help with search and categorization ({keywords.length}/10)
            </p>
          </div>
        </div>

        {/* SEO Settings */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900 dark:text-white">SEO Settings</h4>

          <div>
            <label htmlFor="seoTitle" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              SEO Title
            </label>
            <input
              {...register('metadata.seoTitle')}
              type="text"
              id="seoTitle"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter SEO title (optional)"
              maxLength={60}
            />
            {errors.metadata?.seoTitle && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.metadata.seoTitle.message}</p>
            )}
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {watch('metadata.seoTitle')?.length || 0}/60 characters
            </p>
          </div>

          <div>
            <label htmlFor="seoDescription" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              SEO Description
            </label>
            <textarea
              {...register('metadata.seoDescription')}
              id="seoDescription"
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter SEO description (optional)"
              maxLength={160}
            />
            {errors.metadata?.seoDescription && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.metadata.seoDescription.message}</p>
            )}
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {watch('metadata.seoDescription')?.length || 0}/160 characters
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          
          <LoadingButton
            loading={loading.isLoading}
            loadingText={mode === 'create' ? 'Creating...' : 'Updating...'}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {mode === 'create' ? 'Create Category' : 'Update Category'}
          </LoadingButton>
        </div>
      </form>
    </div>
  );
}

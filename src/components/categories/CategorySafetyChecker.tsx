import React, { useState, useEffect } from 'react';
import { ProviderCategory } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import toast from 'react-hot-toast';

interface SafetyCheck {
  id: string;
  type: 'deletion' | 'circular' | 'orphan' | 'dependency' | 'integrity';
  severity: 'critical' | 'warning' | 'info';
  categoryId: string;
  categoryName: string;
  message: string;
  details: string;
  impact: string[];
  canProceed: boolean;
  autoFixable: boolean;
  fixAction?: string;
}

interface CategorySafetyCheckerProps {
  categories: ProviderCategory[];
  targetCategory?: ProviderCategory;
  operation: 'delete' | 'update' | 'move' | 'validate';
  onSafetyResult: (checks: SafetyCheck[], canProceed: boolean) => void;
  onAutoFix?: (check: SafetyCheck) => Promise<void>;
  className?: string;
}

interface DeletionImpact {
  affectedProviders: number;
  childCategories: ProviderCategory[];
  dependentCategories: ProviderCategory[];
  totalBookings: number;
  activeServices: number;
  revenueImpact: number;
}

export default function CategorySafetyChecker({
  categories,
  targetCategory,
  operation,
  onSafetyResult,
  onAutoFix,
  className = '',
}: CategorySafetyCheckerProps) {
  const [safetyChecks, setSafetyChecks] = useState<SafetyCheck[]>([]);
  const [deletionImpact, setDeletionImpact] = useState<DeletionImpact | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showDetails, setShowDetails] = useState<string | null>(null);
  const loading = useLoading();

  useEffect(() => {
    if (targetCategory || operation === 'validate') {
      performSafetyChecks();
    }
  }, [targetCategory, categories, operation]);

  const performSafetyChecks = async () => {
    setIsAnalyzing(true);
    const checks: SafetyCheck[] = [];

    try {
      // Deletion safety checks
      if (operation === 'delete' && targetCategory) {
        const deletionChecks = await performDeletionChecks(targetCategory);
        checks.push(...deletionChecks);
      }

      // Circular reference checks
      const circularChecks = performCircularReferenceChecks();
      checks.push(...circularChecks);

      // Orphan category checks
      const orphanChecks = performOrphanChecks();
      checks.push(...orphanChecks);

      // Dependency checks
      const dependencyChecks = await performDependencyChecks();
      checks.push(...dependencyChecks);

      // Data integrity checks
      const integrityChecks = performIntegrityChecks();
      checks.push(...integrityChecks);

      setSafetyChecks(checks);
      
      const canProceed = !checks.some(check => 
        check.severity === 'critical' && !check.canProceed
      );
      
      onSafetyResult(checks, canProceed);
    } catch (error) {
      handleError(error, { action: 'perform_safety_checks' });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const performDeletionChecks = async (category: ProviderCategory): Promise<SafetyCheck[]> => {
    const checks: SafetyCheck[] = [];

    try {
      // Get deletion impact data
      const impactResponse = await adminApi.categories.getDeletionImpact(category.id);
      const impact = impactResponse.success ? impactResponse.data : {
        affectedProviders: 0,
        childCategories: categories.filter(c => c.parentId === category.id),
        dependentCategories: [],
        totalBookings: 0,
        activeServices: 0,
        revenueImpact: 0,
      };
      
      setDeletionImpact(impact);

      // Check for child categories
      if (impact.childCategories.length > 0) {
        checks.push({
          id: `child-categories-${category.id}`,
          type: 'deletion',
          severity: 'critical',
          categoryId: category.id,
          categoryName: category.name,
          message: `Category has ${impact.childCategories.length} child categories`,
          details: `Deleting this category will affect: ${impact.childCategories.map(c => c.name).join(', ')}`,
          impact: [
            `${impact.childCategories.length} subcategories will become orphaned`,
            'Subcategory hierarchy will be broken',
            'Provider categorization may be affected'
          ],
          canProceed: false,
          autoFixable: true,
          fixAction: 'Move child categories to parent or make them root categories',
        });
      }

      // Check for assigned providers
      if (impact.affectedProviders > 0) {
        checks.push({
          id: `assigned-providers-${category.id}`,
          type: 'deletion',
          severity: impact.affectedProviders > 10 ? 'critical' : 'warning',
          categoryId: category.id,
          categoryName: category.name,
          message: `Category has ${impact.affectedProviders} assigned providers`,
          details: `Providers will lose their category assignment and may become uncategorized`,
          impact: [
            `${impact.affectedProviders} providers will be uncategorized`,
            'Provider search and filtering may be affected',
            'Customer discovery of services may be impacted'
          ],
          canProceed: impact.affectedProviders <= 5,
          autoFixable: true,
          fixAction: 'Reassign providers to similar categories before deletion',
        });
      }

      // Check for booking history
      if (impact.totalBookings > 0) {
        checks.push({
          id: `booking-history-${category.id}`,
          type: 'deletion',
          severity: 'warning',
          categoryId: category.id,
          categoryName: category.name,
          message: `Category has ${impact.totalBookings} historical bookings`,
          details: `Deletion will affect historical data and analytics`,
          impact: [
            'Historical booking data will reference deleted category',
            'Analytics and reporting may be affected',
            'Data integrity concerns for historical records'
          ],
          canProceed: true,
          autoFixable: false,
          fixAction: 'Consider archiving instead of deleting',
        });
      }

      // Check for revenue impact
      if (impact.revenueImpact > 1000) {
        checks.push({
          id: `revenue-impact-${category.id}`,
          type: 'deletion',
          severity: impact.revenueImpact > 10000 ? 'critical' : 'warning',
          categoryId: category.id,
          categoryName: category.name,
          message: `High revenue impact: $${impact.revenueImpact.toLocaleString()}`,
          details: `This category generates significant revenue for the platform`,
          impact: [
            `Potential revenue loss of $${impact.revenueImpact.toLocaleString()}`,
            'Customer booking patterns may be disrupted',
            'Platform revenue metrics will be affected'
          ],
          canProceed: impact.revenueImpact <= 5000,
          autoFixable: false,
          fixAction: 'Review business impact before proceeding',
        });
      }

    } catch (error) {
      console.error('Error performing deletion checks:', error);
    }

    return checks;
  };

  const performCircularReferenceChecks = (): SafetyCheck[] => {
    const checks: SafetyCheck[] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const detectCircular = (categoryId: string, path: string[]): boolean => {
      if (recursionStack.has(categoryId)) {
        // Circular reference detected
        const circularPath = path.slice(path.indexOf(categoryId));
        const affectedCategories = circularPath.map(id => 
          categories.find(c => c.id === id)?.name || id
        );

        checks.push({
          id: `circular-${categoryId}`,
          type: 'circular',
          severity: 'critical',
          categoryId,
          categoryName: categories.find(c => c.id === categoryId)?.name || 'Unknown',
          message: 'Circular reference detected in category hierarchy',
          details: `Circular path: ${affectedCategories.join(' → ')} → ${affectedCategories[0]}`,
          impact: [
            'Category hierarchy is broken',
            'Infinite loops in category traversal',
            'System instability and performance issues'
          ],
          canProceed: false,
          autoFixable: true,
          fixAction: 'Break circular reference by removing parent assignment',
        });
        return true;
      }

      if (visited.has(categoryId)) {
        return false;
      }

      visited.add(categoryId);
      recursionStack.add(categoryId);

      const category = categories.find(c => c.id === categoryId);
      if (category?.parentId) {
        detectCircular(category.parentId, [...path, categoryId]);
      }

      recursionStack.delete(categoryId);
      return false;
    };

    categories.forEach(category => {
      if (!visited.has(category.id)) {
        detectCircular(category.id, []);
      }
    });

    return checks;
  };

  const performOrphanChecks = (): SafetyCheck[] => {
    const checks: SafetyCheck[] = [];
    const categoryIds = new Set(categories.map(c => c.id));

    categories.forEach(category => {
      if (category.parentId && !categoryIds.has(category.parentId)) {
        checks.push({
          id: `orphan-${category.id}`,
          type: 'orphan',
          severity: 'warning',
          categoryId: category.id,
          categoryName: category.name,
          message: 'Category references non-existent parent',
          details: `Parent category ID "${category.parentId}" does not exist`,
          impact: [
            'Category hierarchy is broken',
            'Category may not display correctly',
            'Navigation and filtering issues'
          ],
          canProceed: true,
          autoFixable: true,
          fixAction: 'Remove invalid parent reference or create missing parent',
        });
      }
    });

    return checks;
  };

  const performDependencyChecks = async (): Promise<SafetyCheck[]> => {
    const checks: SafetyCheck[] = [];

    try {
      // Check for categories with no providers but active status
      const activeEmptyCategories = categories.filter(cat => 
        cat.isActive && (cat.providerCount === 0 || !cat.providerCount)
      );

      activeEmptyCategories.forEach(category => {
        checks.push({
          id: `empty-active-${category.id}`,
          type: 'dependency',
          severity: 'info',
          categoryId: category.id,
          categoryName: category.name,
          message: 'Active category has no providers',
          details: 'Category is marked as active but has no assigned providers',
          impact: [
            'Category appears in listings but has no services',
            'Poor user experience for customers',
            'Misleading category availability'
          ],
          canProceed: true,
          autoFixable: true,
          fixAction: 'Deactivate category or assign providers',
        });
      });

      // Check for inactive categories with providers
      const inactiveWithProviders = categories.filter(cat => 
        !cat.isActive && cat.providerCount && cat.providerCount > 0
      );

      inactiveWithProviders.forEach(category => {
        checks.push({
          id: `inactive-with-providers-${category.id}`,
          type: 'dependency',
          severity: 'warning',
          categoryId: category.id,
          categoryName: category.name,
          message: 'Inactive category has assigned providers',
          details: `${category.providerCount} providers are assigned to this inactive category`,
          impact: [
            'Providers may not be discoverable by customers',
            'Lost business opportunities',
            'Provider confusion about category status'
          ],
          canProceed: true,
          autoFixable: true,
          fixAction: 'Activate category or reassign providers',
        });
      });

    } catch (error) {
      console.error('Error performing dependency checks:', error);
    }

    return checks;
  };

  const performIntegrityChecks = (): SafetyCheck[] => {
    const checks: SafetyCheck[] = [];

    // Check for duplicate category names
    const nameMap = new Map<string, ProviderCategory[]>();
    categories.forEach(cat => {
      const name = cat.name.toLowerCase().trim();
      if (!nameMap.has(name)) {
        nameMap.set(name, []);
      }
      nameMap.get(name)!.push(cat);
    });

    nameMap.forEach((cats, name) => {
      if (cats.length > 1) {
        cats.forEach(cat => {
          checks.push({
            id: `duplicate-name-${cat.id}`,
            type: 'integrity',
            severity: 'warning',
            categoryId: cat.id,
            categoryName: cat.name,
            message: 'Duplicate category name detected',
            details: `Multiple categories share the name "${cat.name}"`,
            impact: [
              'Confusion in category selection',
              'Ambiguous category references',
              'Poor user experience'
            ],
            canProceed: true,
            autoFixable: false,
            fixAction: 'Rename categories to make them unique',
          });
        });
      }
    });

    // Check for excessive hierarchy depth
    const getDepth = (categoryId: string, visited = new Set<string>()): number => {
      if (visited.has(categoryId)) return 0; // Prevent infinite recursion
      visited.add(categoryId);
      
      const category = categories.find(c => c.id === categoryId);
      if (!category?.parentId) return 0;
      
      return 1 + getDepth(category.parentId, visited);
    };

    categories.forEach(category => {
      const depth = getDepth(category.id);
      if (depth > 3) {
        checks.push({
          id: `deep-hierarchy-${category.id}`,
          type: 'integrity',
          severity: 'info',
          categoryId: category.id,
          categoryName: category.name,
          message: `Category hierarchy is too deep (${depth} levels)`,
          details: 'Deep hierarchies can impact performance and user experience',
          impact: [
            'Complex navigation for users',
            'Performance impact on category loading',
            'Difficult category management'
          ],
          canProceed: true,
          autoFixable: false,
          fixAction: 'Consider flattening the category hierarchy',
        });
      }
    });

    return checks;
  };

  const handleAutoFix = async (check: SafetyCheck) => {
    if (!check.autoFixable || !onAutoFix) return;

    try {
      loading.startLoading({ message: 'Applying automatic fix...' });
      await onAutoFix(check);
      
      // Re-run safety checks after fix
      await performSafetyChecks();
      
      toast.success('Issue fixed successfully');
    } catch (error) {
      handleError(error, { action: 'auto_fix_safety_issue' });
    } finally {
      loading.stopLoading();
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return (
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Critical
          </span>
        );
      case 'warning':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            Warning
          </span>
        );
      case 'info':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            Info
          </span>
        );
      default:
        return null;
    }
  };

  const criticalIssues = safetyChecks.filter(check => check.severity === 'critical');
  const warningIssues = safetyChecks.filter(check => check.severity === 'warning');
  const infoIssues = safetyChecks.filter(check => check.severity === 'info');

  if (isAnalyzing) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600 dark:text-gray-400">
              Analyzing category safety...
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Category Safety Analysis
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {operation === 'delete' && targetCategory 
            ? `Safety analysis for deleting "${targetCategory.name}"`
            : 'Comprehensive category safety validation'
          }
        </p>
      </div>

      <div className="p-6">
        {safetyChecks.length === 0 ? (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              All Safety Checks Passed
            </h4>
            <p className="text-gray-600 dark:text-gray-400">
              No safety issues detected. Operation can proceed safely.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Safety Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                <div className="flex items-center">
                  <svg className="w-6 h-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <p className="text-sm font-medium text-red-900 dark:text-red-100">Critical Issues</p>
                    <p className="text-2xl font-semibold text-red-600 dark:text-red-400">{criticalIssues.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                <div className="flex items-center">
                  <svg className="w-6 h-6 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <div>
                    <p className="text-sm font-medium text-yellow-900 dark:text-yellow-100">Warnings</p>
                    <p className="text-2xl font-semibold text-yellow-600 dark:text-yellow-400">{warningIssues.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <div className="flex items-center">
                  <svg className="w-6 h-6 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Info</p>
                    <p className="text-2xl font-semibold text-blue-600 dark:text-blue-400">{infoIssues.length}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Safety Issues */}
            <div className="space-y-4">
              {safetyChecks.map((check) => (
                <div
                  key={check.id}
                  className={`p-4 rounded-lg border-l-4 ${
                    check.severity === 'critical' ? 'border-red-500 bg-red-50 dark:bg-red-900/20' :
                    check.severity === 'warning' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20' :
                    'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      {getSeverityIcon(check.severity)}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {check.message}
                          </h4>
                          {getSeverityBadge(check.severity)}
                          {!check.canProceed && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                              Blocks Operation
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                          {check.details}
                        </p>
                        
                        <button
                          onClick={() => setShowDetails(showDetails === check.id ? null : check.id)}
                          className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                        >
                          {showDetails === check.id ? 'Hide Details' : 'Show Impact Details'}
                        </button>
                        
                        {showDetails === check.id && (
                          <div className="mt-3 p-3 bg-white dark:bg-gray-800 rounded border">
                            <h5 className="font-medium text-gray-900 dark:text-white mb-2">Impact Analysis:</h5>
                            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                              {check.impact.map((impact, index) => (
                                <li key={index} className="flex items-start">
                                  <span className="text-red-500 mr-2">•</span>
                                  {impact}
                                </li>
                              ))}
                            </ul>
                            {check.fixAction && (
                              <div className="mt-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                                <p className="text-sm text-blue-800 dark:text-blue-200">
                                  <strong>Recommended Action:</strong> {check.fixAction}
                                </p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {check.autoFixable && onAutoFix && (
                      <button
                        onClick={() => handleAutoFix(check)}
                        disabled={loading.isLoading}
                        className="ml-4 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Auto Fix
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Deletion Impact Summary */}
            {operation === 'delete' && deletionImpact && (
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Deletion Impact Summary
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                      {deletionImpact.affectedProviders}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Affected Providers</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                      {deletionImpact.childCategories.length}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Child Categories</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {deletionImpact.totalBookings}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Historical Bookings</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                      ${deletionImpact.revenueImpact.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Revenue Impact</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

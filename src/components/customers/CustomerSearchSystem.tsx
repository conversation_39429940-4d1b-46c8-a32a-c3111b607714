import React, { useState, useEffect, useRef } from 'react';
import { Customer } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { useDebounce } from '../../hooks/useDebounce';

interface CustomerSearchSystemProps {
  onCustomerSelect: (customer: Customer) => void;
  onFiltersChange?: (filters: SearchFilters) => void;
  className?: string;
}

interface SearchFilters {
  search: string;
  verified: string;
  dateRange: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface SearchSuggestion {
  id: string;
  type: 'customer' | 'email' | 'phone';
  customer: Customer;
  matchedField: string;
  matchedValue: string;
}

export default function CustomerSearchSystem({
  onCustomerSelect,
  onFiltersChange,
  className = '',
}: CustomerSearchSystemProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [isSearching, setIsSearching] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    search: '',
    verified: '',
    dateRange: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const loading = useLoading();
  
  // Debounce search query to avoid excessive API calls
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  useEffect(() => {
    if (debouncedSearchQuery.trim().length >= 2) {
      fetchSearchSuggestions(debouncedSearchQuery);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [debouncedSearchQuery]);

  useEffect(() => {
    // Update filters when search query changes
    const updatedFilters = { ...filters, search: searchQuery };
    setFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  }, [searchQuery]);

  useEffect(() => {
    // Close suggestions when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const fetchSearchSuggestions = async (query: string) => {
    try {
      setIsSearching(true);
      const response = await adminApi.customers.searchCustomers({
        query,
        limit: 10,
        includeEmail: true,
        includePhone: true,
      });

      if (response.success && response.data) {
        const searchSuggestions: SearchSuggestion[] = [];

        response.data.forEach((customer: Customer) => {
          // Add customer name match
          if (customer.firstName || customer.lastName) {
            const fullName = `${customer.firstName || ''} ${customer.lastName || ''}`.trim();
            if (fullName.toLowerCase().includes(query.toLowerCase())) {
              searchSuggestions.push({
                id: `${customer.id}-name`,
                type: 'customer',
                customer,
                matchedField: 'name',
                matchedValue: fullName,
              });
            }
          }

          // Add email match
          if (customer.email.toLowerCase().includes(query.toLowerCase())) {
            searchSuggestions.push({
              id: `${customer.id}-email`,
              type: 'email',
              customer,
              matchedField: 'email',
              matchedValue: customer.email,
            });
          }

          // Add phone match
          if (customer.phone && customer.phone.includes(query)) {
            searchSuggestions.push({
              id: `${customer.id}-phone`,
              type: 'phone',
              customer,
              matchedField: 'phone',
              matchedValue: customer.phone,
            });
          }
        });

        setSuggestions(searchSuggestions.slice(0, 8)); // Limit to 8 suggestions
        setShowSuggestions(true);
        setSelectedSuggestionIndex(-1);
      }
    } catch (error) {
      handleError(error, { action: 'search_customers' });
      // Fallback suggestions for development
      if (query.length >= 2) {
        const mockSuggestions: SearchSuggestion[] = [
          {
            id: '1-name',
            type: 'customer',
            customer: {
              id: '1',
              email: '<EMAIL>',
              firstName: 'John',
              lastName: 'Doe',
              phone: '+************',
              verified: true,
              credits: 100,
              createdAt: '2024-01-15T10:00:00Z',
              updatedAt: '2024-01-15T10:00:00Z',
            },
            matchedField: 'name',
            matchedValue: 'John Doe',
          },
          {
            id: '2-email',
            type: 'email',
            customer: {
              id: '2',
              email: '<EMAIL>',
              firstName: 'Jane',
              lastName: 'Smith',
              phone: '+************',
              verified: false,
              credits: 50,
              createdAt: '2024-01-14T15:30:00Z',
              updatedAt: '2024-01-14T15:30:00Z',
            },
            matchedField: 'email',
            matchedValue: '<EMAIL>',
          },
        ].filter(s => 
          s.matchedValue.toLowerCase().includes(query.toLowerCase())
        );

        setSuggestions(mockSuggestions);
        setShowSuggestions(true);
      }
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    
    if (value.trim().length === 0) {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setSearchQuery(suggestion.matchedValue);
    setShowSuggestions(false);
    onCustomerSelect(suggestion.customer);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          handleSuggestionClick(suggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  };

  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    const updatedFilters = { ...filters, [key]: value };
    setFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSuggestions([]);
    setShowSuggestions(false);
    const updatedFilters = { ...filters, search: '' };
    setFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'customer':
        return (
          <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      case 'email':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
          </svg>
        );
      case 'phone':
        return (
          <svg className="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        );
    }
  };

  const highlightMatch = (text: string, query: string) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 text-gray-900 dark:text-white">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        
        <input
          ref={searchInputRef}
          type="text"
          placeholder="Search customers by name, email, or phone..."
          value={searchQuery}
          onChange={handleSearchInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (suggestions.length > 0) {
              setShowSuggestions(true);
            }
          }}
          className="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
        />
        
        {/* Loading Spinner */}
        {isSearching && (
          <div className="absolute inset-y-0 right-10 flex items-center">
            <svg className="animate-spin h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        )}
        
        {/* Clear Button */}
        {searchQuery && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Search Suggestions */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg max-h-80 overflow-y-auto"
        >
          <div className="py-2">
            {suggestions.map((suggestion, index) => (
              <button
                key={suggestion.id}
                onClick={() => handleSuggestionClick(suggestion)}
                className={`w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center space-x-3 ${
                  index === selectedSuggestionIndex ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                }`}
              >
                <div className="flex-shrink-0">
                  {getSuggestionIcon(suggestion.type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {suggestion.customer.firstName && suggestion.customer.lastName
                        ? `${suggestion.customer.firstName} ${suggestion.customer.lastName}`
                        : suggestion.customer.email
                      }
                    </p>
                    {suggestion.customer.verified && (
                      <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Verified
                      </span>
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                    {suggestion.matchedField === 'name' ? (
                      <>Email: {suggestion.customer.email}</>
                    ) : (
                      highlightMatch(suggestion.matchedValue, searchQuery)
                    )}
                  </p>
                  
                  <div className="flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400">
                    <span>{suggestion.customer.credits} credits</span>
                    {suggestion.customer.phone && (
                      <>
                        <span className="mx-1">•</span>
                        <span>{suggestion.customer.phone}</span>
                      </>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
          
          {/* Search Tips */}
          <div className="border-t border-gray-200 dark:border-gray-600 px-4 py-2 bg-gray-50 dark:bg-gray-700">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Use ↑↓ to navigate, Enter to select, Esc to close
            </p>
          </div>
        </div>
      )}

      {/* No Results */}
      {showSuggestions && suggestions.length === 0 && debouncedSearchQuery.length >= 2 && !isSearching && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
          <div className="px-4 py-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No customers found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Try searching with a different term or check the spelling.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { Customer } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { formatDistanceToNow } from 'date-fns';
import toast from 'react-hot-toast';
import CustomerSupportTools from './CustomerSupportTools';

interface CustomerProfileViewProps {
  customerId: string;
  onClose: () => void;
  onCustomerUpdate?: (customer: Customer) => void;
}

interface CustomerStats {
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  totalSpent: number;
  averageRating: number;
  favoriteCategories: string[];
  lastActivity: string;
  membershipDuration: string;
}

interface BookingHistory {
  id: string;
  providerName: string;
  serviceName: string;
  date: string;
  status: 'completed' | 'cancelled' | 'pending' | 'in-progress';
  amount: number;
  rating?: number;
  review?: string;
}

interface ActivityLog {
  id: string;
  type: 'booking' | 'payment' | 'review' | 'login' | 'profile_update';
  description: string;
  timestamp: string;
  details?: any;
}

export default function CustomerProfileView({ 
  customerId, 
  onClose, 
  onCustomerUpdate 
}: CustomerProfileViewProps) {
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [stats, setStats] = useState<CustomerStats | null>(null);
  const [bookings, setBookings] = useState<BookingHistory[]>([]);
  const [activities, setActivities] = useState<ActivityLog[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const loading = useLoading();

  useEffect(() => {
    if (customerId) {
      fetchCustomerDetails();
    }
  }, [customerId]);

  const fetchCustomerDetails = async () => {
    try {
      loading.startLoading({ message: 'Loading customer details...' });
      
      const [customerResponse, statsResponse, bookingsResponse, activitiesResponse] = await Promise.all([
        adminApi.customers.getCustomer(customerId),
        adminApi.customers.getCustomerStats(customerId),
        adminApi.customers.getCustomerBookings(customerId),
        adminApi.customers.getCustomerActivities(customerId),
      ]);

      if (customerResponse.success) {
        setCustomer(customerResponse.data);
      }

      if (statsResponse.success) {
        setStats(statsResponse.data);
      } else {
        // Fallback stats for development
        setStats({
          totalBookings: 23,
          completedBookings: 19,
          cancelledBookings: 4,
          totalSpent: 1250,
          averageRating: 4.3,
          favoriteCategories: ['Beauty & Wellness', 'Healthcare'],
          lastActivity: '2 hours ago',
          membershipDuration: '8 months',
        });
      }

      if (bookingsResponse.success) {
        setBookings(bookingsResponse.data);
      } else {
        // Fallback bookings for development
        setBookings([
          {
            id: '1',
            providerName: 'Elite Beauty Salon',
            serviceName: 'Hair Cut & Styling',
            date: '2024-01-15T10:00:00Z',
            status: 'completed',
            amount: 45,
            rating: 5,
            review: 'Excellent service, very professional!',
          },
          {
            id: '2',
            providerName: 'Premium Health Clinic',
            serviceName: 'General Consultation',
            date: '2024-01-10T14:30:00Z',
            status: 'completed',
            amount: 80,
            rating: 4,
          },
          {
            id: '3',
            providerName: 'FitZone Gym',
            serviceName: 'Personal Training Session',
            date: '2024-01-08T09:00:00Z',
            status: 'cancelled',
            amount: 60,
          },
        ]);
      }

      if (activitiesResponse.success) {
        setActivities(activitiesResponse.data);
      } else {
        // Fallback activities for development
        setActivities([
          {
            id: '1',
            type: 'booking',
            description: 'Booked Hair Cut & Styling with Elite Beauty Salon',
            timestamp: '2024-01-15T09:45:00Z',
          },
          {
            id: '2',
            type: 'review',
            description: 'Left a 5-star review for Elite Beauty Salon',
            timestamp: '2024-01-15T11:30:00Z',
          },
          {
            id: '3',
            type: 'login',
            description: 'Logged into the platform',
            timestamp: '2024-01-14T18:20:00Z',
          },
          {
            id: '4',
            type: 'profile_update',
            description: 'Updated profile information',
            timestamp: '2024-01-12T16:15:00Z',
          },
        ]);
      }
    } catch (error) {
      handleError(error, { action: 'fetch_customer_details' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleUpdateCredits = async (newCredits: number) => {
    if (!customer) return;

    try {
      loading.startLoading({ message: 'Updating customer credits...' });
      const response = await adminApi.customers.updateCustomerCredits(customer.id, newCredits);
      
      if (response.success) {
        const updatedCustomer = { ...customer, credits: newCredits };
        setCustomer(updatedCustomer);
        onCustomerUpdate?.(updatedCustomer);
        toast.success('Customer credits updated successfully');
      } else {
        throw new Error(response.message || 'Failed to update credits');
      }
    } catch (error) {
      handleError(error, { action: 'update_customer_credits' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleToggleVerification = async () => {
    if (!customer) return;

    try {
      loading.startLoading({ message: 'Updating verification status...' });
      const response = await adminApi.customers.toggleCustomerVerification(customer.id);
      
      if (response.success) {
        const updatedCustomer = { ...customer, verified: !customer.verified };
        setCustomer(updatedCustomer);
        onCustomerUpdate?.(updatedCustomer);
        toast.success(`Customer ${updatedCustomer.verified ? 'verified' : 'unverified'} successfully`);
      } else {
        throw new Error(response.message || 'Failed to update verification');
      }
    } catch (error) {
      handleError(error, { action: 'toggle_customer_verification' });
    } finally {
      loading.stopLoading();
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Completed
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            Pending
          </span>
        );
      case 'in-progress':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            In Progress
          </span>
        );
      case 'cancelled':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Cancelled
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            {status}
          </span>
        );
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'booking':
        return (
          <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      case 'payment':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        );
      case 'review':
        return (
          <svg className="w-4 h-4 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
          </svg>
        );
      case 'login':
        return (
          <svg className="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
          </svg>
        );
      case 'profile_update':
        return (
          <svg className="w-4 h-4 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '👤' },
    { id: 'bookings', name: 'Bookings', icon: '📅' },
    { id: 'activity', name: 'Activity', icon: '📊' },
    { id: 'support', name: 'Support', icon: '🛠️' },
    { id: 'settings', name: 'Settings', icon: '⚙️' },
  ];

  if (loading.isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-5/6 overflow-hidden">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            </div>
          </div>
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6">
          <p className="text-gray-500 dark:text-gray-400">Customer not found</p>
          <button
            onClick={onClose}
            className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-5/6 overflow-hidden flex flex-col">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <span className="text-lg font-medium text-gray-700 dark:text-gray-300">
                {customer.firstName?.charAt(0).toUpperCase() || customer.email.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {customer.firstName && customer.lastName
                  ? `${customer.firstName} ${customer.lastName}`
                  : customer.email
                }
              </h2>
              <div className="flex items-center space-x-2 mt-1">
                {customer.verified && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Verified
                  </span>
                )}
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {customer.credits} credits
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={handleToggleVerification}
              className={`px-4 py-2 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                customer.verified
                  ? 'text-red-700 bg-red-100 border border-red-300 hover:bg-red-200 focus:ring-red-500'
                  : 'text-green-700 bg-green-100 border border-green-300 hover:bg-green-200 focus:ring-green-500'
              }`}
            >
              {customer.verified ? 'Unverify' : 'Verify'} Customer
            </button>

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }
                `}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'overview' && (
            <OverviewTab customer={customer} stats={stats} />
          )}
          {activeTab === 'bookings' && (
            <BookingsTab bookings={bookings} />
          )}
          {activeTab === 'activity' && (
            <ActivityTab activities={activities} />
          )}
          {activeTab === 'support' && (
            <CustomerSupportTools
              customer={customer}
              onCustomerUpdate={(updatedCustomer) => {
                setCustomer(updatedCustomer);
                onCustomerUpdate?.(updatedCustomer);
              }}
            />
          )}
          {activeTab === 'settings' && (
            <SettingsTab customer={customer} onUpdateCredits={handleUpdateCredits} />
          )}
        </div>
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ customer, stats }: { customer: Customer; stats: CustomerStats | null }) {
  return (
    <div className="space-y-6">
      {/* Customer Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Personal Information
          </h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Full Name</label>
              <p className="text-sm text-gray-900 dark:text-white">
                {customer.firstName && customer.lastName
                  ? `${customer.firstName} ${customer.lastName}`
                  : 'Not provided'
                }
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
              <p className="text-sm text-gray-900 dark:text-white">{customer.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
              <p className="text-sm text-gray-900 dark:text-white">{customer.phone || 'Not provided'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Credits</label>
              <p className="text-sm text-gray-900 dark:text-white">{customer.credits}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Member Since</label>
              <p className="text-sm text-gray-900 dark:text-white">
                {formatDistanceToNow(new Date(customer.createdAt), { addSuffix: true })}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Verification Status</label>
              <p className="text-sm text-gray-900 dark:text-white">
                {customer.verified ? 'Verified' : 'Unverified'}
              </p>
            </div>
          </div>
        </div>

        {/* Customer Stats */}
        {stats && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Activity Summary
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-2xl font-semibold text-blue-600 dark:text-blue-400">
                  {stats.totalBookings}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Total Bookings</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-green-600 dark:text-green-400">
                  {stats.completedBookings}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Completed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-purple-600 dark:text-purple-400">
                  ${stats.totalSpent}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Total Spent</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-yellow-600 dark:text-yellow-400">
                  {stats.averageRating.toFixed(1)}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Avg Rating</p>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Last Activity</span>
                  <span className="text-gray-900 dark:text-white">{stats.lastActivity}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Membership Duration</span>
                  <span className="text-gray-900 dark:text-white">{stats.membershipDuration}</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Favorite Categories */}
      {stats && stats.favoriteCategories.length > 0 && (
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Favorite Categories
          </h3>
          <div className="flex flex-wrap gap-2">
            {stats.favoriteCategories.map((category, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                {category}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Bookings Tab Component
function BookingsTab({ bookings }: { bookings: BookingHistory[] }) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Completed
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            Pending
          </span>
        );
      case 'in-progress':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            In Progress
          </span>
        );
      case 'cancelled':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Cancelled
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            {status}
          </span>
        );
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Booking History ({bookings.length})
        </h3>
      </div>

      <div className="space-y-4">
        {bookings.map((booking) => (
          <div key={booking.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {booking.serviceName}
                  </h4>
                  {getStatusBadge(booking.status)}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                  Provider: {booking.providerName}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Date: {new Date(booking.date).toLocaleDateString()} at {new Date(booking.date).toLocaleTimeString()}
                </p>

                {booking.rating && (
                  <div className="flex items-center mb-2">
                    <span className="text-yellow-500 mr-1">⭐</span>
                    <span className="text-sm text-gray-900 dark:text-white mr-2">{booking.rating}</span>
                    {booking.review && (
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        "{booking.review}"
                      </span>
                    )}
                  </div>
                )}
              </div>

              <div className="text-right ml-4">
                <p className="font-semibold text-gray-900 dark:text-white">
                  ${booking.amount}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {bookings.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">No bookings found</p>
        </div>
      )}
    </div>
  );
}

// Activity Tab Component
function ActivityTab({ activities }: { activities: ActivityLog[] }) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'booking':
        return (
          <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      case 'payment':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        );
      case 'review':
        return (
          <svg className="w-4 h-4 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
          </svg>
        );
      case 'login':
        return (
          <svg className="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
          </svg>
        );
      case 'profile_update':
        return (
          <svg className="w-4 h-4 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
        Recent Activity
      </h3>

      <div className="space-y-4">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-start space-x-3">
            <div className="p-2 rounded-full bg-gray-100 dark:bg-gray-700">
              {getActivityIcon(activity.type)}
            </div>
            <div className="flex-1">
              <p className="text-sm text-gray-900 dark:text-white">
                {activity.description}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
              </p>
            </div>
          </div>
        ))}
      </div>

      {activities.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">No recent activity</p>
        </div>
      )}
    </div>
  );
}

// Settings Tab Component
function SettingsTab({ customer, onUpdateCredits }: {
  customer: Customer;
  onUpdateCredits: (credits: number) => void;
}) {
  const [newCredits, setNewCredits] = useState(customer.credits.toString());

  const handleCreditsSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const credits = parseInt(newCredits);
    if (!isNaN(credits) && credits >= 0) {
      onUpdateCredits(credits);
    }
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
        Customer Settings
      </h3>

      {/* Credits Management */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
          Credits Management
        </h4>
        <form onSubmit={handleCreditsSubmit} className="flex items-end space-x-3">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Current Credits: {customer.credits}
            </label>
            <input
              type="number"
              min="0"
              value={newCredits}
              onChange={(e) => setNewCredits(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
              placeholder="Enter new credit amount"
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Update Credits
          </button>
        </form>
      </div>

      {/* Account Information */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
          Account Information
        </h4>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Account ID</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">{customer.id}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Registration Date</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {new Date(customer.createdAt).toLocaleDateString()}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Last Updated</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {new Date(customer.updatedAt).toLocaleDateString()}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Verification Status</span>
            <span className={`text-sm font-medium ${
              customer.verified
                ? 'text-green-600 dark:text-green-400'
                : 'text-red-600 dark:text-red-400'
            }`}>
              {customer.verified ? 'Verified' : 'Unverified'}
            </span>
          </div>
        </div>
      </div>

      {/* Danger Zone */}
      <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-6">
        <h4 className="text-sm font-medium text-red-900 dark:text-red-100 mb-4">
          Danger Zone
        </h4>
        <div className="space-y-3">
          <button className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 border border-red-200 dark:border-red-800 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/30 text-red-700 dark:text-red-300">
            Suspend Customer Account
          </button>
          <button className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 border border-red-200 dark:border-red-800 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/30 text-red-700 dark:text-red-300">
            Reset Customer Password
          </button>
          <button className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 border border-red-200 dark:border-red-800 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/30 text-red-700 dark:text-red-300">
            Delete Customer Account
          </button>
        </div>
      </div>
    </div>
  );
}

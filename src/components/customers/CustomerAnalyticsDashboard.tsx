import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';

interface CustomerAnalytics {
  overview: {
    totalCustomers: number;
    activeCustomers: number;
    verifiedCustomers: number;
    averageCredits: number;
    totalBookings: number;
    totalSpent: number;
    retentionRate: number;
    growthRate: number;
  };
  demographics: {
    ageGroups: { range: string; count: number; percentage: number }[];
    genderDistribution: { gender: string; count: number; percentage: number }[];
    locationDistribution: { location: string; count: number; percentage: number }[];
    languagePreferences: { language: string; count: number; percentage: number }[];
  };
  behavior: {
    bookingFrequency: { frequency: string; count: number; percentage: number }[];
    preferredCategories: { category: string; bookings: number; customers: number }[];
    averageSessionDuration: number;
    peakUsageHours: { hour: number; usage: number }[];
    deviceTypes: { device: string; count: number; percentage: number }[];
  };
  retention: {
    cohortAnalysis: { month: string; retention: number[] }[];
    churnRate: number;
    lifetimeValue: number;
    repeatCustomerRate: number;
    averageLifespan: number;
  };
  trends: {
    registrations: { date: string; count: number }[];
    bookings: { date: string; count: number }[];
    revenue: { date: string; amount: number }[];
    satisfaction: { date: string; rating: number }[];
  };
}

interface CustomerAnalyticsDashboardProps {
  className?: string;
}

export default function CustomerAnalyticsDashboard({ className = '' }: CustomerAnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<CustomerAnalytics | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');
  const loading = useLoading();

  useEffect(() => {
    fetchAnalytics();
  }, [selectedPeriod]);

  const fetchAnalytics = async () => {
    try {
      loading.startLoading({ message: 'Loading customer analytics...' });
      const response = await adminApi.customers.getAnalytics(selectedPeriod);
      
      if (response.success) {
        setAnalytics(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch analytics');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_customer_analytics' });
      // Set fallback data for development
      setAnalytics({
        overview: {
          totalCustomers: 2847,
          activeCustomers: 1923,
          verifiedCustomers: 2156,
          averageCredits: 127,
          totalBookings: 8934,
          totalSpent: 456789,
          retentionRate: 78.5,
          growthRate: 15.3,
        },
        demographics: {
          ageGroups: [
            { range: '18-25', count: 567, percentage: 20 },
            { range: '26-35', count: 1139, percentage: 40 },
            { range: '36-45', count: 854, percentage: 30 },
            { range: '46-55', count: 227, percentage: 8 },
            { range: '55+', count: 60, percentage: 2 },
          ],
          genderDistribution: [
            { gender: 'Female', count: 1708, percentage: 60 },
            { gender: 'Male', count: 1139, percentage: 40 },
          ],
          locationDistribution: [
            { location: 'Algiers', count: 1139, percentage: 40 },
            { location: 'Oran', count: 569, percentage: 20 },
            { location: 'Constantine', count: 427, percentage: 15 },
            { location: 'Annaba', count: 285, percentage: 10 },
            { location: 'Other', count: 427, percentage: 15 },
          ],
          languagePreferences: [
            { language: 'Arabic', count: 1708, percentage: 60 },
            { language: 'French', count: 854, percentage: 30 },
            { language: 'English', count: 285, percentage: 10 },
          ],
        },
        behavior: {
          bookingFrequency: [
            { frequency: 'Weekly', count: 285, percentage: 10 },
            { frequency: 'Monthly', count: 1139, percentage: 40 },
            { frequency: 'Quarterly', count: 854, percentage: 30 },
            { frequency: 'Rarely', count: 569, percentage: 20 },
          ],
          preferredCategories: [
            { category: 'Beauty & Wellness', bookings: 3574, customers: 1423 },
            { category: 'Healthcare', bookings: 2681, customers: 1139 },
            { category: 'Fitness', bookings: 1787, customers: 854 },
            { category: 'Automotive', bookings: 892, customers: 569 },
          ],
          averageSessionDuration: 12.5,
          peakUsageHours: Array.from({ length: 24 }, (_, i) => ({
            hour: i,
            usage: Math.floor(Math.random() * 100) + 20,
          })),
          deviceTypes: [
            { device: 'Mobile', count: 1993, percentage: 70 },
            { device: 'Desktop', count: 569, percentage: 20 },
            { device: 'Tablet', count: 285, percentage: 10 },
          ],
        },
        retention: {
          cohortAnalysis: Array.from({ length: 12 }, (_, i) => ({
            month: new Date(Date.now() - i * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 7),
            retention: Array.from({ length: 6 }, (_, j) => Math.max(100 - j * 15 - Math.random() * 10, 20)),
          })),
          churnRate: 21.5,
          lifetimeValue: 234.56,
          repeatCustomerRate: 67.8,
          averageLifespan: 18.5,
        },
        trends: {
          registrations: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            count: Math.floor(Math.random() * 20) + 5,
          })),
          bookings: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            count: Math.floor(Math.random() * 50) + 10,
          })),
          revenue: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            amount: Math.floor(Math.random() * 5000) + 1000,
          })),
          satisfaction: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            rating: 4.0 + Math.random() * 1.0,
          })),
        },
      });
    } finally {
      loading.stopLoading();
    }
  };

  const periods = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 3 Months' },
    { value: '1y', label: 'Last Year' },
  ];

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'demographics', name: 'Demographics', icon: '👥' },
    { id: 'behavior', name: 'Behavior', icon: '🎯' },
    { id: 'retention', name: 'Retention', icon: '🔄' },
    { id: 'trends', name: 'Trends', icon: '📈' },
  ];

  if (loading.isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <div className="p-6 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            Unable to load customer analytics
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Customer Analytics
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Comprehensive insights into customer behavior, demographics, and platform engagement
            </p>
          </div>
          
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            {periods.map((period) => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }
              `}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <OverviewTab analytics={analytics} />
        )}
        {activeTab === 'demographics' && (
          <DemographicsTab demographics={analytics.demographics} />
        )}
        {activeTab === 'behavior' && (
          <BehaviorTab behavior={analytics.behavior} />
        )}
        {activeTab === 'retention' && (
          <RetentionTab retention={analytics.retention} />
        )}
        {activeTab === 'trends' && (
          <TrendsTab trends={analytics.trends} />
        )}
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ analytics }: { analytics: CustomerAnalytics }) {
  const { overview } = analytics;
  
  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Customers</p>
              <p className="text-2xl font-semibold text-blue-900 dark:text-blue-100">{overview.totalCustomers.toLocaleString()}</p>
              <p className="text-xs text-blue-700 dark:text-blue-300">+{overview.growthRate}% growth</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
              <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-green-600 dark:text-green-400">Active Customers</p>
              <p className="text-2xl font-semibold text-green-900 dark:text-green-100">{overview.activeCustomers.toLocaleString()}</p>
              <p className="text-xs text-green-700 dark:text-green-300">{((overview.activeCustomers / overview.totalCustomers) * 100).toFixed(1)}% of total</p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
              <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Total Revenue</p>
              <p className="text-2xl font-semibold text-purple-900 dark:text-purple-100">${overview.totalSpent.toLocaleString()}</p>
              <p className="text-xs text-purple-700 dark:text-purple-300">Avg: ${(overview.totalSpent / overview.totalCustomers).toFixed(0)} per customer</p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
              <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Retention Rate</p>
              <p className="text-2xl font-semibold text-yellow-900 dark:text-yellow-100">{overview.retentionRate}%</p>
              <p className="text-xs text-yellow-700 dark:text-yellow-300">Customer loyalty</p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Customer Engagement</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Bookings</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{overview.totalBookings.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Avg. Bookings per Customer</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{(overview.totalBookings / overview.totalCustomers).toFixed(1)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Verified Customers</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{overview.verifiedCustomers.toLocaleString()}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Financial Metrics</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Average Credits</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{overview.averageCredits}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Revenue per Booking</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">${(overview.totalSpent / overview.totalBookings).toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Customer LTV</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">${(overview.totalSpent / overview.totalCustomers).toFixed(2)}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Platform Health</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Verification Rate</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{((overview.verifiedCustomers / overview.totalCustomers) * 100).toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Activity Rate</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{((overview.activeCustomers / overview.totalCustomers) * 100).toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Growth Rate</span>
              <span className="text-sm font-medium text-green-600 dark:text-green-400">+{overview.growthRate}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Demographics Tab Component
function DemographicsTab({ demographics }: { demographics: CustomerAnalytics['demographics'] }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Age Distribution */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Age Distribution
          </h3>
          <div className="space-y-4">
            {demographics.ageGroups.map((group, index) => (
              <div key={group.range} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">{group.range} years</span>
                  <span className="text-gray-600 dark:text-gray-400">{group.count} ({group.percentage}%)</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      index === 0 ? 'bg-blue-500' :
                      index === 1 ? 'bg-green-500' :
                      index === 2 ? 'bg-yellow-500' :
                      index === 3 ? 'bg-purple-500' : 'bg-pink-500'
                    }`}
                    style={{ width: `${group.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Gender Distribution */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Gender Distribution
          </h3>
          <div className="space-y-4">
            {demographics.genderDistribution.map((gender, index) => (
              <div key={gender.gender} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">{gender.gender}</span>
                  <span className="text-gray-600 dark:text-gray-400">{gender.count} ({gender.percentage}%)</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${index === 0 ? 'bg-pink-500' : 'bg-blue-500'}`}
                    style={{ width: `${gender.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Location Distribution */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Geographic Distribution
          </h3>
          <div className="space-y-4">
            {demographics.locationDistribution.map((location, index) => (
              <div key={location.location} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">{location.location}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{location.percentage}% of customers</p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-white">{location.count}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">customers</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Language Preferences */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Language Preferences
          </h3>
          <div className="space-y-4">
            {demographics.languagePreferences.map((language, index) => (
              <div key={language.language} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div className="flex items-center">
                  <span className="text-2xl mr-3">
                    {language.language === 'Arabic' ? '🇩🇿' :
                     language.language === 'French' ? '🇫🇷' : '🇺🇸'}
                  </span>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">{language.language}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{language.percentage}% preference</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-white">{language.count}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">customers</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Behavior Tab Component
function BehaviorTab({ behavior }: { behavior: CustomerAnalytics['behavior'] }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Booking Frequency */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Booking Frequency
          </h3>
          <div className="space-y-4">
            {behavior.bookingFrequency.map((freq, index) => (
              <div key={freq.frequency} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">{freq.frequency}</span>
                  <span className="text-gray-600 dark:text-gray-400">{freq.count} customers ({freq.percentage}%)</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      index === 0 ? 'bg-green-500' :
                      index === 1 ? 'bg-blue-500' :
                      index === 2 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${freq.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Device Types */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Device Usage
          </h3>
          <div className="space-y-4">
            {behavior.deviceTypes.map((device, index) => (
              <div key={device.device} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div className="flex items-center">
                  <span className="text-2xl mr-3">
                    {device.device === 'Mobile' ? '📱' :
                     device.device === 'Desktop' ? '💻' : '📱'}
                  </span>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">{device.device}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{device.percentage}% of usage</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-white">{device.count}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">users</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Retention Tab Component
function RetentionTab({ retention }: { retention: CustomerAnalytics['retention'] }) {
  return (
    <div className="space-y-6">
      {/* Key Retention Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 text-center">
          <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{retention.churnRate}%</p>
          <p className="text-sm text-blue-700 dark:text-blue-300">Churn Rate</p>
        </div>
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 text-center">
          <p className="text-3xl font-bold text-green-600 dark:text-green-400">${retention.lifetimeValue}</p>
          <p className="text-sm text-green-700 dark:text-green-300">Lifetime Value</p>
        </div>
        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6 text-center">
          <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">{retention.repeatCustomerRate}%</p>
          <p className="text-sm text-purple-700 dark:text-purple-300">Repeat Customers</p>
        </div>
        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6 text-center">
          <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{retention.averageLifespan}</p>
          <p className="text-sm text-yellow-700 dark:text-yellow-300">Avg Lifespan (months)</p>
        </div>
      </div>

      {/* Cohort Analysis */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Cohort Retention Analysis
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr>
                <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider py-2">
                  Cohort Month
                </th>
                {Array.from({ length: 6 }, (_, i) => (
                  <th key={i} className="text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider py-2">
                    Month {i + 1}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="space-y-1">
              {retention.cohortAnalysis.slice(0, 6).map((cohort, index) => (
                <tr key={cohort.month}>
                  <td className="text-sm font-medium text-gray-900 dark:text-white py-2">
                    {cohort.month}
                  </td>
                  {cohort.retention.map((rate, i) => (
                    <td key={i} className="text-center py-2">
                      <span
                        className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                          rate >= 80 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          rate >= 60 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          rate >= 40 ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}
                      >
                        {rate.toFixed(0)}%
                      </span>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Retention Insights */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Retention Insights
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Key Findings</h4>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li className="flex items-start">
                <span className="text-green-500 mr-2">•</span>
                Strong first-month retention at {retention.cohortAnalysis[0]?.retention[0]?.toFixed(0)}%
              </li>
              <li className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                {retention.repeatCustomerRate}% of customers make repeat bookings
              </li>
              <li className="flex items-start">
                <span className="text-purple-500 mr-2">•</span>
                Average customer lifespan is {retention.averageLifespan} months
              </li>
              <li className="flex items-start">
                <span className="text-yellow-500 mr-2">•</span>
                Customer lifetime value averages ${retention.lifetimeValue}
              </li>
            </ul>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Recommendations</h4>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li className="flex items-start">
                <span className="text-blue-500 mr-2">→</span>
                Focus on improving 3-month retention rates
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">→</span>
                Implement loyalty programs for repeat customers
              </li>
              <li className="flex items-start">
                <span className="text-purple-500 mr-2">→</span>
                Target at-risk customers with re-engagement campaigns
              </li>
              <li className="flex items-start">
                <span className="text-orange-500 mr-2">→</span>
                Optimize onboarding to improve early retention
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

// Trends Tab Component
function TrendsTab({ trends }: { trends: CustomerAnalytics['trends'] }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Registration Trends */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-4">
            📈 Registration Trends
          </h3>
          <div className="space-y-3">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {trends.registrations.reduce((sum, day) => sum + day.count, 0)}
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300">Total New Customers</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                {(trends.registrations.reduce((sum, day) => sum + day.count, 0) / trends.registrations.length).toFixed(1)}
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300">Daily Average</p>
            </div>
          </div>
        </div>

        {/* Booking Trends */}
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
          <h3 className="text-lg font-medium text-green-900 dark:text-green-100 mb-4">
            📅 Booking Trends
          </h3>
          <div className="space-y-3">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {trends.bookings.reduce((sum, day) => sum + day.count, 0)}
              </p>
              <p className="text-sm text-green-700 dark:text-green-300">Total Bookings</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                {(trends.bookings.reduce((sum, day) => sum + day.count, 0) / trends.bookings.length).toFixed(1)}
              </p>
              <p className="text-sm text-green-700 dark:text-green-300">Daily Average</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trends */}
        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
          <h3 className="text-lg font-medium text-purple-900 dark:text-purple-100 mb-4">
            💰 Revenue Trends
          </h3>
          <div className="space-y-3">
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                ${trends.revenue.reduce((sum, day) => sum + day.amount, 0).toLocaleString()}
              </p>
              <p className="text-sm text-purple-700 dark:text-purple-300">Total Revenue</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-purple-600 dark:text-purple-400">
                ${(trends.revenue.reduce((sum, day) => sum + day.amount, 0) / trends.revenue.length).toLocaleString()}
              </p>
              <p className="text-sm text-purple-700 dark:text-purple-300">Daily Average</p>
            </div>
          </div>
        </div>

        {/* Satisfaction Trends */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
          <h3 className="text-lg font-medium text-yellow-900 dark:text-yellow-100 mb-4">
            ⭐ Satisfaction Trends
          </h3>
          <div className="space-y-3">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                {(trends.satisfaction.reduce((sum, day) => sum + day.rating, 0) / trends.satisfaction.length).toFixed(1)}
              </p>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">Average Rating</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-yellow-600 dark:text-yellow-400">
                {Math.max(...trends.satisfaction.map(s => s.rating)).toFixed(1)}
              </p>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">Peak Rating</p>
            </div>
          </div>
        </div>
      </div>

      {/* Trend Analysis */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Trend Analysis Summary
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Key Insights</h4>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li className="flex items-start">
                <span className="text-green-500 mr-2">•</span>
                Customer registrations are {trends.registrations.slice(-7).reduce((sum, day) => sum + day.count, 0) >
                trends.registrations.slice(-14, -7).reduce((sum, day) => sum + day.count, 0) ? 'increasing' : 'stable'} this week
              </li>
              <li className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                Booking activity shows consistent growth pattern
              </li>
              <li className="flex items-start">
                <span className="text-purple-500 mr-2">•</span>
                Revenue per customer is ${(trends.revenue.reduce((sum, day) => sum + day.amount, 0) /
                trends.registrations.reduce((sum, day) => sum + day.count, 0)).toFixed(0)} on average
              </li>
              <li className="flex items-start">
                <span className="text-yellow-500 mr-2">•</span>
                Customer satisfaction remains high at {(trends.satisfaction.reduce((sum, day) => sum + day.rating, 0) / trends.satisfaction.length).toFixed(1)}
              </li>
            </ul>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Strategic Recommendations</h4>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li className="flex items-start">
                <span className="text-blue-500 mr-2">→</span>
                Continue current acquisition strategies
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">→</span>
                Focus on converting new customers to active users
              </li>
              <li className="flex items-start">
                <span className="text-purple-500 mr-2">→</span>
                Optimize revenue per customer opportunities
              </li>
              <li className="flex items-start">
                <span className="text-orange-500 mr-2">→</span>
                Maintain high satisfaction levels through quality service
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

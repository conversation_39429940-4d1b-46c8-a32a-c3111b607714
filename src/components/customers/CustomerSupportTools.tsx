import React, { useState, useEffect } from 'react';
import { Customer } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { formatDistanceToNow } from 'date-fns';
import toast from 'react-hot-toast';

interface CustomerSupportToolsProps {
  customer: Customer;
  onCustomerUpdate?: (customer: Customer) => void;
  className?: string;
}

interface CommunicationLog {
  id: string;
  type: 'email' | 'sms' | 'call' | 'note' | 'system';
  subject: string;
  content: string;
  adminId: string;
  adminName: string;
  timestamp: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  metadata?: any;
}

interface CreditTransaction {
  id: string;
  type: 'add' | 'deduct' | 'refund' | 'bonus';
  amount: number;
  reason: string;
  adminId: string;
  adminName: string;
  timestamp: string;
  balanceBefore: number;
  balanceAfter: number;
}

interface AccountAction {
  id: string;
  action: 'verify' | 'unverify' | 'suspend' | 'unsuspend' | 'reset_password' | 'update_info';
  description: string;
  adminId: string;
  adminName: string;
  timestamp: string;
  details?: any;
}

export default function CustomerSupportTools({
  customer,
  onCustomerUpdate,
  className = '',
}: CustomerSupportToolsProps) {
  const [activeTab, setActiveTab] = useState('credits');
  const [communicationLogs, setCommunicationLogs] = useState<CommunicationLog[]>([]);
  const [creditTransactions, setCreditTransactions] = useState<CreditTransaction[]>([]);
  const [accountActions, setAccountActions] = useState<AccountAction[]>([]);
  
  // Credit Management State
  const [creditAmount, setCreditAmount] = useState('');
  const [creditReason, setCreditReason] = useState('');
  const [creditType, setCreditType] = useState<'add' | 'deduct' | 'refund' | 'bonus'>('add');
  
  // Communication State
  const [messageType, setMessageType] = useState<'email' | 'sms'>('email');
  const [messageSubject, setMessageSubject] = useState('');
  const [messageContent, setMessageContent] = useState('');
  
  // Account Status State
  const [statusAction, setStatusAction] = useState<'verify' | 'unverify' | 'suspend' | 'unsuspend' | 'reset_password'>('verify');
  const [statusReason, setStatusReason] = useState('');

  const loading = useLoading();

  useEffect(() => {
    fetchSupportData();
  }, [customer.id]);

  const fetchSupportData = async () => {
    try {
      loading.startLoading({ message: 'Loading support data...' });
      
      const [logsResponse, transactionsResponse, actionsResponse] = await Promise.all([
        adminApi.customers.getCommunicationLogs(customer.id),
        adminApi.customers.getCreditTransactions(customer.id),
        adminApi.customers.getAccountActions(customer.id),
      ]);

      if (logsResponse.success) {
        setCommunicationLogs(logsResponse.data);
      } else {
        // Fallback data for development
        setCommunicationLogs([
          {
            id: '1',
            type: 'email',
            subject: 'Welcome to Dalti Platform',
            content: 'Thank you for joining our platform. We are excited to have you!',
            adminId: 'admin1',
            adminName: 'Admin User',
            timestamp: '2024-01-15T10:00:00Z',
            status: 'delivered',
          },
          {
            id: '2',
            type: 'note',
            subject: 'Customer Inquiry',
            content: 'Customer called regarding booking cancellation policy.',
            adminId: 'admin2',
            adminName: 'Support Agent',
            timestamp: '2024-01-14T14:30:00Z',
            status: 'sent',
          },
        ]);
      }

      if (transactionsResponse.success) {
        setCreditTransactions(transactionsResponse.data);
      } else {
        // Fallback data for development
        setCreditTransactions([
          {
            id: '1',
            type: 'add',
            amount: 50,
            reason: 'Welcome bonus',
            adminId: 'system',
            adminName: 'System',
            timestamp: '2024-01-15T10:00:00Z',
            balanceBefore: 0,
            balanceAfter: 50,
          },
          {
            id: '2',
            type: 'add',
            amount: 100,
            reason: 'Customer service compensation',
            adminId: 'admin1',
            adminName: 'Admin User',
            timestamp: '2024-01-10T16:20:00Z',
            balanceBefore: 50,
            balanceAfter: 150,
          },
        ]);
      }

      if (actionsResponse.success) {
        setAccountActions(actionsResponse.data);
      } else {
        // Fallback data for development
        setAccountActions([
          {
            id: '1',
            action: 'verify',
            description: 'Account verified after document submission',
            adminId: 'admin1',
            adminName: 'Admin User',
            timestamp: '2024-01-15T11:00:00Z',
          },
        ]);
      }
    } catch (error) {
      handleError(error, { action: 'fetch_support_data' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleCreditTransaction = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!creditAmount || !creditReason) {
      toast.error('Please fill in all required fields');
      return;
    }

    const amount = parseFloat(creditAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    try {
      loading.startLoading({ message: 'Processing credit transaction...' });
      
      const response = await adminApi.customers.processCreditTransaction(customer.id, {
        type: creditType,
        amount,
        reason: creditReason,
      });

      if (response.success) {
        const newBalance = creditType === 'add' || creditType === 'bonus' || creditType === 'refund'
          ? customer.credits + amount
          : customer.credits - amount;

        const updatedCustomer = { ...customer, credits: newBalance };
        onCustomerUpdate?.(updatedCustomer);

        // Add to transaction history
        const newTransaction: CreditTransaction = {
          id: Date.now().toString(),
          type: creditType,
          amount,
          reason: creditReason,
          adminId: 'current-admin',
          adminName: 'Current Admin',
          timestamp: new Date().toISOString(),
          balanceBefore: customer.credits,
          balanceAfter: newBalance,
        };
        setCreditTransactions(prev => [newTransaction, ...prev]);

        // Reset form
        setCreditAmount('');
        setCreditReason('');
        
        toast.success(`Credits ${creditType === 'deduct' ? 'deducted' : 'added'} successfully`);
      } else {
        throw new Error(response.message || 'Failed to process transaction');
      }
    } catch (error) {
      handleError(error, { action: 'process_credit_transaction' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!messageSubject || !messageContent) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      loading.startLoading({ message: 'Sending message...' });
      
      const response = await adminApi.customers.sendMessage(customer.id, {
        type: messageType,
        subject: messageSubject,
        content: messageContent,
      });

      if (response.success) {
        // Add to communication logs
        const newLog: CommunicationLog = {
          id: Date.now().toString(),
          type: messageType,
          subject: messageSubject,
          content: messageContent,
          adminId: 'current-admin',
          adminName: 'Current Admin',
          timestamp: new Date().toISOString(),
          status: 'sent',
        };
        setCommunicationLogs(prev => [newLog, ...prev]);

        // Reset form
        setMessageSubject('');
        setMessageContent('');
        
        toast.success(`${messageType.toUpperCase()} sent successfully`);
      } else {
        throw new Error(response.message || 'Failed to send message');
      }
    } catch (error) {
      handleError(error, { action: 'send_message' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleAccountAction = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!statusReason && statusAction !== 'reset_password') {
      toast.error('Please provide a reason for this action');
      return;
    }

    try {
      loading.startLoading({ message: 'Processing account action...' });
      
      const response = await adminApi.customers.performAccountAction(customer.id, {
        action: statusAction,
        reason: statusReason,
      });

      if (response.success) {
        // Update customer status if applicable
        let updatedCustomer = { ...customer };
        if (statusAction === 'verify') {
          updatedCustomer.verified = true;
        } else if (statusAction === 'unverify') {
          updatedCustomer.verified = false;
        }
        onCustomerUpdate?.(updatedCustomer);

        // Add to account actions
        const newAction: AccountAction = {
          id: Date.now().toString(),
          action: statusAction,
          description: statusReason || `${statusAction} action performed`,
          adminId: 'current-admin',
          adminName: 'Current Admin',
          timestamp: new Date().toISOString(),
        };
        setAccountActions(prev => [newAction, ...prev]);

        // Reset form
        setStatusReason('');
        
        toast.success(`Account ${statusAction} completed successfully`);
      } else {
        throw new Error(response.message || 'Failed to perform action');
      }
    } catch (error) {
      handleError(error, { action: 'perform_account_action' });
    } finally {
      loading.stopLoading();
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'add':
        return <span className="text-green-500">+</span>;
      case 'deduct':
        return <span className="text-red-500">-</span>;
      case 'refund':
        return <span className="text-blue-500">↩</span>;
      case 'bonus':
        return <span className="text-purple-500">🎁</span>;
      default:
        return <span className="text-gray-500">•</span>;
    }
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'email':
        return (
          <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
          </svg>
        );
      case 'sms':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      case 'call':
        return (
          <svg className="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
        );
      case 'note':
        return (
          <svg className="w-4 h-4 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        );
      case 'system':
        return (
          <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const tabs = [
    { id: 'credits', name: 'Credit Management', icon: '💳' },
    { id: 'communication', name: 'Communication', icon: '💬' },
    { id: 'account', name: 'Account Actions', icon: '⚙️' },
    { id: 'history', name: 'Support History', icon: '📋' },
  ];

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Customer Support Tools
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Manage customer credits, communications, and account status
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }
              `}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'credits' && (
          <CreditManagementTab
            customer={customer}
            creditTransactions={creditTransactions}
            creditAmount={creditAmount}
            setCreditAmount={setCreditAmount}
            creditReason={creditReason}
            setCreditReason={setCreditReason}
            creditType={creditType}
            setCreditType={setCreditType}
            onSubmit={handleCreditTransaction}
            getTransactionIcon={getTransactionIcon}
            loading={loading.isLoading}
          />
        )}
        {activeTab === 'communication' && (
          <CommunicationTab
            communicationLogs={communicationLogs}
            messageType={messageType}
            setMessageType={setMessageType}
            messageSubject={messageSubject}
            setMessageSubject={setMessageSubject}
            messageContent={messageContent}
            setMessageContent={setMessageContent}
            onSubmit={handleSendMessage}
            getMessageIcon={getMessageIcon}
            loading={loading.isLoading}
          />
        )}
        {activeTab === 'account' && (
          <AccountActionsTab
            customer={customer}
            accountActions={accountActions}
            statusAction={statusAction}
            setStatusAction={setStatusAction}
            statusReason={statusReason}
            setStatusReason={setStatusReason}
            onSubmit={handleAccountAction}
            loading={loading.isLoading}
          />
        )}
        {activeTab === 'history' && (
          <SupportHistoryTab
            communicationLogs={communicationLogs}
            creditTransactions={creditTransactions}
            accountActions={accountActions}
            getMessageIcon={getMessageIcon}
            getTransactionIcon={getTransactionIcon}
          />
        )}
      </div>
    </div>
  );
}

// Credit Management Tab Component
function CreditManagementTab({
  customer,
  creditTransactions,
  creditAmount,
  setCreditAmount,
  creditReason,
  setCreditReason,
  creditType,
  setCreditType,
  onSubmit,
  getTransactionIcon,
  loading,
}: any) {
  return (
    <div className="space-y-6">
      {/* Current Balance */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-lg font-medium text-blue-900 dark:text-blue-100">
              Current Credit Balance
            </h4>
            <p className="text-3xl font-bold text-blue-600 dark:text-blue-400 mt-2">
              {customer.credits} Credits
            </p>
          </div>
          <div className="text-blue-600 dark:text-blue-400">
            <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
        </div>
      </div>

      {/* Credit Transaction Form */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Process Credit Transaction
        </h4>
        <form onSubmit={onSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Transaction Type
              </label>
              <select
                value={creditType}
                onChange={(e) => setCreditType(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
              >
                <option value="add">Add Credits</option>
                <option value="deduct">Deduct Credits</option>
                <option value="refund">Refund Credits</option>
                <option value="bonus">Bonus Credits</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Amount
              </label>
              <input
                type="number"
                min="1"
                step="1"
                value={creditAmount}
                onChange={(e) => setCreditAmount(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                placeholder="Enter amount"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Reason
            </label>
            <textarea
              value={creditReason}
              onChange={(e) => setCreditReason(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
              placeholder="Explain the reason for this transaction"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Processing...' : `${creditType === 'deduct' ? 'Deduct' : 'Add'} Credits`}
          </button>
        </form>
      </div>

      {/* Recent Transactions */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Recent Credit Transactions
        </h4>
        <div className="space-y-3">
          {creditTransactions.slice(0, 5).map((transaction) => (
            <div key={transaction.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="text-lg">
                  {getTransactionIcon(transaction.type)}
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)} Credits
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {transaction.reason}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {formatDistanceToNow(new Date(transaction.timestamp), { addSuffix: true })} by {transaction.adminName}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className={`font-semibold ${
                  transaction.type === 'deduct' ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'
                }`}>
                  {transaction.type === 'deduct' ? '-' : '+'}{transaction.amount}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Balance: {transaction.balanceAfter}
                </p>
              </div>
            </div>
          ))}

          {creditTransactions.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No credit transactions found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Communication Tab Component
function CommunicationTab({
  communicationLogs,
  messageType,
  setMessageType,
  messageSubject,
  setMessageSubject,
  messageContent,
  setMessageContent,
  onSubmit,
  getMessageIcon,
  loading,
}: any) {
  return (
    <div className="space-y-6">
      {/* Send Message Form */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Send Message to Customer
        </h4>
        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Message Type
            </label>
            <select
              value={messageType}
              onChange={(e) => setMessageType(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
            >
              <option value="email">Email</option>
              <option value="sms">SMS</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Subject
            </label>
            <input
              type="text"
              value={messageSubject}
              onChange={(e) => setMessageSubject(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
              placeholder="Enter message subject"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Message Content
            </label>
            <textarea
              value={messageContent}
              onChange={(e) => setMessageContent(e.target.value)}
              rows={5}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
              placeholder="Enter your message content"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Sending...' : `Send ${messageType.toUpperCase()}`}
          </button>
        </form>
      </div>

      {/* Communication History */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Communication History
        </h4>
        <div className="space-y-3">
          {communicationLogs.map((log) => (
            <div key={log.id} className="p-4 bg-white dark:bg-gray-800 rounded-lg">
              <div className="flex items-start space-x-3">
                <div className="mt-1">
                  {getMessageIcon(log.type)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-gray-900 dark:text-white">
                      {log.subject}
                    </h5>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      log.status === 'delivered' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      log.status === 'sent' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      log.status === 'read' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {log.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    {log.content}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>{log.type.toUpperCase()}</span>
                    <span>
                      {formatDistanceToNow(new Date(log.timestamp), { addSuffix: true })} by {log.adminName}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {communicationLogs.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No communication history found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Account Actions Tab Component
function AccountActionsTab({
  customer,
  accountActions,
  statusAction,
  setStatusAction,
  statusReason,
  setStatusReason,
  onSubmit,
  loading,
}: any) {
  return (
    <div className="space-y-6">
      {/* Current Account Status */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Current Account Status
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Verification Status</p>
            <p className={`text-lg font-semibold ${
              customer.verified ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
            }`}>
              {customer.verified ? 'Verified' : 'Unverified'}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Account Status</p>
            <p className="text-lg font-semibold text-green-600 dark:text-green-400">
              Active
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Member Since</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {formatDistanceToNow(new Date(customer.createdAt), { addSuffix: true })}
            </p>
          </div>
        </div>
      </div>

      {/* Account Action Form */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Perform Account Action
        </h4>
        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Action Type
            </label>
            <select
              value={statusAction}
              onChange={(e) => setStatusAction(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
            >
              <option value="verify">Verify Account</option>
              <option value="unverify">Remove Verification</option>
              <option value="suspend">Suspend Account</option>
              <option value="unsuspend">Unsuspend Account</option>
              <option value="reset_password">Reset Password</option>
            </select>
          </div>

          {statusAction !== 'reset_password' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Reason
              </label>
              <textarea
                value={statusReason}
                onChange={(e) => setStatusReason(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                placeholder="Explain the reason for this action"
                required={statusAction !== 'reset_password'}
              />
            </div>
          )}

          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex">
              <svg className="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div>
                <h5 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Warning
                </h5>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                  This action will affect the customer's account. Please ensure you have the proper authorization.
                </p>
              </div>
            </div>
          </div>

          <button
            type="submit"
            disabled={loading}
            className={`w-full px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
              statusAction === 'suspend' || statusAction === 'unverify'
                ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
                : 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
            }`}
          >
            {loading ? 'Processing...' : `${statusAction.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}`}
          </button>
        </form>
      </div>

      {/* Recent Account Actions */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Recent Account Actions
        </h4>
        <div className="space-y-3">
          {accountActions.map((action) => (
            <div key={action.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {action.action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {action.description}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatDistanceToNow(new Date(action.timestamp), { addSuffix: true })} by {action.adminName}
                </p>
              </div>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                action.action === 'verify' || action.action === 'unsuspend'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : action.action === 'suspend' || action.action === 'unverify'
                  ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
              }`}>
                {action.action}
              </div>
            </div>
          ))}

          {accountActions.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No account actions found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Support History Tab Component
function SupportHistoryTab({
  communicationLogs,
  creditTransactions,
  accountActions,
  getMessageIcon,
  getTransactionIcon,
}: any) {
  // Combine all activities and sort by timestamp
  const allActivities = [
    ...communicationLogs.map(log => ({ ...log, activityType: 'communication' })),
    ...creditTransactions.map(transaction => ({ ...transaction, activityType: 'credit' })),
    ...accountActions.map(action => ({ ...action, activityType: 'account' })),
  ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  return (
    <div className="space-y-6">
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Complete Support History
        </h4>

        <div className="space-y-4">
          {allActivities.map((activity, index) => (
            <div key={`${activity.activityType}-${activity.id}`} className="relative">
              {/* Timeline line */}
              {index < allActivities.length - 1 && (
                <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-200 dark:bg-gray-600"></div>
              )}

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-full flex items-center justify-center">
                  {activity.activityType === 'communication' && getMessageIcon(activity.type)}
                  {activity.activityType === 'credit' && (
                    <span className="text-lg">{getTransactionIcon(activity.type)}</span>
                  )}
                  {activity.activityType === 'account' && (
                    <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  )}
                </div>

                <div className="flex-1 bg-white dark:bg-gray-800 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-gray-900 dark:text-white">
                      {activity.activityType === 'communication' && activity.subject}
                      {activity.activityType === 'credit' && `${activity.type.charAt(0).toUpperCase() + activity.type.slice(1)} Credits`}
                      {activity.activityType === 'account' && activity.action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </h5>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                    </span>
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    {activity.activityType === 'communication' && activity.content}
                    {activity.activityType === 'credit' && activity.reason}
                    {activity.activityType === 'account' && activity.description}
                  </p>

                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span className="capitalize">
                      {activity.activityType} • {activity.adminName || 'System'}
                    </span>
                    {activity.activityType === 'credit' && (
                      <span className={`font-medium ${
                        activity.type === 'deduct' ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'
                      }`}>
                        {activity.type === 'deduct' ? '-' : '+'}{activity.amount} credits
                      </span>
                    )}
                    {activity.activityType === 'communication' && (
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        activity.status === 'delivered' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                        activity.status === 'sent' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                        'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                      }`}>
                        {activity.status}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}

          {allActivities.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No support history found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

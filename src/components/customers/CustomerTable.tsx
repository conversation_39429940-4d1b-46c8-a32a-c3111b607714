import React, { useState, useEffect } from 'react';
import { Customer, CustomersListRequest } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { formatDistanceToNow } from 'date-fns';
import ResponsiveTable from '../common/ResponsiveTable';
import { getStatusBgClass } from '../../utils/daltiColors';
import CustomerProfileView from './CustomerProfileView';
import CustomerSearchSystem from './CustomerSearchSystem';
import LoadingSpinner, { TableSkeleton } from '../common/LoadingSpinner';

interface CustomerTableProps {
  onCustomerSelect?: (customer: Customer) => void;
  onCustomerEdit?: (customer: Customer) => void;
  className?: string;
}

interface TableFilters {
  search: string;
  verified: string;
  dateRange: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export default function CustomerTable({
  onCustomerSelect,
  onCustomerEdit,
  className = ''
}: CustomerTableProps) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });
  const [filters, setFilters] = useState<TableFilters>({
    search: '',
    verified: '',
    dateRange: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);

  const loading = useLoading('Loading customers...');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchCustomers();
  }, []);

  // Reset pagination when filters change
  useEffect(() => {
    setPagination(prev => ({ ...prev, page: 1 }));
  }, [filters.search, filters.verified, filters.dateRange]);

  const fetchCustomers = async () => {
    try {
      // loading.startLoading();
      setIsLoading(true);
      
      const params: CustomersListRequest = {
        page: pagination.page,
        limit: pagination.limit,
        search: filters.search || undefined,
        verified: filters.verified ? filters.verified === 'true' : undefined,
        dateRange: filters.dateRange || undefined,
        sortBy: filters.sortBy as any,
        sortOrder: filters.sortOrder,
      };

      const response = await adminApi.customers.getCustomers(params);
      
      if (response.success) {
        setCustomers(response.data);
        if (response.pagination) {
          setPagination(prev => ({
            ...prev,
            total: response.pagination!.total,
            totalPages: response.pagination!.totalPages,
          }));
        }
      } else {
        throw new Error(response.message || 'Failed to fetch customers');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_customers' });
      // Set fallback data for development
      setCustomers([
        {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          dateOfBirth: '1990-05-15',
          gender: 'male',
          verified: true,
          credits: 150,
          totalBookings: 12,
          completedBookings: 10,
          cancelledBookings: 2,
          noShowCount: 0,
          averageRating: 4.8,
          createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          lastActiveAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          preferredLanguage: 'en',
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '+1234567891',
          dateOfBirth: '1985-08-22',
          gender: 'female',
          verified: false,
          credits: 50,
          totalBookings: 3,
          completedBookings: 2,
          cancelledBookings: 1,
          noShowCount: 0,
          averageRating: 4.5,
          createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          lastActiveAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          preferredLanguage: 'en',
        },
        {
          id: '3',
          name: 'Ahmed Hassan',
          email: '<EMAIL>',
          phone: '+1234567892',
          dateOfBirth: '1992-12-10',
          gender: 'male',
          verified: true,
          credits: 200,
          totalBookings: 25,
          completedBookings: 23,
          cancelledBookings: 2,
          noShowCount: 1,
          averageRating: 4.9,
          createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          lastActiveAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
          preferredLanguage: 'ar',
        },
      ]);
    } finally {
      // loading.stopLoading();
      setIsLoading(false)
    }
  };



  const handleSort = (column: string) => {
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortBy === column && prev.sortOrder === 'asc' ? 'desc' : 'asc',
    }));
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleSelectCustomer = (customerId: string) => {
    setSelectedCustomers(prev => 
      prev.includes(customerId) 
        ? prev.filter(id => id !== customerId)
        : [...prev, customerId]
    );
  };

  const handleSelectAll = () => {
    setSelectedCustomers(
      selectedCustomers.length === customers.length 
        ? [] 
        : customers.map(c => c.id)
    );
  };

  const getVerificationBadge = (verified: boolean) => {
    if (verified) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          Verified
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          Unverified
        </span>
      );
    }
  };

  const getActivityStatus = (lastActiveAt?: string) => {
    if (!lastActiveAt) return 'Never';
    
    const lastActive = new Date(lastActiveAt);
    const now = new Date();
    const diffHours = (now.getTime() - lastActive.getTime()) / (1000 * 60 * 60);
    
    if (diffHours < 1) {
      return (
        <span className="text-green-600 dark:text-green-400 font-medium">
          Active now
        </span>
      );
    } else if (diffHours < 24) {
      return (
        <span className="text-blue-600 dark:text-blue-400">
          {Math.floor(diffHours)}h ago
        </span>
      );
    } else {
      return (
        <span className="text-gray-500 dark:text-gray-400">
          {formatDistanceToNow(lastActive, { addSuffix: true })}
        </span>
      );
    }
  };

  const getCompletionRate = (completed: number, total: number) => {
    if (total === 0) return 0;
    return Math.round((completed / total) * 100);
  };

  const getLanguageFlag = (language?: string) => {
    switch (language) {
      case 'ar':
        return '🇸🇦';
      case 'fr':
        return '🇫🇷';
      case 'en':
      default:
        return '🇺🇸';
    }
  };

  if (isLoading) {
    return <TableLoading isLoading={isLoading} rows={10} columns={8} message="Loading customers..." />;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Customers ({pagination.total})
          </h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Manage and monitor all customers on the platform
          </p>
        </div>

        <button
          onClick={fetchCustomers}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
      </div>

      {/* Advanced Search System */}
      <CustomerSearchSystem
        onCustomerSelect={(customer) => {
          setSelectedCustomer(customer.id);
          onCustomerSelect?.(customer);
        }}
        onFiltersChange={setFilters}
      />

      {/* Customer Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        {/* Bulk Actions */}
        {selectedCustomers.length > 0 && (
          <div className="mt-4 flex items-center justify-between bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
            <span className="text-sm text-blue-700 dark:text-blue-300">
              {selectedCustomers.length} customer(s) selected
            </span>
            <div className="flex items-center space-x-2">
              <button className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700">
                Verify Selected
              </button>
              <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                Add Credits
              </button>
              <button className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                Suspend
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedCustomers.length === customers.length && customers.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center">
                  Customer
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                  </svg>
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Credits
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Bookings
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Completion
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Last Active
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort('createdAt')}
              >
                <div className="flex items-center">
                  Joined
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                  </svg>
                </div>
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {customers.map((customer) => (
              <tr
                key={customer.id}
                className="hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                onClick={() => {
                  setSelectedCustomer(customer.id);
                  onCustomerSelect?.(customer);
                }}
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedCustomers.includes(customer.id)}
                    onChange={(e) => {
                      e.stopPropagation();
                      handleSelectCustomer(customer.id);
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {customer.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {customer.name}
                        </div>
                        <span className="ml-2 text-lg">
                          {getLanguageFlag(customer.preferredLanguage)}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {customer.email}
                      </div>
                      {customer.phone && (
                        <div className="text-xs text-gray-400 dark:text-gray-500">
                          {customer.phone}
                        </div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getVerificationBadge(customer.verified)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {customer.credits}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    credits
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {customer.totalBookings}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {customer.completedBookings} completed
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {getCompletionRate(customer.completedBookings, customer.totalBookings)}%
                    </div>
                    <div className="ml-2 w-16 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ 
                          width: `${getCompletionRate(customer.completedBookings, customer.totalBookings)}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {getActivityStatus(customer.lastActiveAt)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {formatDistanceToNow(new Date(customer.createdAt), { addSuffix: true })}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onCustomerEdit?.(customer);
                      }}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      Edit
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onCustomerSelect?.(customer);
                      }}
                      className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                    >
                      View
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="px-6 py-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700 dark:text-gray-300">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              {/* Page Numbers */}
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const pageNum = i + 1;
                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`px-3 py-1 text-sm border rounded ${
                      pagination.page === pageNum
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
              
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages}
                className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Customer Profile Modal */}
      {selectedCustomer && (
        <CustomerProfileView
          customerId={selectedCustomer}
          onClose={() => setSelectedCustomer(null)}
          onCustomerUpdate={(updatedCustomer) => {
            setCustomers(prev =>
              prev.map(c => c.id === updatedCustomer.id ? updatedCustomer : c)
            );
          }}
        />
      )}
    </div>
  );
}


const TableLoading = ({ isLoading, rows, columns, message }: any) => {
  if (!isLoading) return null;

  return (
    <div className="space-y-4">
      {message && (
        <div className="text-center py-4">
          <LoadingSpinner size="md" message={message} />
        </div>
      )}
      <TableSkeleton rows={rows} columns={columns} />
    </div>
  );
};

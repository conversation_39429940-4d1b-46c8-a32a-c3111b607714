import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { tokenManager } from '../utils/tokenManager';
import { cacheManager } from '../utils/cacheManager';
import { ApiResponse, ApiError } from '../types';
import toast from 'react-hot-toast';

// HTTP Client Configuration
class HttpClient {
  private client: AxiosInstance;

  constructor() {
    // Create axios instance with base configuration
    this.client = axios.create({
      baseURL: this.getBaseURL(),
      timeout: 30000, // 30 seconds
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        Authorization: `Bearer ${tokenManager.getToken()}`,
      },
    });

    this.setupInterceptors();
  }

  /**
   * Get the appropriate base URL based on environment
   */
  private getBaseURL(): string {
    const isProduction = import.meta.env.VITE_NODE_ENV === 'production';
    return isProduction 
      ? import.meta.env.VITE_API_BASE_URL_PROD 
      : import.meta.env.VITE_API_BASE_URL_DEV;
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor - Add auth token to requests
    this.client.interceptors.request.use(
      async (config) => {
        // Get valid token (will refresh if needed)
        const token = await tokenManager.getToken();
        
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request timestamp for debugging
        (config as any).metadata = { startTime: new Date() };

        return config;
      },
      (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor - Handle responses and errors
    this.client.interceptors.response.use(
      (response) => {
        // Log response time for debugging
        const endTime = new Date();
        const startTime = (response.config as any).metadata?.startTime;
        if (startTime) {
          const duration = endTime.getTime() - startTime.getTime();
          console.log(`API Request to ${response.config.url} took ${duration}ms`);
        }

        return response;
      },
      async (error: AxiosError) => {
        // Handle 401 errors - tokens are auto-refreshed, so 401 means session is invalid
        if (error.response?.status === 401) {
          // Clear tokens and redirect to login
          tokenManager.clearTokens();
          window.location.href = '/login';
          return Promise.reject(error);
        }

        // Handle other errors
        return Promise.reject(this.handleError(error));
      }
    );
  }



  /**
   * Handle and format API errors
   */
  private handleError(error: AxiosError): ApiError {
    const apiError: ApiError = {
      code: 'UNKNOWN_ERROR',
      message: 'An unexpected error occurred',
      details: null,
      timestamp: new Date().toISOString(),
    };

    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;

      apiError.code = `HTTP_${status}`;
      apiError.details = data;

      switch (status) {
        case 400:
          apiError.message = (data as any)?.message || 'Bad request';
          break;
        case 401:
          apiError.message = 'Authentication required';
          break;
        case 403:
          apiError.message = 'Access forbidden';
          break;
        case 404:
          apiError.message = 'Resource not found';
          break;
        case 409:
          apiError.message = (data as any)?.message || 'Conflict occurred';
          break;
        case 422:
          apiError.message = (data as any)?.message || 'Validation failed';
          break;
        case 429:
          apiError.message = 'Too many requests. Please try again later.';
          break;
        case 500:
          apiError.message = 'Internal server error';
          break;
        case 502:
          apiError.message = 'Bad gateway';
          break;
        case 503:
          apiError.message = 'Service unavailable';
          break;
        default:
          apiError.message = (data as any)?.message || `Server error (${status})`;
      }

      // Log error for debugging (suppress 404s in development to reduce noise)
      if (status !== 404 || import.meta.env.VITE_NODE_ENV === 'production') {
        console.error('API Error:', apiError);
      }
    } else if (error.request) {
      // Network error
      apiError.code = 'NETWORK_ERROR';
      apiError.message = 'Network error. Please check your connection.';
      console.error('API Error:', apiError);
    } else {
      // Request setup error
      apiError.code = 'REQUEST_ERROR';
      apiError.message = error.message || 'Request configuration error';
      console.error('API Error:', apiError);
    }

    return apiError;
  }

  /**
   * Generic request method with caching support
   */
  async request<T = any>(config: AxiosRequestConfig & { cache?: boolean; cacheKey?: string; cacheTTL?: number }): Promise<ApiResponse<T>> {
    const { cache = false, cacheKey, cacheTTL, ...axiosConfig } = config;

    // Check cache for GET requests
    if (cache && axiosConfig.method === 'GET' && cacheKey) {
      const cached = cacheManager.get<ApiResponse<T>>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.client(axiosConfig);

      // Cache successful GET responses
      if (cache && axiosConfig.method === 'GET' && cacheKey && response.data.success) {
        cacheManager.set(cacheKey, response.data, cacheTTL);
      }

      return response.data;
    } catch (error) {
      throw error; // Error is already processed by interceptor
    }
  }

  /**
   * GET request with caching support
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig & { cache?: boolean; cacheKey?: string; cacheTTL?: number }): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  /**
   * POST request
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  /**
   * PUT request
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PATCH', url, data });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  /**
   * Raw GET request that returns unwrapped response data
   * Used for endpoints that don't follow the standard ApiResponse structure
   */
  async getRaw<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.client.get<T>(url, config);
      return response.data;
    } catch (error) {
      throw error; // Error is already processed by interceptor
    }
  }

  /**
   * Upload file
   */
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.request<T>({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  /**
   * Download file
   */
  async download(url: string, filename?: string): Promise<void> {
    try {
      const response = await this.client({
        method: 'GET',
        url,
        responseType: 'blob',
      });

      // Create download link
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      toast.error('Download failed');
      throw error;
    }
  }

  /**
   * Cancel request
   */
  createCancelToken() {
    return axios.CancelToken.source();
  }

  /**
   * Check if error is a cancel error
   */
  isCancel(error: any): boolean {
    return axios.isCancel(error);
  }
}

// Export singleton instance
export const httpClient = new HttpClient();

// Export default instance for convenience
export default httpClient;

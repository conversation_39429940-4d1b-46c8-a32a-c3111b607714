// Dalti Admin API Services
//
// API Endpoint Patterns:
// - Authentication: /api/auth/admin/* (login, logout)
// - User Profile: /auth/me (get current user info)
// - All Admin Operations: /api/auth/admin/* (requires Authorization token)
//
// All endpoints automatically include Authorization: Bearer <token> header
// via httpClient interceptor for authenticated requests.

import { httpClient } from './httpClient';
import cacheManager, { cacheUtils, CACHE_KEYS } from '../utils/cacheManager';
import {
  ApiResponse,
  AdminLoginRequest,
  AdminLoginResponse,
  Provider,
  RawProvider,
  ProvidersListRequest,
  ProviderApprovalRequest,
  ProviderStats,
  ProviderServiceDetail,
  ProviderLocationDetail,
  Customer,
  RawCustomer,
  CustomersListRequest,
  ProviderCategory,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  ImageUploadRequest,
  CategoryImageUploadResponse,
  FileUploadResponse,
  DashboardMetrics,
  RecentActivity,
  AnalyticsData,
  AuditLog,
  AuditLogsRequest,
  Advertisement,
  CreateAdvertisementRequest,
  UpdateAdvertisementRequest,
  AdvertisementsListRequest,
  AdvertisementImageUploadResponse,
  PaginationInfo,
} from '../types';

/**
 * Transform raw provider data from API to normalized Provider interface
 */
function transformRawProvider(rawProvider: RawProvider): Provider {
  return {
    id: rawProvider.id.toString(),
    name: rawProvider.title,
    email: rawProvider.user?.email || '',
    phone: rawProvider.phone,
    businessTitle: rawProvider.title,
    description: rawProvider.presentation,
    verified: rawProvider.isVerified,
    status: rawProvider.isVerified ? 'approved' : 'pending',
    locationCount: 1, // Default value, can be updated when location data is available
    serviceCount: rawProvider._count?.services || 0,
    customerCount: rawProvider._count?.customerFolders || 0,
    rating: rawProvider.averageRating || undefined,
    totalRatings: rawProvider.totalReviews || 0,
    createdAt: rawProvider.createdAt,
    updatedAt: rawProvider.updatedAt,
    category: rawProvider.category ? {
      id: rawProvider.category.id.toString(),
      title: rawProvider.category.title,
      description: '',
      isActive: true,
      createdAt: rawProvider.createdAt,
      updatedAt: rawProvider.updatedAt,
    } : undefined,
  };
}

/**
 * Transform raw customer data from API to normalized Customer interface
 */
function transformRawCustomer(rawCustomer: RawCustomer): Customer {
  return {
    id: rawCustomer.id,
    name: `${rawCustomer.firstName} ${rawCustomer.lastName}`.trim(),
    email: rawCustomer.email,
    phone: rawCustomer.mobileNumber || undefined,
    verified: rawCustomer.isEmailVerified, // Consider email verification as primary verification
    credits: 0, // Default value, can be updated when credit data is available
    totalBookings: rawCustomer._count?.customerFolders || 0,
    completedBookings: 0, // Default value, can be updated when booking data is available
    cancelledBookings: 0, // Default value
    noShowCount: 0, // Default value
    createdAt: rawCustomer.createdAt,
    updatedAt: rawCustomer.createdAt, // Use createdAt as fallback
    preferredLanguage: 'en', // Default value
  };
}

/**
 * Authentication API Services
 */
export const authApi = {
  /**
   * Admin login
   */
  async login(credentials: AdminLoginRequest): Promise<ApiResponse<AdminLoginResponse>> {
    return httpClient.post<AdminLoginResponse>('/api/auth/admin/login', credentials);
  },

  /**
   * Get current admin profile using /auth/me endpoint
   * Note: This endpoint returns raw data, not wrapped in ApiResponse structure
   */
  async getProfile(): Promise<ApiResponse<any>> {
    try {
      // Use the new getRaw method since /auth/me returns raw data
      const rawData = await httpClient.getRaw('/auth/me');

      // Wrap the raw response in ApiResponse structure
      return {
        success: true,
        data: rawData,
        message: 'Profile fetched successfully'
      };
    } catch (error) {
      console.error('Error fetching profile:', error);
      return {
        success: false,
        data: null,
        message: 'Failed to fetch profile'
      };
    }
  },

  /**
   * Admin logout (if endpoint exists)
   */
  async logout(): Promise<ApiResponse<void>> {
    return httpClient.post<void>('/api/auth/admin/logout');
  },
};

/**
 * Provider Management API Services
 */
export const providersApi = {
  /**
   * Get all providers with pagination and filters
   * Note: This endpoint returns raw data, not wrapped in ApiResponse structure
   */
  async getProviders(params?: ProvidersListRequest): Promise<ApiResponse<Provider[]>> {
    try {
      const queryParams = new URLSearchParams();

      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.verified !== undefined) queryParams.append('verified', params.verified.toString());
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const url = `/api/auth/admin/providers${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      // Use getRaw since this endpoint returns raw data
      const rawData = await httpClient.getRaw(url);

      // Transform the raw response to match our expected structure
      const rawProviders: RawProvider[] = rawData.providers || [];
      const providers = rawProviders.map(transformRawProvider);

      const pagination = rawData.pagination ? {
        page: rawData.pagination.page,
        limit: rawData.pagination.limit,
        total: rawData.pagination.totalCount,
        totalPages: rawData.pagination.totalPages,
      } : undefined;

      // Wrap in ApiResponse structure
      return {
        success: true,
        data: providers,
        message: 'Providers fetched successfully',
        pagination,
      };
    } catch (error) {
      console.error('Error fetching providers:', error);
      return {
        success: false,
        data: [],
        message: 'Failed to fetch providers',
      };
    }
  },

  /**
   * Get provider by ID
   * Note: This endpoint returns raw data, not wrapped in ApiResponse structure
   */
  async getProvider(id: string): Promise<ApiResponse<Provider>> {
    try {
      // Use getRaw since this endpoint returns raw data
      const rawData = await httpClient.getRaw(`/api/auth/admin/providers/${id}`);

      // Check if the response has the expected structure
      if (rawData.success && rawData.data) {
        // Transform the raw provider data to normalized Provider interface
        const rawProvider: RawProvider = rawData.data;
        const provider = transformRawProvider(rawProvider);

        // Wrap in ApiResponse structure
        return {
          success: true,
          data: provider,
          message: rawData.message || 'Provider fetched successfully'
        };
      } else {
        throw new Error('Invalid response structure');
      }
    } catch (error) {
      console.error('Error fetching provider:', error);
      return {
        success: false,
        data: null as any,
        message: 'Failed to fetch provider'
      };
    }
  },

  /**
   * Update provider verification status using the correct API endpoint
   */
  async updateProviderStatus(id: string, data: { isVerified: boolean; reason?: string }): Promise<ApiResponse<Provider>> {
    const result = await httpClient.put<Provider>(`/api/auth/admin/providers/${id}/status`, data);

    // Invalidate related cache entries
    cacheUtils.invalidateRelated(CACHE_KEYS.PROVIDERS, id);

    return result;
  },

  /**
   * Approve provider
   */
  async approveProvider(id: string, data: { reason?: string }): Promise<ApiResponse<Provider>> {
    return this.updateProviderStatus(id, { isVerified: true, reason: data.reason });
  },

  /**
   * Reject provider
   */
  async rejectProvider(id: string, data: { reason?: string }): Promise<ApiResponse<Provider>> {
    return this.updateProviderStatus(id, { isVerified: false, reason: data.reason });
  },

  /**
   * Request more information from provider (sets to pending status)
   */
  async requestProviderInfo(id: string, data: { reason?: string; requiresDocuments?: boolean; documentTypes?: string[] }): Promise<ApiResponse<Provider>> {
    // For requesting info, we keep them unverified but could add additional logic here
    return this.updateProviderStatus(id, { isVerified: false, reason: data.reason });
  },

  /**
   * Get provider analytics
   */
  async getProviderAnalytics(id: string): Promise<ApiResponse<any>> {
    return httpClient.get<any>(`/api/auth/admin/providers/${id}/analytics`);
  },

  /**
   * Get provider statistics
   */
  async getProviderStats(id: string): Promise<ApiResponse<ProviderStats>> {
    try {
      // Use getRaw since this endpoint returns raw data
      const rawData = await httpClient.getRaw(`/api/auth/admin/providers/${id}/stats`);

      if (rawData.success && rawData.data) {
        return {
          success: true,
          data: rawData.data,
          message: rawData.message || 'Provider statistics retrieved successfully'
        };
      } else {
        throw new Error('Invalid response structure');
      }
    } catch (error) {
      console.error('Error fetching provider stats:', error);
      return {
        success: false,
        data: null as any,
        message: 'Failed to fetch provider statistics'
      };
    }
  },

  /**
   * Get provider services
   */
  async getProviderServices(id: string): Promise<ApiResponse<ProviderServiceDetail[]>> {
    try {
      // Use getRaw since this endpoint returns raw data
      const rawData = await httpClient.getRaw(`/api/auth/admin/providers/${id}/services`);

      if (rawData.success && rawData.data) {
        return {
          success: true,
          data: rawData.data,
          message: rawData.message || 'Provider services retrieved successfully'
        };
      } else {
        throw new Error('Invalid response structure');
      }
    } catch (error) {
      console.error('Error fetching provider services:', error);
      return {
        success: false,
        data: [],
        message: 'Failed to fetch provider services'
      };
    }
  },

  /**
   * Get provider locations
   */
  async getProviderLocations(id: string): Promise<ApiResponse<ProviderLocationDetail[]>> {
    try {
      // Use getRaw since this endpoint returns raw data
      const rawData = await httpClient.getRaw(`/api/auth/admin/providers/${id}/locations`);

      if (rawData.success && rawData.data) {
        return {
          success: true,
          data: rawData.data,
          message: rawData.message || 'Provider locations retrieved successfully'
        };
      } else {
        throw new Error('Invalid response structure');
      }
    } catch (error) {
      console.error('Error fetching provider locations:', error);
      return {
        success: false,
        data: [],
        message: 'Failed to fetch provider locations'
      };
    }
  },

  /**
   * Bulk approve providers
   */
  async bulkApproveProviders(providerIds: string[], approval: ProviderApprovalRequest): Promise<ApiResponse<any>> {
    return httpClient.post<any>('/api/auth/admin/providers/bulk-approve', {
      providerIds,
      ...approval,
    });
  },
};

/**
 * Customer Management API Services
 */
export const customersApi = {
  /**
   * Get all customers with pagination and filters
   * Note: This endpoint returns raw data, not wrapped in ApiResponse structure
   */
  async getCustomers(params?: CustomersListRequest): Promise<ApiResponse<Customer[]>> {
    try {
      const queryParams = new URLSearchParams();

      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.verified !== undefined) queryParams.append('verified', params.verified.toString());
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const url = `/api/auth/admin/customers${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      // Use getRaw since this endpoint returns raw data
      const rawData = await httpClient.getRaw(url);

      // Transform the raw response to match our expected structure
      const rawCustomers: RawCustomer[] = rawData.customers || [];
      const customers = rawCustomers.map(transformRawCustomer);

      const pagination = rawData.pagination ? {
        page: rawData.pagination.page,
        limit: rawData.pagination.limit,
        total: rawData.pagination.totalCount,
        totalPages: rawData.pagination.totalPages,
      } : undefined;

      // Wrap in ApiResponse structure
      return {
        success: true,
        data: customers,
        message: 'Customers fetched successfully',
        pagination,
      };
    } catch (error) {
      console.error('Error fetching customers:', error);
      return {
        success: false,
        data: [],
        message: 'Failed to fetch customers',
      };
    }
  },

  /**
   * Get customer by ID
   */
  async getCustomer(id: string): Promise<ApiResponse<Customer>> {
    return httpClient.get<Customer>(`/api/auth/admin/customers/${id}`);
  },

  /**
   * Update customer status
   */
  async updateCustomerStatus(id: string, status: string): Promise<ApiResponse<Customer>> {
    return httpClient.patch<Customer>(`/api/auth/admin/customers/${id}/status`, { status });
  },

  /**
   * Update customer credits
   */
  async updateCustomerCredits(id: string, credits: number): Promise<ApiResponse<Customer>> {
    return httpClient.patch<Customer>(`/api/auth/admin/customers/${id}/credits`, { credits });
  },

  /**
   * Get customer booking history
   */
  async getCustomerBookings(id: string, page?: number, limit?: number): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    if (page) queryParams.append('page', page.toString());
    if (limit) queryParams.append('limit', limit.toString());

    const url = `/api/auth/admin/customers/${id}/bookings${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return httpClient.get<any[]>(url);
  },

  /**
   * Get customer analytics
   */
  async getCustomerAnalytics(id: string): Promise<ApiResponse<any>> {
    return httpClient.get<any>(`/api/auth/admin/customers/${id}/analytics`);
  },
};

/**
 * Provider Category Management API Services
 */
export const categoriesApi = {
  /**
   * Get all provider categories
   */
  async getCategories(): Promise<ApiResponse<ProviderCategory[]>> {
    // Note: Authorization header is automatically added by httpClient interceptor
    return httpClient.get<ProviderCategory[]>('/api/auth/admin/provider-categories', {
      cache: true,
      cacheKey: CACHE_KEYS.CATEGORIES,
    });
  },

  /**
   * Get category by ID
   */
  async getCategory(id: string): Promise<ApiResponse<ProviderCategory>> {
    return httpClient.get<ProviderCategory>(`/api/auth/admin/provider-categories/${id}`);
  },

  /**
   * Create new category
   */
  async createCategory(category: CreateCategoryRequest): Promise<ApiResponse<ProviderCategory>> {
    const result = await httpClient.post<ProviderCategory>('/api/auth/admin/provider-categories', category);

    // Invalidate categories cache after creation
    cacheManager.invalidate([CACHE_KEYS.CATEGORIES]);

    return result;
  },

  /**
   * Update existing category
   */
  async updateCategory(id: string, category: UpdateCategoryRequest): Promise<ApiResponse<ProviderCategory>> {
    const result = await httpClient.put<ProviderCategory>(`/api/auth/admin/provider-categories/${id}`, category);

    // Invalidate categories cache after update
    cacheManager.invalidate([CACHE_KEYS.CATEGORIES]);

    return result;
  },

  /**
   * Delete category
   */
  async deleteCategory(id: string, options?: {
    reassignProviders?: boolean;
    targetCategoryId?: string;
    handleChildCategories?: 'delete' | 'orphan' | 'reassign';
    childTargetCategoryId?: string;
  }): Promise<ApiResponse<void>> {
    const result = await httpClient.delete<void>(`/api/auth/admin/provider-categories/${id}`, {
      data: options,
    });

    // Invalidate categories cache after deletion
    cacheManager.invalidate([CACHE_KEYS.CATEGORIES]);

    return result;
  },

  /**
   * Get category deletion impact analysis
   */
  async getDeletionImpact(id: string): Promise<ApiResponse<{
    affectedProviders: number;
    childCategories: ProviderCategory[];
    dependentCategories: ProviderCategory[];
    totalBookings: number;
    activeServices: number;
    revenueImpact: number;
  }>> {
    return httpClient.get<any>(`/api/auth/admin/provider-categories/${id}/deletion-impact`);
  },

  /**
   * Get category analytics
   */
  async getAnalytics(period: string = '30d'): Promise<ApiResponse<any>> {
    return httpClient.get<any>(`/api/auth/admin/provider-categories/analytics?period=${period}`);
  },

  /**
   * Reorder categories
   */
  async reorderCategories(reorderData: { id: string; sortOrder: number }[]): Promise<ApiResponse<void>> {
    const result = await httpClient.post<void>('/api/auth/admin/provider-categories/reorder', { categories: reorderData });

    // Invalidate categories cache after reordering
    cacheManager.invalidate([CACHE_KEYS.CATEGORIES]);

    return result;
  },

  /**
   * Upload category image - Request upload URL
   */
  async uploadCategoryImage(categoryId: string, imageData: ImageUploadRequest): Promise<ApiResponse<CategoryImageUploadResponse>> {
    return httpClient.post<CategoryImageUploadResponse>(`/api/auth/admin/provider-categories/${categoryId}/image`, imageData);
  },

  /**
   * Get category image
   */
  async getCategoryImage(categoryId: string): Promise<ApiResponse<{
    category: ProviderCategory;
    image: FileUploadResponse;
  }>> {
    return httpClient.get<any>(`/api/auth/admin/provider-categories/${categoryId}/image`);
  },

  /**
   * Remove category image
   */
  async removeCategoryImage(categoryId: string): Promise<ApiResponse<ProviderCategory>> {
    const result = await httpClient.delete<ProviderCategory>(`/api/auth/admin/provider-categories/${categoryId}/image`);

    // Invalidate categories cache after image removal
    cacheManager.invalidate([CACHE_KEYS.CATEGORIES]);

    return result;
  },

  /**
   * Upload file directly to S3 using pre-signed URL
   */
  async uploadFileToS3(uploadUrl: string, uploadFields: Record<string, string>, file: File): Promise<void> {
    const formData = new FormData();

    // Add all the upload fields first
    Object.entries(uploadFields).forEach(([key, value]) => {
      formData.append(key, value);
    });

    // Add the file last
    formData.append('file', file);

    // Upload directly to S3
    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }
  },
};

/**
 * Audit Log Management API Services
 */
export const auditLogsApi = {
  /**
   * Get audit logs with filtering and pagination
   */
  async getAuditLogs(request: AuditLogsRequest): Promise<ApiResponse<{
    logs: AuditLog[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>> {
    const params = new URLSearchParams();

    if (request.page) params.append('page', request.page.toString());
    if (request.limit) params.append('limit', request.limit.toString());
    if (request.adminId) params.append('adminId', request.adminId);
    if (request.action) params.append('action', request.action);
    if (request.entityType) params.append('entityType', request.entityType);
    if (request.entityId) params.append('entityId', request.entityId);
    if (request.dateFrom) params.append('dateFrom', request.dateFrom);
    if (request.dateTo) params.append('dateTo', request.dateTo);
    if (request.search) params.append('search', request.search);

    return httpClient.get<any>(`/api/auth/admin/audit-logs?${params.toString()}`);
  },

  /**
   * Get audit log by ID
   */
  async getAuditLog(id: string): Promise<ApiResponse<AuditLog>> {
    return httpClient.get<AuditLog>(`/api/auth/admin/audit-logs/${id}`);
  },

  /**
   * Export audit logs as CSV
   */
  async exportAuditLogs(filters: {
    adminId?: string;
    action?: string;
    entityType?: 'provider' | 'customer' | 'category' | 'system';
    entityId?: string;
    dateFrom?: string;
    dateTo?: string;
    search?: string;
  }): Promise<ApiResponse<string>> {
    const params = new URLSearchParams();

    if (filters.adminId) params.append('adminId', filters.adminId);
    if (filters.action) params.append('action', filters.action);
    if (filters.entityType) params.append('entityType', filters.entityType);
    if (filters.entityId) params.append('entityId', filters.entityId);
    if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters.dateTo) params.append('dateTo', filters.dateTo);
    if (filters.search) params.append('search', filters.search);

    return httpClient.get<string>(`/api/auth/admin/audit-logs/export?${params.toString()}`);
  },

  /**
   * Create audit log entry (for manual logging)
   */
  async createAuditLog(logData: {
    action: string;
    entityType: 'provider' | 'customer' | 'category' | 'system';
    entityId?: string;
    details: Record<string, any>;
  }): Promise<ApiResponse<AuditLog>> {
    return httpClient.post<AuditLog>('/api/auth/admin/audit-logs', logData);
  },

  /**
   * Get audit log statistics
   */
  async getAuditLogStats(period: string = '30d'): Promise<ApiResponse<{
    totalLogs: number;
    logsByAction: { action: string; count: number }[];
    logsByEntityType: { entityType: string; count: number }[];
    logsByAdmin: { adminEmail: string; count: number }[];
    dailyActivity: { date: string; count: number }[];
  }>> {
    return httpClient.get<any>(`/api/auth/admin/audit-logs/stats?period=${period}`);
  },

  /**
   * Get category analytics
   */
  async getCategoryAnalytics(): Promise<ApiResponse<any>> {
    return httpClient.get<any>('/api/auth/admin/provider-categories/analytics');
  },
};

/**
 * Dashboard and Analytics API Services
 */
export const dashboardApi = {
  /**
   * Get dashboard metrics
   */
  async getMetrics(): Promise<ApiResponse<DashboardMetrics>> {
    try {
      return await httpClient.get<DashboardMetrics>('/api/auth/admin/dashboard/metrics');
    } catch (error) {
      // Return mock data if endpoint doesn't exist
      return {
        success: true,
        data: {
          totalProviders: 0,
          verifiedProviders: 0,
          pendingProviders: 0,
          totalCustomers: 0,
          verifiedCustomers: 0,
          totalBookings: 0,
          completedBookings: 0,
          totalRevenue: 0,
          growthMetrics: {
            providersGrowth: 0,
            customersGrowth: 0,
            bookingsGrowth: 0,
            revenueGrowth: 0,
          },
        },
        message: 'Dashboard metrics retrieved (mock data)'
      };
    }
  },

  /**
   * Get recent activities
   */
  async getRecentActivities(limit?: number): Promise<ApiResponse<RecentActivity[]>> {
    try {
      const url = `/api/auth/admin/dashboard/activities${limit ? `?limit=${limit}` : ''}`;
      return await httpClient.get<RecentActivity[]>(url);
    } catch (error) {
      // Return mock data if endpoint doesn't exist
      return {
        success: true,
        data: [],
        message: 'Recent activities retrieved (mock data)'
      };
    }
  },

  /**
   * Get analytics data
   */
  async getAnalytics(period?: string): Promise<ApiResponse<AnalyticsData>> {
    try {
      const url = `/api/auth/admin/dashboard/analytics${period ? `?period=${period}` : ''}`;
      return await httpClient.get<AnalyticsData>(url);
    } catch (error) {
      // Return mock data if endpoint doesn't exist
      return {
        success: true,
        data: {
          providerGrowth: [],
          customerGrowth: [],
          bookingTrends: [],
          revenueData: [],
          categoryDistribution: [],
        },
        message: 'Analytics data retrieved (mock data)'
      };
    }
  },

  /**
   * Get pending counts for quick actions
   */
  async getPendingCounts(): Promise<ApiResponse<{
    providers: number;
    customers: number;
    categories: number;
    reports: number;
  }>> {
    try {
      // Since this endpoint doesn't exist, return mock data for now
      return {
        success: true,
        data: {
          providers: 5,
          customers: 12,
          categories: 2,
          reports: 3,
        },
        message: 'Pending counts retrieved successfully (mock data)'
      };
    } catch (error) {
      return {
        success: false,
        data: {
          providers: 0,
          customers: 0,
          categories: 0,
          reports: 0,
        },
        message: 'Failed to fetch pending counts'
      };
    }
  },

  /**
   * Get system health status
   */
  async getSystemHealth(): Promise<ApiResponse<any>> {
    try {
      return await httpClient.get<any>('/api/auth/admin/system/health');
    } catch (error) {
      // Return mock data if endpoint doesn't exist
      return {
        success: true,
        data: {
          status: 'healthy',
          uptime: Date.now(),
          version: '1.0.0',
          environment: 'development',
          services: {
            database: 'healthy',
            redis: 'healthy',
            storage: 'healthy',
            email: 'healthy',
          },
          lastChecked: new Date().toISOString(),
        },
        message: 'System health retrieved (mock data)'
      };
    }
  },
};

/**
 * Audit Log API Services
 */
export const auditApi = {
  /**
   * Get audit logs
   */
  async getAuditLogs(params?: AuditLogsRequest): Promise<ApiResponse<AuditLog[]>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.adminId) queryParams.append('adminId', params.adminId);
    if (params?.action) queryParams.append('action', params.action);
    if (params?.entityType) queryParams.append('entityType', params.entityType);
    if (params?.entityId) queryParams.append('entityId', params.entityId);
    if (params?.dateFrom) queryParams.append('dateFrom', params.dateFrom);
    if (params?.dateTo) queryParams.append('dateTo', params.dateTo);
    if (params?.search) queryParams.append('search', params.search);

    const url = `/api/auth/admin/audit-logs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return httpClient.get<AuditLog[]>(url);
  },

  /**
   * Get audit log by ID
   */
  async getAuditLog(id: string): Promise<ApiResponse<AuditLog>> {
    return httpClient.get<AuditLog>(`/api/auth/admin/audit-logs/${id}`);
  },
};

/**
 * System Administration API Services
 */
export const systemApi = {
  /**
   * Get system settings
   */
  async getSettings(): Promise<ApiResponse<any>> {
    return httpClient.get<any>('/api/auth/admin/system/settings');
  },

  /**
   * Update system settings
   */
  async updateSettings(settings: any): Promise<ApiResponse<any>> {
    return httpClient.put<any>('/api/auth/admin/system/settings', settings);
  },

  /**
   * Get admin users
   */
  async getAdminUsers(): Promise<ApiResponse<any[]>> {
    return httpClient.get<any[]>('/api/auth/admin/system/admins');
  },

  /**
   * Get system statistics
   */
  async getSystemStats(): Promise<ApiResponse<any>> {
    return httpClient.get<any>('/api/auth/admin/system/stats');
  },

  /**
   * Backup system data
   */
  async createBackup(): Promise<ApiResponse<any>> {
    return httpClient.post<any>('/api/auth/admin/system/backup');
  },

  /**
   * Get backup status
   */
  async getBackupStatus(): Promise<ApiResponse<any>> {
    return httpClient.get<any>('/api/auth/admin/system/backup/status');
  },

  /**
   * Get system health status
   */
  async getSystemHealth(): Promise<ApiResponse<{
    status: 'healthy' | 'warning' | 'critical';
    uptime: number;
    lastChecked: string;
    services: Record<string, {
      status: 'online' | 'degraded' | 'offline';
      responseTime: number;
      lastChecked: string;
      errorRate: number;
      uptime: number;
    }>;
  }>> {
    return httpClient.get<any>('/api/auth/admin/system/health');
  },

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(): Promise<ApiResponse<{
    apiResponseTime: {
      average: number;
      p95: number;
      p99: number;
    };
    requestsPerMinute: number;
    errorRate: number;
    activeConnections: number;
    memoryUsage: number;
    cpuUsage: number;
    diskUsage: number;
  }>> {
    return httpClient.get<any>('/api/auth/admin/system/metrics');
  },

  /**
   * Get error summary
   */
  async getErrorSummary(): Promise<ApiResponse<{
    totalErrors: number;
    errorsByType: { type: string; count: number; percentage: number }[];
    recentErrors: {
      id: string;
      timestamp: string;
      type: string;
      message: string;
      endpoint: string;
      userId?: string;
    }[];
  }>> {
    return httpClient.get<any>('/api/auth/admin/system/errors');
  },

  /**
   * Get system logs
   */
  async getSystemLogs(params?: {
    level?: 'error' | 'warn' | 'info' | 'debug';
    service?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
  }): Promise<ApiResponse<{
    logs: {
      id: string;
      timestamp: string;
      level: string;
      service: string;
      message: string;
      metadata?: Record<string, any>;
    }[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>> {
    const queryParams = new URLSearchParams();
    if (params?.level) queryParams.append('level', params.level);
    if (params?.service) queryParams.append('service', params.service);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    return httpClient.get<any>(`/api/auth/admin/system/logs?${queryParams.toString()}`);
  },

  /**
   * Clear system cache
   */
  async clearCache(cacheType?: 'all' | 'redis' | 'application'): Promise<ApiResponse<void>> {
    return httpClient.post<void>('/api/auth/admin/system/cache/clear', { type: cacheType || 'all' });
  },

  /**
   * Restart system service
   */
  async restartService(serviceName: string): Promise<ApiResponse<void>> {
    return httpClient.post<void>(`/api/auth/admin/system/services/${serviceName}/restart`);
  },

  /**
   * Get backup schedules
   */
  async getBackupSchedules(): Promise<ApiResponse<any[]>> {
    return httpClient.get<any[]>('/api/auth/admin/system/backup/schedules');
  },

  /**
   * Update backup schedule
   */
  async updateBackupSchedule(scheduleId: string, data: any): Promise<ApiResponse<void>> {
    return httpClient.put<void>(`/api/auth/admin/system/backup/schedules/${scheduleId}`, data);
  },

  /**
   * Download backup
   */
  async downloadBackup(backupId: string): Promise<ApiResponse<Blob>> {
    return httpClient.get<Blob>(`/api/auth/admin/system/backup/${backupId}/download`, {
      responseType: 'blob',
    });
  },

  /**
   * Delete backup
   */
  async deleteBackup(backupId: string): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`/api/auth/admin/system/backup/${backupId}`);
  },

  /**
   * Get maintenance tasks
   */
  async getMaintenanceTasks(): Promise<ApiResponse<any[]>> {
    return httpClient.get<any[]>('/api/auth/admin/system/maintenance/tasks');
  },

  /**
   * Run maintenance task
   */
  async runMaintenanceTask(taskId: string): Promise<ApiResponse<{ duration: number }>> {
    return httpClient.post<{ duration: number }>(`/api/auth/admin/system/maintenance/tasks/${taskId}/run`);
  },

  /**
   * Get system status
   */
  async getSystemStatus(): Promise<ApiResponse<any>> {
    return httpClient.get<any>('/api/auth/admin/system/status');
  },

  /**
   * Set maintenance mode
   */
  async setMaintenanceMode(enabled: boolean): Promise<ApiResponse<void>> {
    return httpClient.post<void>('/api/auth/admin/system/maintenance-mode', { enabled });
  },

  /**
   * Get system alerts
   */
  async getSystemAlerts(): Promise<ApiResponse<any[]>> {
    return httpClient.get<any[]>('/api/auth/admin/system/alerts');
  },

  /**
   * Acknowledge alert
   */
  async acknowledgeAlert(alertId: string): Promise<ApiResponse<void>> {
    return httpClient.post<void>(`/api/auth/admin/system/alerts/${alertId}/acknowledge`);
  },

  /**
   * Resolve alert
   */
  async resolveAlert(alertId: string): Promise<ApiResponse<void>> {
    return httpClient.post<void>(`/api/auth/admin/system/alerts/${alertId}/resolve`);
  },
};

/**
 * Advertisement Management API Services
 */
export const advertisementsApi = {
  /**
   * Get all advertisements with pagination and filtering
   */
  async getAdvertisements(params?: AdvertisementsListRequest): Promise<ApiResponse<{ advertisements: Advertisement[]; pagination: PaginationInfo }>> {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.isActive !== undefined) queryParams.append('isActive', params.isActive.toString());
    if (params?.search) queryParams.append('search', params.search);

    const url = `/api/auth/admin/advertisements${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return httpClient.get<{ advertisements: Advertisement[]; pagination: PaginationInfo }>(url);
  },

  /**
   * Create new advertisement
   */
  async createAdvertisement(data: CreateAdvertisementRequest): Promise<ApiResponse<Advertisement>> {
    return httpClient.post<Advertisement>('/api/auth/admin/advertisements', data);
  },

  /**
   * Update existing advertisement
   */
  async updateAdvertisement(advertisementId: string, data: UpdateAdvertisementRequest): Promise<ApiResponse<Advertisement>> {
    return httpClient.put<Advertisement>(`/api/auth/admin/advertisements/${advertisementId}`, data);
  },

  /**
   * Delete advertisement
   */
  async deleteAdvertisement(advertisementId: string): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`/api/auth/admin/advertisements/${advertisementId}`);
  },

  /**
   * Upload advertisement background image - Request upload URL
   */
  async uploadBackgroundImage(advertisementId: string, imageData: ImageUploadRequest): Promise<ApiResponse<AdvertisementImageUploadResponse>> {
    return httpClient.post<AdvertisementImageUploadResponse>(`/api/auth/admin/advertisements/${advertisementId}/background-image`, imageData);
  },

  /**
   * Upload advertisement PNG image - Request upload URL
   */
  async uploadPngImage(advertisementId: string, imageData: ImageUploadRequest): Promise<ApiResponse<AdvertisementImageUploadResponse>> {
    return httpClient.post<AdvertisementImageUploadResponse>(`/api/auth/admin/advertisements/${advertisementId}/png-image`, imageData);
  },

  /**
   * Get advertisement background image
   */
  async getBackgroundImage(advertisementId: string): Promise<ApiResponse<FileUploadResponse>> {
    return httpClient.get<FileUploadResponse>(`/api/auth/admin/advertisements/${advertisementId}/background-image`);
  },

  /**
   * Get advertisement PNG image
   */
  async getPngImage(advertisementId: string): Promise<ApiResponse<FileUploadResponse>> {
    return httpClient.get<FileUploadResponse>(`/api/auth/admin/advertisements/${advertisementId}/png-image`);
  },

  /**
   * Remove advertisement background image
   */
  async removeBackgroundImage(advertisementId: string): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`/api/auth/admin/advertisements/${advertisementId}/background-image`);
  },

  /**
   * Remove advertisement PNG image
   */
  async removePngImage(advertisementId: string): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`/api/auth/admin/advertisements/${advertisementId}/png-image`);
  },

  /**
   * Upload file to S3 using pre-signed URL (reuse from categories)
   */
  async uploadFileToS3(uploadUrl: string, uploadFields: Record<string, string>, file: File): Promise<void> {
    return categoriesApi.uploadFileToS3(uploadUrl, uploadFields, file);
  },
};

// Export all APIs as a single object for convenience
export const adminApi = {
  auth: authApi,
  providers: providersApi,
  customers: customersApi,
  categories: categoriesApi,
  advertisements: advertisementsApi,
  dashboard: dashboardApi,
  audit: auditApi,
  auditLogs: auditLogsApi,
  system: systemApi,
};

export default adminApi;

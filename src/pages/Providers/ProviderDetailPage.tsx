import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Provider, ProviderStats, ProviderServiceDetail, ProviderLocationDetail } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import { formatDistanceToNow } from 'date-fns';
import toast from 'react-hot-toast';
import PageMeta from '../../components/common/PageMeta';

export default function ProviderDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [provider, setProvider] = useState<Provider | null>(null);
  const [stats, setStats] = useState<ProviderStats | null>(null);
  const [services, setServices] = useState<ProviderServiceDetail[]>([]);
  const [locations, setLocations] = useState<ProviderLocationDetail[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const loading = useLoading();

  useEffect(() => {
    if (id) {
      fetchProviderDetails();
    }
  }, [id]);

  const fetchProviderDetails = async () => {
    if (!id) return;

    try {
      loading.startLoading({ message: 'Loading provider details...' });

      const [providerResponse, statsResponse, servicesResponse, locationsResponse] = await Promise.all([
        adminApi.providers.getProvider(id),
        adminApi.providers.getProviderStats(id),
        adminApi.providers.getProviderServices(id),
        adminApi.providers.getProviderLocations(id),
      ]);

      if (providerResponse.success) {
        setProvider(providerResponse.data);
      }

      if (statsResponse.success) {
        setStats(statsResponse.data);
      } else {
        // Fallback stats for development matching API structure
        setStats({
          overview: {
            totalAppointments: 156,
            monthlyAppointments: 45,
            weeklyAppointments: 12,
            todayAppointments: 3,
            totalCustomers: 89,
            totalServices: 8,
            totalLocations: 2,
            totalQueues: 3,
            averageRating: 4.7,
            totalReviews: 89,
            isVerified: true,
            isSetupComplete: true,
            memberSince: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
          },
          appointments: {
            byStatus: {
              completed: 142,
              confirmed: 14,
            },
            recent: [],
          },
          revenue: {
            monthlyTotal: 12450,
            currency: 'DZD',
          },
        });
      }

      if (servicesResponse.success) {
        setServices(servicesResponse.data);
      } else {
        // Fallback services for development
        setServices([
          {
            id: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            title: 'General Consultation',
            color: '#3B82F6',
            sProviderId: parseInt(id),
            serviceCategoryId: null,
            duration: 30,
            minDuration: null,
            maxDuration: null,
            queue: null,
            acceptOnline: true,
            acceptNew: true,
            notificationOn: true,
            pointsRequirements: 1,
            price: 50,
            isPublic: true,
            deliveryType: null,
            servedRegions: null,
            description: 'Standard consultation service',
            category: null,
            queues: [],
            _count: {
              appointments: 25,
              queues: 1,
            },
          },
        ]);
      }

      if (locationsResponse.success) {
        setLocations(locationsResponse.data);
      } else {
        // Fallback locations for development
        setLocations([
          {
            id: 1,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            sProviderId: parseInt(id),
            name: 'Main Office',
            shortName: null,
            address: null,
            city: null,
            mobile: null,
            isMobileHidden: false,
            fax: null,
            floor: null,
            parking: true,
            elevator: true,
            handicapAccess: true,
            timezone: 'Africa/Algiers',
            detailedAddressId: 1,
            detailedAddress: {
              id: 1,
              address: '123 Main Street',
              city: 'Algiers',
              state: null,
              postalCode: '16000',
              country: 'Algeria',
              latitude: 36.7538,
              longitude: 3.0588,
              description: null,
              isPrimary: false,
            },
            openings: [],
            queues: [],
            _count: {
              appointments: 25,
              queues: 1,
            },
          },
        ]);
      }
    } catch (error) {
      handleError(error, { action: 'fetch_provider_details' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleApproveProvider = async () => {
    if (!provider) return;

    try {
      loading.startLoading({ message: 'Approving provider...' });
      const response = await adminApi.providers.approveProvider(provider.id, { reason: 'Quick approval from detail page' });

      if (response.success) {
        const updatedProvider = { ...provider, status: 'approved' as const, verified: true };
        setProvider(updatedProvider);
        toast.success('Provider approved successfully');
      } else {
        throw new Error(response.message || 'Failed to approve provider');
      }
    } catch (error) {
      handleError(error, { action: 'approve_provider' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleRejectProvider = async () => {
    if (!provider) return;

    try {
      loading.startLoading({ message: 'Rejecting provider...' });
      const response = await adminApi.providers.rejectProvider(provider.id, { reason: 'Rejected from detail page' });

      if (response.success) {
        const updatedProvider = { ...provider, status: 'rejected' as const, verified: false };
        setProvider(updatedProvider);
        toast.success('Provider rejected');
      } else {
        throw new Error(response.message || 'Failed to reject provider');
      }
    } catch (error) {
      handleError(error, { action: 'reject_provider' });
    } finally {
      loading.stopLoading();
    }
  };

  if (loading.isLoading) {
    return (
      <>
        <PageMeta
          title="Loading Provider | Dalti Admin Panel"
          description="Loading provider details..."
        />
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
              <div className="space-y-6">
                <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (!provider) {
    return (
      <>
        <PageMeta
          title="Provider Not Found | Dalti Admin Panel"
          description="The requested provider could not be found"
        />
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Provider Not Found</h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">The provider you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => navigate('/providers')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Back to Providers
          </button>
        </div>
      </>
    );
  }

  return (
    <>
      <PageMeta
        title={`${provider.name} | Provider Details | Dalti Admin Panel`}
        description={`View and manage details for ${provider.name} - ${provider.businessTitle}`}
      />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/providers')}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <span className="text-lg font-medium text-gray-700 dark:text-gray-300">
                {provider.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {provider.name}
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {provider.businessTitle} • Member since {formatDistanceToNow(new Date(provider.createdAt))} ago
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {provider.status === 'pending' && (
              <>
                <button
                  onClick={handleRejectProvider}
                  disabled={loading.isLoading}
                  className="px-4 py-2 text-sm font-medium text-red-700 bg-red-100 border border-red-300 rounded-lg hover:bg-red-200 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50"
                >
                  Reject
                </button>
                <button
                  onClick={handleApproveProvider}
                  disabled={loading.isLoading}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
                >
                  Approve
                </button>
              </>
            )}
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Overview', icon: '📊' },
              { id: 'services', name: 'Services', icon: '🛠️' },
              { id: 'locations', name: 'Locations', icon: '📍' },
              { id: 'reviews', name: 'Reviews', icon: '⭐' },
              { id: 'bookings', name: 'Bookings', icon: '📅' },
              { id: 'analytics', name: 'Analytics', icon: '📈' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'overview' && (
            <OverviewTab provider={provider} stats={stats} />
          )}
          {activeTab === 'services' && (
            <ServicesTab services={services} />
          )}
          {activeTab === 'locations' && (
            <LocationsTab locations={locations} />
          )}
          {activeTab === 'reviews' && (
            <ReviewsTab providerId={provider.id} />
          )}
          {activeTab === 'bookings' && (
            <BookingsTab providerId={provider.id} />
          )}
          {activeTab === 'analytics' && (
            <AnalyticsTab providerId={provider.id} stats={stats} />
          )}
        </div>
      </div>
    </>
  );
}

// Tab Components (simplified versions of the modal components)
function OverviewTab({ provider, stats }: { provider: Provider; stats: ProviderStats | null }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Basic Information */}
      <div className="lg:col-span-2 space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Provider Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Business Name</label>
              <p className="text-sm text-gray-900 dark:text-white">{provider.businessTitle}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
              <p className="text-sm text-gray-900 dark:text-white">{provider.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
              <p className="text-sm text-gray-900 dark:text-white">{provider.phone || 'Not provided'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Category</label>
              <p className="text-sm text-gray-900 dark:text-white">{provider.category?.name || 'Uncategorized'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                provider.status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                provider.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}>
                {provider.status.charAt(0).toUpperCase() + provider.status.slice(1)}
              </span>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Verified</label>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                provider.verified ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
              }`}>
                {provider.verified ? 'Verified' : 'Not Verified'}
              </span>
            </div>
          </div>
        </div>

        {/* Description */}
        {provider.description && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              About
            </h3>
            <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
              {provider.description}
            </p>
          </div>
        )}
      </div>

      {/* Statistics */}
      <div className="space-y-6">
        {stats && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Performance Overview
            </h3>
            <div className="grid grid-cols-1 gap-4">
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-2xl font-semibold text-blue-600 dark:text-blue-400">
                  {stats.overview.totalAppointments}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Total Appointments</p>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-2xl font-semibold text-green-600 dark:text-green-400">
                  {Math.round((stats.appointments.byStatus.completed / stats.overview.totalAppointments) * 100) || 0}%
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Completion Rate</p>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-2xl font-semibold text-yellow-600 dark:text-yellow-400">
                  {stats.overview.averageRating || 'N/A'}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Average Rating</p>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-2xl font-semibold text-purple-600 dark:text-purple-400">
                  {stats.revenue.monthlyTotal.toLocaleString()} {stats.revenue.currency}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Monthly Revenue</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function ServicesTab({ services }: { services: ProviderServiceDetail[] }) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Services ({services.length})
        </h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.map((service) => (
          <div key={service.id} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                {service.title}
              </h4>
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: service.color }}
              ></div>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">Duration:</span>
                <span className="text-gray-900 dark:text-white">{service.duration} min</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">Price:</span>
                <span className="text-gray-900 dark:text-white">
                  {service.price ? `${service.price} DZD` : 'Free'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">Online Booking:</span>
                <span className={`${service.acceptOnline ? 'text-green-600' : 'text-red-600'}`}>
                  {service.acceptOnline ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">Appointments:</span>
                <span className="text-gray-900 dark:text-white">{service._count.appointments}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function LocationsTab({ locations }: { locations: ProviderLocationDetail[] }) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Locations ({locations.length})
        </h3>
      </div>

      <div className="space-y-6">
        {locations.map((location) => (
          <div key={location.id} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {location.name}
            </h4>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h5 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Address</h5>
                <p className="text-sm text-gray-900 dark:text-white">
                  {location.detailedAddress.address}<br />
                  {location.detailedAddress.city}, {location.detailedAddress.postalCode}<br />
                  {location.detailedAddress.country}
                </p>

                <h5 className="text-sm font-medium text-gray-500 dark:text-gray-400 mt-4 mb-2">Facilities</h5>
                <div className="flex flex-wrap gap-2">
                  {location.parking && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      🅿️ Parking
                    </span>
                  )}
                  {location.elevator && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      🛗 Elevator
                    </span>
                  )}
                  {location.handicapAccess && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                      ♿ Accessible
                    </span>
                  )}
                </div>
              </div>

              <div>
                <h5 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Opening Hours</h5>
                <div className="space-y-1">
                  {location.openings.map((opening) => (
                    <div key={opening.id} className="flex justify-between text-sm">
                      <span className="text-gray-900 dark:text-white">{opening.dayOfWeek}</span>
                      <span className="text-gray-500 dark:text-gray-400">
                        {opening.hours.map(h => `${h.timeFrom} - ${h.timeTo}`).join(', ')}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Placeholder components for other tabs
function ReviewsTab({ providerId }: { providerId: string }) {
  return (
    <div className="text-center py-12">
      <p className="text-gray-500 dark:text-gray-400">Reviews functionality coming soon...</p>
    </div>
  );
}

function BookingsTab({ providerId }: { providerId: string }) {
  return (
    <div className="text-center py-12">
      <p className="text-gray-500 dark:text-gray-400">Bookings functionality coming soon...</p>
    </div>
  );
}

function AnalyticsTab({ providerId, stats }: { providerId: string; stats: ProviderStats | null }) {
  return (
    <div className="text-center py-12">
      <p className="text-gray-500 dark:text-gray-400">Analytics functionality coming soon...</p>
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { ProviderCategory } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import PageMeta from '../../components/common/PageMeta';
import CategoryTreeView from '../../components/categories/CategoryTreeView';
import toast from 'react-hot-toast';

export default function CategoriesPage() {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<ProviderCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<ProviderCategory | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const loading = useLoading();

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      loading.startLoading({ message: 'Loading categories...' });
      const response = await adminApi.categories.getCategories();
      
      if (response.success) {
        setCategories(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch categories');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_categories' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleCategorySelect = (category: ProviderCategory) => {
    setSelectedCategory(category);
    // Could open a detail view or perform other actions
    console.log('Selected category:', category);
  };

  const handleCategoryEdit = (category: ProviderCategory) => {
    navigate(`/categories/edit/${String(category.id)}`);
  };

  const handleCategoryDelete = (category: ProviderCategory) => {
    setSelectedCategory(category);
    setShowDeleteModal(true);
  };

  const handleCreateCategory = () => {
    navigate('/categories/create');
  };

  const confirmDelete = async () => {
    if (!selectedCategory) return;

    try {
      loading.startLoading({ message: 'Deleting category...' });
      const response = await adminApi.categories.deleteCategory(String(selectedCategory.id));
      
      if (response.success) {
        setCategories(prev => prev.filter(c => c.id !== selectedCategory.id));
        toast.success('Category deleted successfully');
        setShowDeleteModal(false);
        setSelectedCategory(null);
      } else {
        throw new Error(response.message || 'Failed to delete category');
      }
    } catch (error) {
      handleError(error, { action: 'delete_category' });
    } finally {
      loading.stopLoading();
    }
  };

  const getCategoryStats = () => {
    const totalCategories = categories.length;
    const activeCategories = categories.filter(c => c.isActive).length;
    const rootCategories = categories.filter(c => !c.parentId).length;
    const totalProviders = categories.reduce((sum, c) => sum + (c._count?.providers || c.providerCount || 0), 0);

    return { totalCategories, activeCategories, rootCategories, totalProviders };
  };

  const stats = getCategoryStats();

  return (
    <>
      <PageMeta
        title="Categories | Dalti Admin Panel"
        description="Manage provider service categories and hierarchical organization"
      />
      
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Category Management
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Organize and manage service categories for providers
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export
            </button>

            <button
              onClick={handleCreateCategory}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Category
            </button>
          </div>
        </div>

        {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                  <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Categories</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.totalCategories}</p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
                  <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.activeCategories}</p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                  <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Root Categories</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.rootCategories}</p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                  <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Providers</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.totalProviders}</p>
                </div>
              </div>
            </div>
          </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Category Tree View */}
          <div className="lg:col-span-2">
            <CategoryTreeView
              onCategorySelect={handleCategorySelect}
              onCategoryEdit={handleCategoryEdit}
              onCategoryDelete={handleCategoryDelete}
              selectedCategoryId={selectedCategory?.id ? String(selectedCategory.id) : undefined}
            />
          </div>

          {/* Category Details Sidebar */}
          <div className="lg:col-span-1">
              {selectedCategory ? (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Category Details
                  </h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <span className="text-2xl mr-3">
                        {selectedCategory.metadata?.icon || '📁'}
                      </span>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {selectedCategory.title}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {selectedCategory.description || 'No description'}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Status:</span>
                        <span className={`ml-2 ${selectedCategory.isActive ? 'text-green-600' : 'text-red-600'}`}>
                          {selectedCategory.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Providers:</span>
                        <span className="ml-2 text-gray-900 dark:text-white">
                          {selectedCategory._count?.providers || selectedCategory.providerCount || 0}
                        </span>
                      </div>
                    </div>

                    <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleCategoryEdit(selectedCategory)}
                          className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleCategoryDelete(selectedCategory)}
                          className="flex-1 px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <div className="text-center py-8">
                    <svg className="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    <p className="text-gray-500 dark:text-gray-400">
                      Select a category to view details
                    </p>
                  </div>
                </div>
              )}
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && selectedCategory && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full mx-4">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      Delete Category
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      This action cannot be undone.
                    </p>
                  </div>
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                  Are you sure you want to delete "{selectedCategory.title}"?
                  {(selectedCategory._count?.providers || selectedCategory.providerCount || 0) > 0 && (
                    <span className="text-red-600 dark:text-red-400">
                      {' '}This category has {selectedCategory._count?.providers || selectedCategory.providerCount} provider(s) associated with it.
                    </span>
                  )}
                </p>
                
                <div className="flex items-center justify-end space-x-3">
                  <button
                    onClick={() => setShowDeleteModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={confirmDelete}
                    disabled={loading.isLoading}
                    className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {loading.isLoading ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}

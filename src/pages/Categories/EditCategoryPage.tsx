import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router';
import { AngleLeftIcon, TrashBinIcon } from '../../icons';
import CategoryForm from '../../components/categories/CategoryForm';
import PageMeta from '../../components/common/PageMeta';
import { ProviderCategory } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import toast from 'react-hot-toast';

export default function EditCategoryPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [category, setCategory] = useState<ProviderCategory | null>(null);
  const [parentCategories, setParentCategories] = useState<ProviderCategory[]>([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const loading = useLoading();
  const deleteLoading = useLoading();

  useEffect(() => {
    if (id) {
      fetchCategory();
      fetchParentCategories();
    }
  }, [id]);

  const fetchCategory = async () => {
    if (!id) return;

    try {
      loading.startLoading({ message: 'Loading category...' });
      const response = await adminApi.categories.getCategory(id);
      
      if (response.success) {

        setCategory(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch category');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_category' });
      toast.error('Failed to load category');
      navigate('/categories');
    } finally {
      loading.stopLoading();
    }
  };

  const fetchParentCategories = async () => {
    try {
      const response = await adminApi.categories.getCategories();

      if (response.success) {
        // Filter out the current category and its children to prevent circular references
        // Also filter out inactive categories
        // Convert IDs to strings for comparison since API might return numbers
        // Filter out the current category and its children to prevent circular references
        // Also filter out inactive categories
        // Convert IDs to strings for comparison since API might return numbers
        const availableParents = response.data.filter(cat =>
          cat?.children?.length! > 0
        );

        setParentCategories(availableParents);
      } else {
        throw new Error(response.message || 'Failed to fetch categories');
      }
    } catch (error) {
      console.error('Failed to fetch parent categories:', error);
      handleError(error, { action: 'fetch_parent_categories' });
      // No fallback data - if API fails, show error to user
      setParentCategories([]);
    }
  };

  // Helper function to check if a category is a descendant of another
  const isDescendantOf = (categoryId: string, ancestorId: string): boolean => {
    // Simple check to prevent immediate circular references
    // In a more complex implementation, this would traverse the entire hierarchy
    const currentCategory = parentCategories.find(cat => String(cat.id) === String(categoryId));
    if (!currentCategory) return false;

    // Check if the category's parent is the ancestor we're checking against
    if (String(currentCategory.parentId) === String(ancestorId)) return true;

    // For now, we'll do a simple one-level check
    // This could be extended to check multiple levels if needed
    return false;
  };

  const handleSubmit = async (data: any) => {
    if (!id) return;

    try {
      const response = await adminApi.categories.updateCategory(id, data);
      
      if (response.success) {
        toast.success('Category updated successfully');
        navigate('/categories');
      } else {
        throw new Error(response.message || 'Failed to update category');
      }
    } catch (error) {
      handleError(error, { action: 'update_category' });
      throw error; // Re-throw to let the form handle the error state
    }
  };

  const handleDelete = async () => {
    if (!id || !category) return;

    try {
      deleteLoading.startLoading({ message: 'Deleting category...' });
      const response = await adminApi.categories.deleteCategory(id);
      
      if (response.success) {
        toast.success('Category deleted successfully');
        navigate('/categories');
      } else {
        throw new Error(response.message || 'Failed to delete category');
      }
    } catch (error) {
      handleError(error, { action: 'delete_category' });
    } finally {
      deleteLoading.stopLoading();
      setShowDeleteConfirm(false);
    }
  };

  const handleCancel = () => {
    navigate('/categories');
  };

  if (loading.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600 dark:text-gray-400">Loading category...</span>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Category Not Found
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          The category you're looking for doesn't exist or has been deleted.
        </p>
        <button
          onClick={handleCancel}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Back to Categories
        </button>
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title={`Edit ${category.title} | Dalti Admin Panel`}
        description={`Edit category: ${category.title}`}
      />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleCancel}
              className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors"
            >
              <AngleLeftIcon className="w-5 h-5 mr-2" />
              Back to Categories
            </button>
          </div>

          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="flex items-center px-4 py-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
          >
            <TrashBinIcon className="w-4 h-4 mr-2" />
            Delete Category
          </button>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Edit Category: {category.title}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Update category information and settings
            </p>
          </div>
        </div>

        {/* Form Container */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="p-6">
            <CategoryForm
              mode="edit"
              category={category}
              parentCategories={parentCategories}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
            />
          </div>
        </div>

        {/* Category Stats */}
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Category Statistics
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {category._count?.providers || category.providerCount || 0}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Providers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {category._count?.children || 0}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Subcategories</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {category.isActive ? 'Active' : 'Inactive'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Status</div>
            </div>
          </div>

          {/* Parent Category Info */}
          {category.parent && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Parent Category</h4>
              <div className="bg-white dark:bg-gray-700 rounded-lg p-3">
                <span className="text-sm text-gray-600 dark:text-gray-300">{category.parent.title}</span>
              </div>
            </div>
          )}

          {/* Providers in this category */}
          {category.providers && category.providers.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Providers in this Category ({category.providers.length})
              </h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {category.providers.map((provider) => (
                  <div key={provider.id} className="bg-white dark:bg-gray-700 rounded-lg p-3 flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {provider.title}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {provider.user.firstName} {provider.user.lastName}
                      </div>
                    </div>
                    <div className="flex items-center">
                      {provider.isVerified ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          Verified
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                          Pending
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mr-3">
                  <TrashBinIcon className="w-5 h-5 text-red-600 dark:text-red-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Delete Category
                </h3>
              </div>
              
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Are you sure you want to delete "{category.title}"? This action cannot be undone.
              </p>

              {/* Show affected providers */}
              {category.providers && category.providers.length > 0 && (
                <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div className="text-red-800 dark:text-red-200 font-medium mb-2">
                    ⚠️ Warning: This category has {category.providers.length} provider(s) associated with it:
                  </div>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {category.providers.map((provider) => (
                      <div key={provider.id} className="text-sm text-red-700 dark:text-red-300">
                        • {provider.title} ({provider.user.firstName} {provider.user.lastName})
                      </div>
                    ))}
                  </div>
                  <div className="text-xs text-red-600 dark:text-red-400 mt-2">
                    These providers will need to be reassigned to another category.
                  </div>
                </div>
              )}

              {/* Show child categories warning */}
              {(category._count?.children || 0) > 0 && (
                <div className="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <div className="text-yellow-800 dark:text-yellow-200 font-medium">
                    ⚠️ This category has {category._count?.children} subcategory(ies) that will also be affected.
                  </div>
                </div>
              )}

              <div className="flex items-center justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  disabled={deleteLoading.isLoading}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {deleteLoading.isLoading ? 'Deleting...' : 'Delete Category'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { AngleLeftIcon } from '../../icons';
import CategoryForm from '../../components/categories/CategoryForm';
import PageMeta from '../../components/common/PageMeta';
import { ProviderCategory } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import toast from 'react-hot-toast';

export default function CreateCategoryPage() {
  const navigate = useNavigate();
  const [parentCategories, setParentCategories] = useState<ProviderCategory[]>([]);
  const loading = useLoading();

  useEffect(() => {
    fetchParentCategories();
  }, []);

  const fetchParentCategories = async () => {
    try {
      loading.startLoading({ message: 'Loading categories...' });
      const response = await adminApi.categories.getCategories();

      if (response.success) {
        // Show all categories as potential parents for new categories
        // Filter out inactive categories if needed
        const availableCategories = response.data.filter(cat => cat.isActive !== false);
        setParentCategories(availableCategories);
      } else {
        throw new Error(response.message || 'Failed to fetch categories');
      }
    } catch (error) {
      console.error('Failed to fetch parent categories:', error);
      handleError(error, { action: 'fetch_parent_categories' });
      // No fallback data - if API fails, show error to user
      setParentCategories([]);
    } finally {
      loading.stopLoading();
    }
  };

  const handleSubmit = async (data: any) => {
    try {
      const response = await adminApi.categories.createCategory(data);
      
      if (response.success) {
        toast.success('Category created successfully');
        navigate('/categories');
      } else {
        throw new Error(response.message || 'Failed to create category');
      }
    } catch (error) {
      handleError(error, { action: 'create_category' });
      throw error; // Re-throw to let the form handle the error state
    }
  };

  const handleCancel = () => {
    navigate('/categories');
  };

  return (
    <>
      <PageMeta
        title="Create Category | Dalti Admin Panel"
        description="Create a new provider service category"
      />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleCancel}
              className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors"
            >
              <AngleLeftIcon className="w-5 h-5 mr-2" />
              Back to Categories
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Create New Category
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Add a new provider service category to organize your services
            </p>
          </div>
        </div>



        {/* Form Container */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="p-6">
            {!loading.isLoading ? (
              <CategoryForm
                mode="create"
                parentCategories={parentCategories}
                onSubmit={handleSubmit}
                onCancel={handleCancel}
              />
            ) : (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">Loading parent categories...</p>
              </div>
            )}
          </div>
        </div>

        {/* Help Section */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
            Category Creation Tips
          </h3>
          <ul className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
            <li className="flex items-start">
              <span className="text-blue-500 mr-2">•</span>
              Choose a clear, descriptive title that providers and customers will understand
            </li>
            <li className="flex items-start">
              <span className="text-blue-500 mr-2">•</span>
              Add a detailed description to help users understand what services belong in this category
            </li>
            <li className="flex items-start">
              <span className="text-blue-500 mr-2">•</span>
              Select a parent category if this is a subcategory, or leave empty for a root category
            </li>
            <li className="flex items-start">
              <span className="text-blue-500 mr-2">•</span>
              Upload an image to make the category more visually appealing (optional)
            </li>
            <li className="flex items-start">
              <span className="text-blue-500 mr-2">•</span>
              Use keywords and SEO settings to improve discoverability
            </li>
          </ul>
        </div>
      </div>
    </>
  );
}

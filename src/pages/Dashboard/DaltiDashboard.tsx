import React from 'react';
import { useAdminUser } from '../../context/AuthContext';
import PageMeta from '../../components/common/PageMeta';
import DashboardMetrics from '../../components/dashboard/DashboardMetrics';
import RecentActivityFeed, { ActivitySummary } from '../../components/dashboard/RecentActivityFeed';
import AnalyticsCharts from '../../components/dashboard/AnalyticsCharts';
import QuickActionsPanel from '../../components/dashboard/QuickActionsPanel';
import SystemHealthIndicators from '../../components/dashboard/SystemHealthIndicators';

export default function DaltiDashboard() {
  const admin = useAdminUser();

  return (
    <>
      <PageMeta
        title="Dashboard | Dalti Admin Panel"
        description="Dalti appointment booking platform admin dashboard"
      />
      
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome to Dalti Admin Panel
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Hello {admin?.name || 'Admin'}, manage your appointment booking platform from here.
          </p>
        </div>

        {/* Dashboard Metrics */}
        <DashboardMetrics />

        {/* Activity and Summary Row */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity Feed */}
          <div className="lg:col-span-2">
            <RecentActivityFeed />
          </div>

          {/* Activity Summary */}
          <div>
            <ActivitySummary />
          </div>
        </div>

        {/* Development Status */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Development Status
          </h2>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Authentication System</span>
              <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded">
                ✅ Complete
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">API Integration Layer</span>
              <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 rounded">
                🚧 In Progress
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Provider Management</span>
              <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded">
                ⏳ Pending
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Customer Management</span>
              <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded">
                ⏳ Pending
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Category Management</span>
              <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded">
                ⏳ Pending
              </span>
            </div>
          </div>
        </div>

        {/* Quick Actions Panel */}
        <QuickActionsPanel />

        {/* System Health Indicators */}
        <SystemHealthIndicators />

        {/* Analytics Charts */}
        <AnalyticsCharts />

        {/* Next Steps */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
            Next Development Steps
          </h3>
          <ul className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
            <li className="flex items-start">
              <span className="mr-2">1.</span>
              <span>Complete API Integration Layer with Dalti endpoints</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">2.</span>
              <span>Implement Dashboard Overview with real metrics</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">3.</span>
              <span>Build Provider Management interface</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">4.</span>
              <span>Create Customer Management system</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">5.</span>
              <span>Develop Category Management with CRUD operations</span>
            </li>
          </ul>
        </div>
      </div>
    </>
  );
}

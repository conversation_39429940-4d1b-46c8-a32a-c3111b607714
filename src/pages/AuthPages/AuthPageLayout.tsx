import React from "react";
import GridShape from "../../components/common/GridShape";
import { Link } from "react-router";
import ThemeTogglerTwo from "../../components/common/ThemeTogglerTwo";
import <PERSON><PERSON><PERSON>ogo from "../../components/branding/DaltiLogo";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="flex min-h-screen">
        {/* Left side - Login Form */}
        <div className="flex flex-col justify-center w-full px-6 py-12 lg:w-1/2 lg:px-8">
          <div className="mx-auto w-full max-w-md">
            {children}
          </div>
        </div>

        {/* Right side - Branding */}
        <div className="hidden lg:flex lg:w-1/2 lg:flex-col lg:justify-center lg:items-center relative overflow-hidden">
          {/* Background with Dalti brand colors */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#19727F] via-[#155b68] to-[#114451]"></div>

          {/* Grid pattern overlay */}
          <div className="absolute inset-0 opacity-10">
            <GridShape />
          </div>

          {/* Content */}
          <div className="relative z-10 flex flex-col items-center text-center max-w-md px-8">
            <div className="mb-8">
              <DaltiLogo size="xl" variant="full" className="text-white" showAdmin />
            </div>

            <h2 className="text-3xl font-bold text-white mb-4">
              Welcome to Dalti Admin
            </h2>

            <p className="text-lg text-white/80 mb-8">
              Comprehensive platform management for appointment booking services
            </p>

            <div className="grid grid-cols-1 gap-4 w-full max-w-sm">
              <div className="flex items-center space-x-3 text-white/90">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm">Provider Management</span>
              </div>
              <div className="flex items-center space-x-3 text-white/90">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm">Customer Analytics</span>
              </div>
              <div className="flex items-center space-x-3 text-white/90">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm">System Monitoring</span>
              </div>
              <div className="flex items-center space-x-3 text-white/90">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm">Security Controls</span>
              </div>
            </div>
          </div>
        </div>

        {/* Theme Toggle */}
        <div className="fixed z-50 bottom-6 right-6">
          <ThemeTogglerTwo />
        </div>
      </div>
    </div>
  );
}

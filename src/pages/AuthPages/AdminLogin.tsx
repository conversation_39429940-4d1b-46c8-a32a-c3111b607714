import { useState } from "react";
import { useNavigate } from "react-router";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import toast from "react-hot-toast";
import PageMeta from "../../components/common/PageMeta";
import AuthLayout from "./AuthPageLayout";
import { EyeIcon, EyeCloseIcon } from "../../icons";
import { AdminLoginRequest } from "../../types";
import { useAuth } from "../../context/AuthContext";
import { handleError } from "../../utils/errorHandler";
import <PERSON><PERSON><PERSON>ogo from "../../components/branding/DaltiLogo";

// Validation schema for admin login
const adminLoginSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .refine((email) => email === "<EMAIL>", {
      message: "Only <EMAIL> is allowed to access this panel",
    }),
  password: z
    .string()
    .min(1, "Password is required")
    .min(6, "Password must be at least 6 characters"),
});

type AdminLoginForm = z.infer<typeof adminLoginSchema>;

export default function AdminLogin() {
  const navigate = useNavigate();
  const { login } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<AdminLoginForm>({
    resolver: zodResolver(adminLoginSchema),
    defaultValues: {
      email: import.meta.env.VITE_ADMIN_EMAIL || "",
      password: "",
    },
  });

  const onSubmit = async (data: AdminLoginForm) => {
    setIsLoading(true);

    try {
      await login(data.email, data.password);
      navigate('/', { replace: true });
    } catch (error) {
      const enhancedError = handleError(error, {
        action: 'admin_login',
        email: data.email
      });

      // Set form-specific errors based on error category
      if (enhancedError.category === 'authentication') {
        setError('password', { message: 'Invalid email or password' });
      } else if (enhancedError.category === 'authorization') {
        setError('email', { message: 'Admin access required' });
      } else if (enhancedError.category === 'validation') {
        // Validation errors are already handled by the form
        setError('root', { message: enhancedError.userMessage });
      }
      // Other errors are handled by the error handler (toast notifications)
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <PageMeta
        title="Admin Login | Dalti Admin Panel"
        description="Secure admin login for Dalti appointment booking platform management"
      />
      <AuthLayout>
        {/* Mobile Logo - Only shown on small screens */}
        <div className="mb-8 text-center lg:hidden">
          <DaltiLogo size="lg" variant="full" showAdmin className="mx-auto mb-4" />
        </div>

        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome Back
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Sign in to access the Dalti Admin Panel
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Admin Email
            </label>
            <input
              {...register("email")}
              type="email"
              id="email"
              autoComplete="email"
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#19727F] focus:border-[#19727F] dark:bg-gray-800 dark:border-gray-600 dark:text-white transition-colors ${
                errors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="<EMAIL>"
              disabled={isLoading}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.email.message}
              </p>
            )}
          </div>

          {/* Password Field */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Password
            </label>
            <div className="relative">
              <input
                {...register("password")}
                type={showPassword ? "text" : "password"}
                id="password"
                autoComplete="current-password"
                className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-[#19727F] focus:border-[#19727F] dark:bg-gray-800 dark:border-gray-600 dark:text-white transition-colors ${
                  errors.password ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="Enter your admin password"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-[#19727F] dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                disabled={isLoading}
              >
                {showPassword ? <EyeCloseIcon /> : <EyeIcon />}
              </button>
            </div>
            {errors.password && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.password.message}
              </p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className={`w-full py-3 px-4 rounded-lg font-medium text-white transition-all duration-200 ${
              isLoading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-[#19727F] hover:bg-[#155b68] focus:ring-2 focus:ring-[#19727F] focus:ring-offset-2 shadow-lg hover:shadow-xl'
            }`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Signing in...
              </div>
            ) : (
              'Sign In to Admin Panel'
            )}
          </button>
        </form>

        {/* Security Notice */}
        <div className="mt-8 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                Admin Access Only
              </h3>
              <p className="mt-1 text-sm text-amber-700 dark:text-amber-300">
                This panel is restricted to authorized administrators only. All access attempts are logged.
              </p>
            </div>
          </div>
        </div>
      </AuthLayout>
    </>
  );
}

import React, { useState, useEffect } from 'react';
import { Customer } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import PageMeta from '../../components/common/PageMeta';
import CustomerSearchSystem from '../../components/customers/CustomerSearchSystem';
import CustomerSupportTools from '../../components/customers/CustomerSupportTools';

interface SupportTicket {
  id: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  subject: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  category: string;
  createdAt: string;
  updatedAt: string;
  assignedTo?: string;
  lastResponse?: string;
}

export default function CustomerSupportPage() {
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [supportTickets, setSupportTickets] = useState<SupportTicket[]>([]);
  const [activeView, setActiveView] = useState<'search' | 'tickets' | 'tools'>('search');
  const loading = useLoading();

  useEffect(() => {
    fetchSupportTickets();
  }, []);

  const fetchSupportTickets = async () => {
    try {
      loading.startLoading({ message: 'Loading support tickets...' });
      const response = await adminApi.support.getTickets();
      
      if (response.success) {
        setSupportTickets(response.data);
      } else {
        // Fallback data for development
        setSupportTickets([
          {
            id: '1',
            customerId: '1',
            customerName: 'John Doe',
            customerEmail: '<EMAIL>',
            subject: 'Unable to book appointment',
            priority: 'high',
            status: 'open',
            category: 'Booking Issues',
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T10:00:00Z',
            assignedTo: 'Support Agent 1',
          },
          {
            id: '2',
            customerId: '2',
            customerName: 'Jane Smith',
            customerEmail: '<EMAIL>',
            subject: 'Credit refund request',
            priority: 'medium',
            status: 'in_progress',
            category: 'Billing',
            createdAt: '2024-01-14T15:30:00Z',
            updatedAt: '2024-01-15T09:20:00Z',
            assignedTo: 'Support Agent 2',
            lastResponse: '2024-01-15T09:20:00Z',
          },
          {
            id: '3',
            customerId: '3',
            customerName: 'Ahmed Hassan',
            customerEmail: '<EMAIL>',
            subject: 'Account verification issue',
            priority: 'urgent',
            status: 'resolved',
            category: 'Account',
            createdAt: '2024-01-13T11:15:00Z',
            updatedAt: '2024-01-14T16:45:00Z',
            assignedTo: 'Support Agent 1',
            lastResponse: '2024-01-14T16:45:00Z',
          },
        ]);
      }
    } catch (error) {
      handleError(error, { action: 'fetch_support_tickets' });
    } finally {
      loading.stopLoading();
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Urgent
          </span>
        );
      case 'high':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
            High
          </span>
        );
      case 'medium':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            Medium
          </span>
        );
      case 'low':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Low
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            {priority}
          </span>
        );
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            Open
          </span>
        );
      case 'in_progress':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            In Progress
          </span>
        );
      case 'resolved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Resolved
          </span>
        );
      case 'closed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            Closed
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            {status}
          </span>
        );
    }
  };

  return (
    <>
      <PageMeta
        title="Customer Support | Dalti Admin Panel"
        description="Customer support tools for managing tickets, communications, and account assistance"
      />
      
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Customer Support
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Manage customer support tickets, communications, and account assistance
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setActiveView('search')}
              className={`px-4 py-2 rounded-lg text-sm font-medium ${
                activeView === 'search'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              Customer Search
            </button>
            <button
              onClick={() => setActiveView('tickets')}
              className={`px-4 py-2 rounded-lg text-sm font-medium ${
                activeView === 'tickets'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              Support Tickets
            </button>
            {selectedCustomer && (
              <button
                onClick={() => setActiveView('tools')}
                className={`px-4 py-2 rounded-lg text-sm font-medium ${
                  activeView === 'tools'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                Support Tools
              </button>
            )}
          </div>
        </div>

        {/* Support Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Open Tickets</p>
                <p className="text-2xl font-semibold text-blue-900 dark:text-blue-100">
                  {supportTickets.filter(t => t.status === 'open').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">In Progress</p>
                <p className="text-2xl font-semibold text-yellow-900 dark:text-yellow-100">
                  {supportTickets.filter(t => t.status === 'in_progress').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
                <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Resolved Today</p>
                <p className="text-2xl font-semibold text-green-900 dark:text-green-100">
                  {supportTickets.filter(t => t.status === 'resolved').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Urgent Tickets</p>
                <p className="text-2xl font-semibold text-purple-900 dark:text-purple-100">
                  {supportTickets.filter(t => t.priority === 'urgent').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        {activeView === 'search' && (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Find Customer for Support
              </h3>
              <CustomerSearchSystem
                onCustomerSelect={(customer) => {
                  setSelectedCustomer(customer);
                  setActiveView('tools');
                }}
                onFiltersChange={() => {}}
              />
            </div>
            
            {selectedCustomer && (
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-blue-900 dark:text-blue-100">
                      Selected Customer: {selectedCustomer.firstName} {selectedCustomer.lastName} ({selectedCustomer.email})
                    </p>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Credits: {selectedCustomer.credits} • Status: {selectedCustomer.verified ? 'Verified' : 'Unverified'}
                    </p>
                  </div>
                  <button
                    onClick={() => setActiveView('tools')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Open Support Tools
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {activeView === 'tickets' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Support Tickets
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Subject
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Assigned To
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Created
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {supportTickets.map((ticket) => (
                    <tr key={ticket.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {ticket.customerName}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {ticket.customerEmail}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {ticket.subject}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {ticket.category}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getPriorityBadge(ticket.priority)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(ticket.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {ticket.assignedTo || 'Unassigned'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {new Date(ticket.createdAt).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeView === 'tools' && selectedCustomer && (
          <CustomerSupportTools
            customer={selectedCustomer}
            onCustomerUpdate={setSelectedCustomer}
          />
        )}
      </div>
    </>
  );
}

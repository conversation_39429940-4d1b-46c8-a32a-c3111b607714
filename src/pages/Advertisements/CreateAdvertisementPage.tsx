import React from 'react';
import { useNavigate } from 'react-router';
import { Advertisement } from '../../types';
import PageMeta from '../../components/common/PageMeta';
import AdvertisementForm from '../../components/advertisements/AdvertisementForm';

export default function CreateAdvertisementPage() {
  const navigate = useNavigate();

  const handleSubmit = (advertisement: Advertisement) => {
    // Navigate to the advertisements list or detail page
    navigate('/advertisements');
  };

  const handleCancel = () => {
    navigate('/advertisements');
  };

  return (
    <>
      <PageMeta
        title="Create Advertisement | Dalti Admin Panel"
        description="Create a new advertisement for the platform"
      />
      
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <div>
                    <button
                      onClick={() => navigate('/advertisements')}
                      className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                    >
                      <svg className="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <span className="sr-only">Back</span>
                    </button>
                  </div>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <button
                      onClick={() => navigate('/advertisements')}
                      className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    >
                      Advertisements
                    </button>
                  </div>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      Create
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            
            <div className="mt-2">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Create Advertisement
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                Create a new promotional advertisement for the platform
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <AdvertisementForm
          mode="create"
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      </div>
    </>
  );
}

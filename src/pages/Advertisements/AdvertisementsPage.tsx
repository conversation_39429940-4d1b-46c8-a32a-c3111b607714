import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { Advertisement } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import PageMeta from '../../components/common/PageMeta';
import AdvertisementTable from '../../components/advertisements/AdvertisementTable';
import toast from 'react-hot-toast';

export default function AdvertisementsPage() {
  const navigate = useNavigate();
  const [advertisements, setAdvertisements] = useState<Advertisement[]>([]);
  const [selectedAdvertisement, setSelectedAdvertisement] = useState<Advertisement | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });
  const [filters, setFilters] = useState({
    search: '',
    isActive: undefined as boolean | undefined,
  });
  const loading = useLoading();

  useEffect(() => {
    fetchAdvertisements();
  }, [pagination.page, pagination.limit, filters]);

  const fetchAdvertisements = async () => {
    try {
      loading.startLoading({ message: 'Loading advertisements...' });
      const response = await adminApi.advertisements.getAdvertisements({
        page: pagination.page,
        limit: pagination.limit,
        search: filters.search || undefined,
        isActive: filters.isActive,
      });
      
      if (response.success) {
        setAdvertisements(response.data.advertisements);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total,
          totalPages: response.data.pagination.totalPages,
        }));
      } else {
        throw new Error(response.message || 'Failed to fetch advertisements');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_advertisements' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleAdvertisementSelect = (advertisement: Advertisement) => {
    navigate(`/advertisements/${advertisement.id}`);
  };

  const handleAdvertisementEdit = (advertisement: Advertisement) => {
    navigate(`/advertisements/edit/${advertisement.id}`);
  };

  const handleAdvertisementDelete = (advertisement: Advertisement) => {
    setSelectedAdvertisement(advertisement);
    setShowDeleteModal(true);
  };

  const handleCreateAdvertisement = () => {
    navigate('/advertisements/create');
  };

  const handleToggleStatus = async (advertisement: Advertisement) => {
    try {
      loading.startLoading({ message: 'Updating advertisement status...' });
      const response = await adminApi.advertisements.updateAdvertisement(
        String(advertisement.id),
        { isActive: !advertisement.isActive }
      );
      
      if (response.success) {
        setAdvertisements(prev => 
          prev.map(ad => 
            ad.id === advertisement.id 
              ? { ...ad, isActive: !ad.isActive }
              : ad
          )
        );
        toast.success(`Advertisement ${!advertisement.isActive ? 'activated' : 'deactivated'} successfully`);
      } else {
        throw new Error(response.message || 'Failed to update advertisement status');
      }
    } catch (error) {
      handleError(error, { action: 'toggle_advertisement_status' });
    } finally {
      loading.stopLoading();
    }
  };

  const confirmDelete = async () => {
    if (!selectedAdvertisement) return;

    try {
      loading.startLoading({ message: 'Deleting advertisement...' });
      const response = await adminApi.advertisements.deleteAdvertisement(String(selectedAdvertisement.id));
      
      if (response.success) {
        setAdvertisements(prev => prev.filter(ad => ad.id !== selectedAdvertisement.id));
        toast.success('Advertisement deleted successfully');
        setShowDeleteModal(false);
        setSelectedAdvertisement(null);
      } else {
        throw new Error(response.message || 'Failed to delete advertisement');
      }
    } catch (error) {
      handleError(error, { action: 'delete_advertisement' });
    } finally {
      loading.stopLoading();
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const getAdvertisementStats = () => {
    const totalAdvertisements = advertisements.length;
    const activeAdvertisements = advertisements.filter(ad => ad.isActive).length;
    const inactiveAdvertisements = advertisements.filter(ad => !ad.isActive).length;
    const externalLinks = advertisements.filter(ad => ad.isExternal).length;

    return { totalAdvertisements, activeAdvertisements, inactiveAdvertisements, externalLinks };
  };

  const stats = getAdvertisementStats();

  return (
    <>
      <PageMeta
        title="Advertisements | Dalti Admin Panel"
        description="Manage platform advertisements and promotional content"
      />
      
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Advertisement Management
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Create and manage promotional advertisements for the platform
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export
            </button>

            <button
              onClick={handleCreateAdvertisement}
              className="inline-flex items-center px-4 py-2 bg-brand-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create Advertisement
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-brand-100 dark:bg-brand-900">
                <svg className="w-6 h-6 text-brand-600 dark:text-brand-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Ads</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{pagination.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
                <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.activeAdvertisements}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-700">
                <svg className="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Inactive</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.inactiveAdvertisements}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">External Links</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.externalLinks}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Advertisement Table */}
        <AdvertisementTable
          advertisements={advertisements}
          loading={loading.isLoading}
          pagination={{
            current: pagination.page,
            pageSize: pagination.limit,
            total: pagination.total,
            onChange: handlePageChange,
          }}
          onAdvertisementSelect={handleAdvertisementSelect}
          onAdvertisementEdit={handleAdvertisementEdit}
          onAdvertisementDelete={handleAdvertisementDelete}
          onToggleStatus={handleToggleStatus}
          onSearch={handleSearch}
          onFilterChange={handleFilterChange}
          filters={filters}
        />

        {/* Delete Confirmation Modal */}
        {showDeleteModal && selectedAdvertisement && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full mx-4">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      Delete Advertisement
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      This action cannot be undone.
                    </p>
                  </div>
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                  Are you sure you want to delete "{selectedAdvertisement.title}"?
                  This will permanently remove the advertisement and all associated data.
                </p>

                <div className="flex items-center justify-end space-x-3">
                  <button
                    onClick={() => setShowDeleteModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={confirmDelete}
                    disabled={loading.isLoading}
                    className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {loading.isLoading ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}

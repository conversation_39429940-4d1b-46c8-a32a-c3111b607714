import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Advertisement } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import PageMeta from '../../components/common/PageMeta';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { formatDate, formatRelativeTime } from '../../utils/dateUtils';
import toast from 'react-hot-toast';

export default function AdvertisementDetailPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [advertisement, setAdvertisement] = useState<Advertisement | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const loading = useLoading();

  useEffect(() => {
    if (id) {
      fetchAdvertisement();
    }
  }, [id]);

  const fetchAdvertisement = async () => {
    if (!id) return;

    try {
      loading.startLoading({ message: 'Loading advertisement...' });
      
      const response = await adminApi.advertisements.getAdvertisements({
        page: 1,
        limit: 100,
      });
      
      if (response.success) {
        const foundAdvertisement = response.data.advertisements.find(ad => String(ad.id) === id);
        if (foundAdvertisement) {
          setAdvertisement(foundAdvertisement);
        } else {
          throw new Error('Advertisement not found');
        }
      } else {
        throw new Error(response.message || 'Failed to fetch advertisement');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_advertisement' });
      navigate('/advertisements');
    } finally {
      loading.stopLoading();
    }
  };

  const handleToggleStatus = async () => {
    if (!advertisement) return;

    try {
      loading.startLoading({ message: 'Updating advertisement status...' });
      const response = await adminApi.advertisements.updateAdvertisement(
        String(advertisement.id),
        { isActive: !advertisement.isActive }
      );
      
      if (response.success) {
        setAdvertisement(prev => prev ? { ...prev, isActive: !prev.isActive } : null);
        toast.success(`Advertisement ${!advertisement.isActive ? 'activated' : 'deactivated'} successfully`);
      } else {
        throw new Error(response.message || 'Failed to update advertisement status');
      }
    } catch (error) {
      handleError(error, { action: 'toggle_advertisement_status' });
    } finally {
      loading.stopLoading();
    }
  };

  const handleDelete = async () => {
    if (!advertisement) return;

    try {
      loading.startLoading({ message: 'Deleting advertisement...' });
      const response = await adminApi.advertisements.deleteAdvertisement(String(advertisement.id));
      
      if (response.success) {
        toast.success('Advertisement deleted successfully');
        navigate('/advertisements');
      } else {
        throw new Error(response.message || 'Failed to delete advertisement');
      }
    } catch (error) {
      handleError(error, { action: 'delete_advertisement' });
    } finally {
      loading.stopLoading();
      setShowDeleteModal(false);
    }
  };

  if (loading.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!advertisement) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Advertisement not found
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            The advertisement you're looking for doesn't exist.
          </p>
          <button
            onClick={() => navigate('/advertisements')}
            className="mt-4 px-4 py-2 bg-brand-600 text-white rounded-lg hover:bg-brand-700"
          >
            Back to Advertisements
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title={`${advertisement.title} | Dalti Admin Panel`}
        description={`Advertisement details: ${advertisement.title}`}
      />
      
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <div>
                    <button
                      onClick={() => navigate('/advertisements')}
                      className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                    >
                      <svg className="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <span className="sr-only">Back</span>
                    </button>
                  </div>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <button
                      onClick={() => navigate('/advertisements')}
                      className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    >
                      Advertisements
                    </button>
                  </div>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      {advertisement.title}
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            
            <div className="mt-2 flex items-center space-x-3">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {advertisement.title}
              </h1>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                advertisement.isActive
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              }`}>
                <span className={`w-2 h-2 rounded-full mr-1.5 ${
                  advertisement.isActive ? 'bg-green-400' : 'bg-gray-400'
                }`} />
                {advertisement.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={handleToggleStatus}
              className={`px-4 py-2 text-sm font-medium rounded-lg border ${
                advertisement.isActive
                  ? 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700'
                  : 'border-green-300 text-green-700 bg-green-50 hover:bg-green-100 dark:border-green-600 dark:text-green-300 dark:bg-green-900 dark:hover:bg-green-800'
              }`}
            >
              {advertisement.isActive ? 'Deactivate' : 'Activate'}
            </button>
            
            <button
              onClick={() => navigate(`/advertisements/edit/${advertisement.id}`)}
              className="px-4 py-2 text-sm font-medium text-white bg-brand-600 rounded-lg hover:bg-brand-700"
            >
              Edit
            </button>
            
            <button
              onClick={() => setShowDeleteModal(true)}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700"
            >
              Delete
            </button>
          </div>
        </div>

        {/* Advertisement Preview */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Advertisement Preview
          </h3>
          
          <div className="max-w-md mx-auto">
            <div 
              className="relative rounded-lg overflow-hidden shadow-lg"
              style={{
                backgroundColor: advertisement.backgroundImage ? 'transparent' : '#19727F',
                backgroundImage: advertisement.backgroundImage ? `url(${advertisement.backgroundImage.uploadUrl})` : undefined,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                minHeight: '200px',
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent" />
              
              <div className="relative p-6 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="text-xl font-bold mb-2">
                      {advertisement.title}
                    </h4>
                    {advertisement.subtitle && (
                      <p className="text-sm opacity-90 mb-4">
                        {advertisement.subtitle}
                      </p>
                    )}
                    
                    <button className="inline-flex items-center px-4 py-2 bg-orange-500 text-white rounded-lg font-medium hover:bg-orange-600 transition-colors">
                      {advertisement.callToActionText}
                      {advertisement.isExternal && (
                        <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      )}
                    </button>
                  </div>
                  
                  {advertisement.pngImage && (
                    <div className="ml-4">
                      <img
                        src={advertisement.pngImage.uploadUrl}
                        alt="Advertisement icon"
                        className="w-16 h-16 object-contain"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Advertisement Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Basic Information
            </h3>

            <dl className="space-y-4">
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Title</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{advertisement.title}</dd>
              </div>

              {advertisement.subtitle && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Subtitle</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{advertisement.subtitle}</dd>
                </div>
              )}

              {advertisement.description && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{advertisement.description}</dd>
                </div>
              )}

              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Call to Action</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  <span className="inline-flex items-center space-x-2">
                    <span>{advertisement.callToActionText}</span>
                    {advertisement.isExternal && (
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    )}
                  </span>
                </dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Link URL</dt>
                <dd className="mt-1 text-sm">
                  <a
                    href={advertisement.callToActionLink}
                    target={advertisement.isExternal ? '_blank' : '_self'}
                    rel={advertisement.isExternal ? 'noopener noreferrer' : undefined}
                    className="text-brand-600 hover:text-brand-500 dark:text-brand-400 dark:hover:text-brand-300 break-all"
                  >
                    {advertisement.callToActionLink}
                  </a>
                </dd>
              </div>
            </dl>
          </div>

          {/* Settings & Metadata */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Settings & Metadata
            </h3>

            <dl className="space-y-4">
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                <dd className="mt-1">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    advertisement.isActive
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {advertisement.isActive ? 'Active' : 'Inactive'}
                  </span>
                </dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Sort Order</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{advertisement.sortOrder}</dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Link Type</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  {advertisement.isExternal ? 'External (opens in new tab)' : 'Internal (same tab)'}
                </dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  {formatDate(advertisement.createdAt)} ({formatRelativeTime(advertisement.createdAt)})
                </dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  {formatDate(advertisement.updatedAt)} ({formatRelativeTime(advertisement.updatedAt)})
                </dd>
              </div>
            </dl>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full mx-4">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      Delete Advertisement
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      This action cannot be undone.
                    </p>
                  </div>
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                  Are you sure you want to delete "{advertisement.title}"?
                  This will permanently remove the advertisement and all associated data.
                </p>

                <div className="flex items-center justify-end space-x-3">
                  <button
                    onClick={() => setShowDeleteModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    disabled={loading.isLoading}
                    className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {loading.isLoading ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}

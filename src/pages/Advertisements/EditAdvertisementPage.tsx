import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Advertisement } from '../../types';
import { adminApi } from '../../services/adminApi';
import { useLoading } from '../../hooks/useLoading';
import { handleError } from '../../utils/errorHandler';
import PageMeta from '../../components/common/PageMeta';
import AdvertisementForm from '../../components/advertisements/AdvertisementForm';
import LoadingSpinner from '../../components/common/LoadingSpinner';

export default function EditAdvertisementPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [advertisement, setAdvertisement] = useState<Advertisement | null>(null);
  const loading = useLoading();

  useEffect(() => {
    if (id) {
      fetchAdvertisement();
    }
  }, [id]);

  const fetchAdvertisement = async () => {
    if (!id) return;

    try {
      loading.startLoading({ message: 'Loading advertisement...' });
      
      // Since we don't have a single advertisement endpoint, we'll get it from the list
      // In a real implementation, you'd have a getAdvertisement(id) endpoint
      const response = await adminApi.advertisements.getAdvertisements({
        page: 1,
        limit: 100, // Get all to find the specific one
      });
      
      if (response.success) {
        const foundAdvertisement = response.data.advertisements.find(ad => String(ad.id) === id);
        if (foundAdvertisement) {
          setAdvertisement(foundAdvertisement);
        } else {
          throw new Error('Advertisement not found');
        }
      } else {
        throw new Error(response.message || 'Failed to fetch advertisement');
      }
    } catch (error) {
      handleError(error, { action: 'fetch_advertisement' });
      navigate('/advertisements');
    } finally {
      loading.stopLoading();
    }
  };

  const handleSubmit = (updatedAdvertisement: Advertisement) => {
    // Navigate back to the advertisements list
    navigate('/advertisements');
  };

  const handleCancel = () => {
    navigate('/advertisements');
  };

  if (loading.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!advertisement) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Advertisement not found
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            The advertisement you're looking for doesn't exist.
          </p>
          <button
            onClick={() => navigate('/advertisements')}
            className="mt-4 px-4 py-2 bg-brand-600 text-white rounded-lg hover:bg-brand-700"
          >
            Back to Advertisements
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title={`Edit ${advertisement.title} | Dalti Admin Panel`}
        description={`Edit advertisement: ${advertisement.title}`}
      />
      
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <div>
                    <button
                      onClick={() => navigate('/advertisements')}
                      className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                    >
                      <svg className="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <span className="sr-only">Back</span>
                    </button>
                  </div>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <button
                      onClick={() => navigate('/advertisements')}
                      className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    >
                      Advertisements
                    </button>
                  </div>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      Edit
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            
            <div className="mt-2">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Edit Advertisement
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                Update the details for "{advertisement.title}"
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <AdvertisementForm
          advertisement={advertisement}
          mode="edit"
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      </div>
    </>
  );
}

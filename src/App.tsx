import { BrowserRouter as Router, Routes, Route } from "react-router";
import { Toaster } from "react-hot-toast";
import AdminLogin from "./pages/AuthPages/AdminLogin";
import NotFound from "./pages/OtherPage/NotFound";
import AppLayout from "./layout/AppLayout";
import { ScrollToTop } from "./components/common/ScrollToTop";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import DaltiDashboard from "./pages/Dashboard/DaltiDashboard";
import ProvidersPage from "./pages/Providers/ProvidersPage";
import ProviderDetailPage from "./pages/Providers/ProviderDetailPage";
import ProviderAnalyticsPage from "./pages/Providers/ProviderAnalyticsPage";
import CustomersPage from "./pages/Customers/CustomersPage";
import CustomerAnalyticsPage from "./pages/Customers/CustomerAnalyticsPage";
import CustomerSupportPage from "./pages/Customers/CustomerSupportPage";
import CategoriesPage from "./pages/Categories/CategoriesPage";
import CreateCategoryPage from "./pages/Categories/CreateCategoryPage";
import EditCategoryPage from "./pages/Categories/EditCategoryPage";
import AdvertisementsPage from "./pages/Advertisements/AdvertisementsPage";
import CreateAdvertisementPage from "./pages/Advertisements/CreateAdvertisementPage";
import EditAdvertisementPage from "./pages/Advertisements/EditAdvertisementPage";
import AdvertisementDetailPage from "./pages/Advertisements/AdvertisementDetailPage";
import AdminUsersPage from "./pages/Admin/AdminUsersPage";
import SettingsPage from "./pages/Settings/SettingsPage";
// TODO: Import other Dalti admin pages as they are created

export default function App() {
  return (
    <>
      <Router>
        <ScrollToTop />
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />
        <Routes>
          {/* Protected Dalti Admin Dashboard Layout */}
          <Route element={
            <ProtectedRoute>
              <AppLayout />
            </ProtectedRoute>
          }>
            <Route index path="/" element={<DaltiDashboard />} />

            {/* Dalti admin routes */}
            <Route path="/providers" element={<ProvidersPage />} />
            <Route path="/provider/:id" element={<ProviderDetailPage />} />
            <Route path="/providers/analytics" element={<ProviderAnalyticsPage />} />
            <Route path="/customers" element={<CustomersPage />} />
            <Route path="/customers/analytics" element={<CustomerAnalyticsPage />} />
            <Route path="/customers/support" element={<CustomerSupportPage />} />
            <Route path="/categories" element={<CategoriesPage />} />
            <Route path="/categories/create" element={<CreateCategoryPage />} />
            <Route path="/categories/edit/:id" element={<EditCategoryPage />} />
            <Route path="/advertisements" element={<AdvertisementsPage />} />
            <Route path="/advertisements/create" element={<CreateAdvertisementPage />} />
            <Route path="/advertisements/edit/:id" element={<EditAdvertisementPage />} />
            <Route path="/advertisements/:id" element={<AdvertisementDetailPage />} />
            <Route path="/admin/users" element={<AdminUsersPage />} />
            <Route path="/admin/settings" element={<SettingsPage />} />
            {/* TODO: Add more admin routes as needed */}
          </Route>

          {/* Admin Authentication - Only accessible when not authenticated */}
          <Route path="/login" element={
            <ProtectedRoute requireAuth={false}>
              <AdminLogin />
            </ProtectedRoute>
          } />

          {/* Fallback Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Router>
    </>
  );
}

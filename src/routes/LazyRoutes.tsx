/**
 * Optimized Lazy Route Definitions
 * Implements code splitting and lazy loading for all route components
 */

import { createLazyComponent } from '../utils/lazyLoading';

// Dashboard routes
export const DashboardPage = createLazyComponent(
  () => import('../pages/Dashboard'),
  {
    chunkName: 'dashboard',
    preload: true, // Preload dashboard as it's the main page
  }
);

// Provider management routes
export const ProvidersPage = createLazyComponent(
  () => import('../pages/providers/ProvidersPage'),
  {
    chunkName: 'providers',
  }
);

export const ProviderDetailsPage = createLazyComponent(
  () => import('../pages/providers/ProviderDetailsPage'),
  {
    chunkName: 'provider-details',
  }
);

export const ProviderAnalyticsPage = createLazyComponent(
  () => import('../pages/providers/ProviderAnalyticsPage'),
  {
    chunkName: 'provider-analytics',
  }
);

// Customer management routes
export const CustomersPage = createLazyComponent(
  () => import('../pages/customers/CustomersPage'),
  {
    chunkName: 'customers',
  }
);

export const CustomerDetailsPage = createLazyComponent(
  () => import('../pages/customers/CustomerDetailsPage'),
  {
    chunkName: 'customer-details',
  }
);

export const CustomerAnalyticsPage = createLazyComponent(
  () => import('../pages/customers/CustomerAnalyticsPage'),
  {
    chunkName: 'customer-analytics',
  }
);

export const CustomerSupportPage = createLazyComponent(
  () => import('../pages/customers/CustomerSupportPage'),
  {
    chunkName: 'customer-support',
  }
);

// Category management routes
export const CategoriesPage = createLazyComponent(
  () => import('../pages/categories/CategoriesPage'),
  {
    chunkName: 'categories',
  }
);

export const CategoryDetailsPage = createLazyComponent(
  () => import('../pages/categories/CategoryDetailsPage'),
  {
    chunkName: 'category-details',
  }
);

// Admin routes
export const AdminUsersPage = createLazyComponent(
  () => import('../pages/admin/AdminUsersPage'),
  {
    chunkName: 'admin-users',
  }
);

export const SettingsPage = createLazyComponent(
  () => import('../pages/admin/SettingsPage'),
  {
    chunkName: 'settings',
  }
);

export const AuditLogsPage = createLazyComponent(
  () => import('../pages/admin/AuditLogsPage'),
  {
    chunkName: 'audit-logs',
  }
);

export const SystemMonitoringPage = createLazyComponent(
  () => import('../pages/admin/SystemMonitoringPage'),
  {
    chunkName: 'system-monitoring',
  }
);

// Authentication routes
export const AdminLogin = createLazyComponent(
  () => import('../components/auth/AdminLogin'),
  {
    chunkName: 'auth',
    preload: true, // Preload auth as it might be needed quickly
  }
);

// Error pages
export const NotFoundPage = createLazyComponent(
  () => import('../pages/errors/NotFoundPage'),
  {
    chunkName: 'error-pages',
  }
);

export const UnauthorizedPage = createLazyComponent(
  () => import('../pages/errors/UnauthorizedPage'),
  {
    chunkName: 'error-pages',
  }
);

// Route preloading strategies
export class RoutePreloader {
  private static preloadedRoutes = new Set<string>();

  // Preload critical routes on app initialization
  static preloadCriticalRoutes(): void {
    const criticalRoutes = [
      { component: DashboardPage, name: 'dashboard' },
      { component: AdminLogin, name: 'auth' },
    ];

    criticalRoutes.forEach(({ component, name }) => {
      if (!this.preloadedRoutes.has(name)) {
        // Preload after a short delay to not block initial render
        setTimeout(() => {
          this.preloadRoute(component, name);
        }, 100);
      }
    });
  }

  // Preload routes based on user navigation patterns
  static preloadByUserRole(userRole: string): void {
    const roleBasedRoutes: Record<string, Array<{ component: any; name: string }>> = {
      super_admin: [
        { component: ProvidersPage, name: 'providers' },
        { component: CustomersPage, name: 'customers' },
        { component: CategoriesPage, name: 'categories' },
        { component: AdminUsersPage, name: 'admin-users' },
        { component: SettingsPage, name: 'settings' },
      ],
      admin: [
        { component: ProvidersPage, name: 'providers' },
        { component: CustomersPage, name: 'customers' },
        { component: CategoriesPage, name: 'categories' },
      ],
      moderator: [
        { component: ProvidersPage, name: 'providers' },
        { component: CustomersPage, name: 'customers' },
      ],
    };

    const routes = roleBasedRoutes[userRole] || [];
    
    routes.forEach(({ component, name }, index) => {
      // Stagger preloading to avoid overwhelming the network
      setTimeout(() => {
        this.preloadRoute(component, name);
      }, index * 200);
    });
  }

  // Preload routes on hover over navigation items
  static preloadOnHover(routeName: string, component: any): (element: HTMLElement) => () => void {
    return (element: HTMLElement) => {
      let timeoutId: NodeJS.Timeout;

      const handleMouseEnter = () => {
        timeoutId = setTimeout(() => {
          this.preloadRoute(component, routeName);
        }, 150); // Small delay to avoid unnecessary preloads
      };

      const handleMouseLeave = () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      };

      element.addEventListener('mouseenter', handleMouseEnter);
      element.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        element.removeEventListener('mouseenter', handleMouseEnter);
        element.removeEventListener('mouseleave', handleMouseLeave);
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      };
    };
  }

  // Preload routes based on current route
  static preloadRelatedRoutes(currentRoute: string): void {
    const relatedRoutes: Record<string, Array<{ component: any; name: string }>> = {
      '/dashboard': [
        { component: ProvidersPage, name: 'providers' },
        { component: CustomersPage, name: 'customers' },
      ],
      '/providers': [
        { component: ProviderDetailsPage, name: 'provider-details' },
        { component: ProviderAnalyticsPage, name: 'provider-analytics' },
      ],
      '/customers': [
        { component: CustomerDetailsPage, name: 'customer-details' },
        { component: CustomerAnalyticsPage, name: 'customer-analytics' },
        { component: CustomerSupportPage, name: 'customer-support' },
      ],
      '/categories': [
        { component: CategoryDetailsPage, name: 'category-details' },
      ],
      '/admin/users': [
        { component: SettingsPage, name: 'settings' },
        { component: AuditLogsPage, name: 'audit-logs' },
      ],
    };

    const routes = relatedRoutes[currentRoute] || [];
    
    routes.forEach(({ component, name }, index) => {
      setTimeout(() => {
        this.preloadRoute(component, name);
      }, index * 300);
    });
  }

  // Preload routes during idle time
  static preloadDuringIdle(): void {
    if ('requestIdleCallback' in window) {
      const preloadQueue = [
        { component: ProviderAnalyticsPage, name: 'provider-analytics' },
        { component: CustomerAnalyticsPage, name: 'customer-analytics' },
        { component: CustomerSupportPage, name: 'customer-support' },
        { component: AuditLogsPage, name: 'audit-logs' },
        { component: SystemMonitoringPage, name: 'system-monitoring' },
      ];

      const preloadNext = () => {
        if (preloadQueue.length === 0) return;

        window.requestIdleCallback((deadline) => {
          while (deadline.timeRemaining() > 0 && preloadQueue.length > 0) {
            const { component, name } = preloadQueue.shift()!;
            this.preloadRoute(component, name);
          }
          
          if (preloadQueue.length > 0) {
            preloadNext();
          }
        });
      };

      preloadNext();
    }
  }

  private static async preloadRoute(component: any, name: string): Promise<void> {
    if (this.preloadedRoutes.has(name)) return;

    try {
      // Mark as being preloaded to avoid duplicates
      this.preloadedRoutes.add(name);
      
      // Trigger the lazy component to load
      await component._payload._result;
      
      console.log(`📦 Preloaded route: ${name}`);
    } catch (error) {
      console.warn(`Failed to preload route ${name}:`, error);
      // Remove from preloaded set so it can be retried
      this.preloadedRoutes.delete(name);
    }
  }
}

// Route-based code splitting configuration
export const routeConfig = {
  // Critical routes that should be preloaded
  critical: ['dashboard', 'auth'],
  
  // Routes that can be preloaded on idle
  idle: ['provider-analytics', 'customer-analytics', 'audit-logs'],
  
  // Routes that should be preloaded based on user role
  roleBasedPreload: {
    super_admin: ['providers', 'customers', 'categories', 'admin-users', 'settings'],
    admin: ['providers', 'customers', 'categories'],
    moderator: ['providers', 'customers'],
  },
  
  // Related routes that should be preloaded together
  related: {
    '/dashboard': ['providers', 'customers'],
    '/providers': ['provider-details', 'provider-analytics'],
    '/customers': ['customer-details', 'customer-analytics', 'customer-support'],
    '/categories': ['category-details'],
    '/admin/users': ['settings', 'audit-logs'],
  },
};

// Initialize route preloading
export function initializeRoutePreloading(userRole?: string, currentRoute?: string): void {
  // Preload critical routes immediately
  RoutePreloader.preloadCriticalRoutes();
  
  // Preload based on user role
  if (userRole) {
    setTimeout(() => {
      RoutePreloader.preloadByUserRole(userRole);
    }, 500);
  }
  
  // Preload related routes
  if (currentRoute) {
    setTimeout(() => {
      RoutePreloader.preloadRelatedRoutes(currentRoute);
    }, 1000);
  }
  
  // Preload during idle time
  setTimeout(() => {
    RoutePreloader.preloadDuringIdle();
  }, 2000);
}

export default {
  // Dashboard
  DashboardPage,
  
  // Providers
  ProvidersPage,
  ProviderDetailsPage,
  ProviderAnalyticsPage,
  
  // Customers
  CustomersPage,
  CustomerDetailsPage,
  CustomerAnalyticsPage,
  CustomerSupportPage,
  
  // Categories
  CategoriesPage,
  CategoryDetailsPage,
  
  // Admin
  AdminUsersPage,
  SettingsPage,
  AuditLogsPage,
  SystemMonitoringPage,
  
  // Auth
  AdminLogin,
  
  // Errors
  NotFoundPage,
  UnauthorizedPage,
  
  // Utilities
  RoutePreloader,
  initializeRoutePreloading,
};

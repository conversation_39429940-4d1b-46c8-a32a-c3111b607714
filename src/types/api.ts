// Dalti Admin Panel API Types

// Base API Response Structure
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Authentication Types
export interface AdminLoginRequest {
  email: string;
  password: string;
}

export interface AdminLoginResponse {
  token: string;
  refreshToken?: string;
  admin: AdminUser;
}

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'ADMIN';
  isAdmin: true;
  createdAt: string;
  lastLoginAt?: string;
}

// Provider Types
// Raw provider data from API
export interface RawProvider {
  id: number;
  title: string;
  phone?: string;
  presentation?: string;
  isVerified: boolean;
  isSetupComplete?: boolean;
  logoId?: string;
  userId?: string;
  isLowPriorityInSearchResult?: boolean;
  averageRating?: number | null;
  totalReviews?: number;
  providerCategoryId?: number;
  createdAt: string;
  updatedAt: string;

  user?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    mobileNumber?: string;
    createdAt: string;
    isPhoneVerified: boolean;
    isEmailVerified: boolean;
  };

  category?: {
    id: number;
    title: string;
  };

  _count?: {
    services: number;
    customerFolders: number;
    reviewsReceived: number;
    queues?: number;
  };

  // Additional fields that may be present in single provider response
  services?: Array<{
    id: number;
    title: string;
    description?: string;
    price?: number;
    duration: number;
    acceptOnline: boolean;
    acceptNew: boolean;
    notificationOn: boolean;
    pointsRequirements: number;
    isPublic: boolean;
    deliveryType?: string;
    color: string;
  }>;

  queues?: Array<any>;
}

// Provider Statistics Types
export interface ProviderStats {
  overview: {
    totalAppointments: number;
    monthlyAppointments: number;
    weeklyAppointments: number;
    todayAppointments: number;
    totalCustomers: number;
    totalServices: number;
    totalLocations: number;
    totalQueues: number;
    averageRating: number | null;
    totalReviews: number;
    isVerified: boolean;
    isSetupComplete: boolean;
    memberSince: string;
  };
  appointments: {
    byStatus: {
      completed: number;
      confirmed: number;
      [key: string]: number;
    };
    recent: Array<{
      id: number;
      status: string;
      createdAt: string;
      expectedStartTime: string;
      customer: {
        name: string;
        email: string;
      };
      service: {
        title: string;
        duration: number;
      };
      location: {
        name: string;
      };
    }>;
  };
  revenue: {
    monthlyTotal: number;
    currency: string;
  };
}

// Provider Service Types
export interface ProviderServiceDetail {
  id: number;
  createdAt: string;
  updatedAt: string;
  title: string;
  color: string;
  sProviderId: number;
  serviceCategoryId: number | null;
  duration: number;
  minDuration: number | null;
  maxDuration: number | null;
  queue: any | null;
  acceptOnline: boolean;
  acceptNew: boolean;
  notificationOn: boolean;
  pointsRequirements: number;
  price: number | null;
  isPublic: boolean;
  deliveryType: string | null;
  servedRegions: any | null;
  description: string | null;
  category: any | null;
  queues: Array<{
    id: number;
    title: string;
    isActive: boolean;
    sProvidingPlace: {
      id: number;
      name: string;
    };
  }>;
  _count: {
    appointments: number;
    queues: number;
  };
}

// Provider Location Types
export interface ProviderLocationDetail {
  id: number;
  createdAt: string;
  updatedAt: string;
  sProviderId: number;
  name: string;
  shortName: string | null;
  address: string | null;
  city: string | null;
  mobile: string | null;
  isMobileHidden: boolean;
  fax: string | null;
  floor: string | null;
  parking: boolean;
  elevator: boolean;
  handicapAccess: boolean;
  timezone: string;
  detailedAddressId: number;
  detailedAddress: {
    id: number;
    address: string;
    city: string;
    state: string | null;
    postalCode: string;
    country: string;
    latitude: number;
    longitude: number;
    description: string | null;
    isPrimary: boolean;
  };
  openings: Array<{
    id: number;
    createdAt: string;
    updatedAt: string;
    sProvidingPlaceId: number;
    dayOfWeek: string;
    type: string;
    isActive: boolean;
    hours: Array<{
      id: number;
      timeFrom: string;
      timeTo: string;
    }>;
  }>;
  queues: Array<{
    id: number;
    title: string;
    isActive: boolean;
  }>;
  _count: {
    appointments: number;
    queues: number;
  };
}

// Normalized provider interface for UI
export interface Provider {
  id: string; // Normalized to string
  name: string;
  email: string;
  phone?: string;
  businessTitle: string;
  description?: string;
  verified: boolean;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  locationCount: number;
  serviceCount: number;
  customerCount: number;
  rating?: number;
  totalRatings?: number;
  createdAt: string;
  updatedAt: string;
  approvedAt?: string;
  approvedBy?: string;
  adminNotes?: string;
  category?: ProviderCategory;
  locations?: ProviderLocation[];
  services?: ProviderService[];
}

export interface ProviderLocation {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
  workingHours?: WorkingHours[];
}

export interface ProviderService {
  id: string;
  name: string;
  description?: string;
  duration: number; // in minutes
  price: number;
  currency: string;
  category?: string;
  isActive: boolean;
}

export interface WorkingHours {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  openTime: string; // HH:mm format
  closeTime: string; // HH:mm format
  isOpen: boolean;
}

export interface ProviderApprovalRequest {
  action: 'approve' | 'reject';
  adminNotes?: string;
}

export interface ProvidersListRequest {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'pending' | 'approved' | 'rejected' | 'suspended';
  verified?: boolean;
  category?: string;
  dateRange?: string;
  sortBy?: 'name' | 'createdAt' | 'rating' | 'customerCount' | 'email' | 'status' | 'verified';
  sortOrder?: 'asc' | 'desc';
}

// Customer Types
// Raw customer data from API
export interface RawCustomer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber?: string | null;
  createdAt: string;
  isPhoneVerified: boolean;
  isEmailVerified: boolean;
  role: string;
  _count?: {
    customerFolders: number;
  };
}

// Normalized customer interface for UI
export interface Customer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  verified: boolean;
  credits: number;
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  noShowCount: number;
  averageRating?: number;
  createdAt: string;
  updatedAt: string;
  lastActiveAt?: string;
  preferredLanguage?: 'en' | 'ar' | 'fr';
  addresses?: CustomerAddress[];
}

export interface CustomerAddress {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
  latitude?: number;
  longitude?: number;
  isDefault: boolean;
}

export interface CustomersListRequest {
  page?: number;
  limit?: number;
  search?: string;
  verified?: boolean;
  sortBy?: 'name' | 'createdAt' | 'totalBookings' | 'credits';
  sortOrder?: 'asc' | 'desc';
}

// File Upload Types
export interface FileUploadResponse {
  id: string;
  name: string;
  type: string;
  key: string;
  uploadUrl?: string;
  createdAt?: string;
}

export interface ImageUploadRequest {
  fileName: string;
  fileType: string;
}

export interface CategoryImageUploadResponse {
  uploadUrl: string;
  uploadFields: {
    key: string;
    'Content-Type': string;
    policy: string;
    signature: string;
    [key: string]: string;
  };
  file: FileUploadResponse;
  category: ProviderCategory;
}

// Provider Category Types
export interface ProviderCategory {
  id: string | number; // API returns numbers but we convert to strings
  title: string; // API uses 'title' field
  description?: string;
  parentId?: string | number | null;
  level?: number;
  isActive?: boolean;
  providerCount?: number;
  sortOrder?: number;
  imageId?: string | null;
  image?: FileUploadResponse | null;
  _count?: {
    providers: number;
    children: number;
  };
  metadata?: {
    icon?: string;
    color?: string;
    keywords?: string[];
    seoTitle?: string;
    seoDescription?: string;
  };
  createdAt?: string;
  updatedAt?: string;
  children?: ProviderCategory[];
  parent?: ProviderCategory | null;
  providers?: Array<{
    id: number;
    title: string;
    isVerified: boolean;
    user: {
      firstName: string;
      lastName: string;
      email: string;
    };
  }>;
}

export interface CreateCategoryRequest {
  title: string;
  description?: string;
  parentId?: string | null;
  isActive?: boolean;
  sortOrder?: number;
  metadata?: {
    icon?: string;
    color?: string;
    keywords?: string[];
    seoTitle?: string;
    seoDescription?: string;
  };
}

export interface UpdateCategoryRequest {
  title?: string;
  description?: string;
  parentId?: string | null;
  isActive?: boolean;
  sortOrder?: number;
  metadata?: {
    icon?: string;
    color?: string;
    keywords?: string[];
    seoTitle?: string;
    seoDescription?: string;
  };
}

// Dashboard Analytics Types
export interface DashboardMetrics {
  totalProviders: number;
  verifiedProviders: number;
  pendingProviders: number;
  totalCustomers: number;
  verifiedCustomers: number;
  totalBookings: number;
  completedBookings: number;
  totalRevenue: number;
  growthMetrics: {
    providersGrowth: number; // percentage
    customersGrowth: number; // percentage
    bookingsGrowth: number; // percentage
    revenueGrowth: number; // percentage
  };
}

export interface RecentActivity {
  id: string;
  type: 'provider_registration' | 'provider_approval' | 'customer_registration' | 'booking_created' | 'booking_completed';
  title: string;
  description: string;
  timestamp: string;
  entityId?: string;
  entityType?: 'provider' | 'customer' | 'booking';
}

export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface AnalyticsData {
  providerGrowth: ChartDataPoint[];
  customerGrowth: ChartDataPoint[];
  bookingTrends: ChartDataPoint[];
  revenueData: ChartDataPoint[];
  categoryDistribution: {
    categoryName: string;
    providerCount: number;
    percentage: number;
  }[];
}

// Error Types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Audit Log Types
export interface AuditLog {
  id: string;
  adminId: string;
  adminEmail: string;
  action: string;
  entityType: 'provider' | 'customer' | 'category' | 'system';
  entityId?: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
}

export interface AuditLogsRequest {
  page?: number;
  limit?: number;
  adminId?: string;
  action?: string;
  entityType?: 'provider' | 'customer' | 'category' | 'system';
  entityId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

// Advertisement Types
export interface Advertisement {
  id: number;
  title: string;
  subtitle?: string;
  description?: string;
  callToActionText: string;
  callToActionLink: string;
  isExternal: boolean;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  backgroundImage?: FileUploadResponse | null;
  pngImage?: FileUploadResponse | null;
}

export interface CreateAdvertisementRequest {
  title: string;
  subtitle?: string;
  description?: string;
  callToActionText: string;
  callToActionLink: string;
  isExternal?: boolean;
  isActive?: boolean;
  sortOrder?: number;
  backgroundImageId?: string;
  pngImageId?: string;
}

export interface UpdateAdvertisementRequest {
  title?: string;
  subtitle?: string;
  description?: string;
  callToActionText?: string;
  callToActionLink?: string;
  isExternal?: boolean;
  isActive?: boolean;
  sortOrder?: number;
  backgroundImageId?: string;
  pngImageId?: string;
}

export interface AdvertisementsListRequest {
  page?: number;
  limit?: number;
  isActive?: boolean;
  search?: string;
}

export interface AdvertisementImageUploadResponse {
  uploadUrl: string;
  uploadFields: {
    key: string;
    'Content-Type': string;
    policy: string;
    signature: string;
    [key: string]: string;
  };
  file: FileUploadResponse;
  advertisement: Advertisement;
}

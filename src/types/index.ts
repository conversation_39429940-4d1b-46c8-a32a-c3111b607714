// Dalti Admin Panel - Type Definitions Index

// Export all API types
export * from './api';

// Export all application types
export * from './app';

// Re-export commonly used types for convenience
export type {
  // Authentication
  AdminUser,
  AuthState,
  AdminLoginRequest,
  AdminLoginResponse,
  
  // API
  ApiResponse,
  PaginationInfo,
  ApiError,
  
  // Providers
  Provider,
  ProviderLocation,
  ProviderService,
  ProvidersListRequest,
  ProviderApprovalRequest,
  ProviderStats,
  ProviderServiceDetail,
  ProviderLocationDetail,
  
  // Customers
  Customer,
  CustomerAddress,
  CustomersListRequest,
  
  // Categories
  ProviderCategory,
  CreateCategoryRequest,
  UpdateCategoryRequest,

  // Advertisements
  Advertisement,
  CreateAdvertisementRequest,
  UpdateAdvertisementRequest,
  AdvertisementsListRequest,
  AdvertisementImageUploadResponse,

  // File Upload
  FileUploadResponse,
  ImageUploadRequest,
  CategoryImageUploadResponse,
  
  // Dashboard
  DashboardMetrics,
  RecentActivity,
  AnalyticsData,
  ChartDataPoint,
  
  // UI Components
  TableProps,
  TableColumn,
  PaginationProps,
  ModalProps,
  NotificationConfig,
  
  // Forms and Filters
  SearchFilters,
  FilterOption,
  FormField,
  
  // Navigation
  NavItem,
  BreadcrumbItem,
  
  // Settings
  AppSettings,
  
  // State Management
  LoadingState,
  ErrorState,
  
  // API Client
  ApiClientConfig,
  RequestConfig,
  
  // Cache
  CacheEntry,
  CacheConfig,
  
  // Audit
  AuditLog,
  AuditLogsRequest,
} from './api';

export type {
  // Dashboard Widgets
  DashboardWidget,
  MetricWidget,
  
  // Charts
  ChartConfig,
} from './app';

/* <PERSON><PERSON> Brand Theme Variables */
:root {
  /* Primary Dalti Colors */
  --dalti-primary: #19727F;
  --dalti-primary-50: #f0f9fa;
  --dalti-primary-100: #ccecef;
  --dalti-primary-200: #99d8de;
  --dalti-primary-300: #66c5cd;
  --dalti-primary-400: #33b1bc;
  --dalti-primary-500: #1a8a96;
  --dalti-primary-600: #19727F;
  --dalti-primary-700: #155b68;
  --dalti-primary-800: #114451;
  --dalti-primary-900: #0d2d3a;

  /* Secondary Dalti Colors */
  --dalti-secondary: #10b981;
  --dalti-secondary-50: #ecfdf5;
  --dalti-secondary-100: #d1fae5;
  --dalti-secondary-200: #a7f3d0;
  --dalti-secondary-300: #6ee7b7;
  --dalti-secondary-400: #34d399;
  --dalti-secondary-500: #10b981;
  --dalti-secondary-600: #059669;
  --dalti-secondary-700: #047857;
  --dalti-secondary-800: #065f46;
  --dalti-secondary-900: #064e3b;

  /* Accent Colors */
  --dalti-accent: #f59e0b;
  --dalti-accent-50: #fffbeb;
  --dalti-accent-100: #fef3c7;
  --dalti-accent-200: #fde68a;
  --dalti-accent-300: #fcd34d;
  --dalti-accent-400: #fbbf24;
  --dalti-accent-500: #f59e0b;
  --dalti-accent-600: #d97706;
  --dalti-accent-700: #b45309;
  --dalti-accent-800: #92400e;
  --dalti-accent-900: #78350f;

  /* Status Colors */
  --dalti-success: #10b981;
  --dalti-warning: #f59e0b;
  --dalti-error: #ef4444;
  --dalti-info: #3b82f6;

  /* Neutral Colors */
  --dalti-gray-50: #f9fafb;
  --dalti-gray-100: #f3f4f6;
  --dalti-gray-200: #e5e7eb;
  --dalti-gray-300: #d1d5db;
  --dalti-gray-400: #9ca3af;
  --dalti-gray-500: #6b7280;
  --dalti-gray-600: #4b5563;
  --dalti-gray-700: #374151;
  --dalti-gray-800: #1f2937;
  --dalti-gray-900: #111827;

  /* Typography */
  --dalti-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --dalti-font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

  /* Spacing */
  --dalti-spacing-xs: 0.25rem;
  --dalti-spacing-sm: 0.5rem;
  --dalti-spacing-md: 1rem;
  --dalti-spacing-lg: 1.5rem;
  --dalti-spacing-xl: 2rem;
  --dalti-spacing-2xl: 3rem;

  /* Border Radius */
  --dalti-radius-sm: 0.375rem;
  --dalti-radius-md: 0.5rem;
  --dalti-radius-lg: 0.75rem;
  --dalti-radius-xl: 1rem;

  /* Shadows */
  --dalti-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --dalti-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --dalti-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --dalti-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --dalti-transition: all 0.2s ease-in-out;
  --dalti-transition-fast: all 0.15s ease-in-out;
  --dalti-transition-slow: all 0.3s ease-in-out;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --dalti-bg-primary: #0f172a;
  --dalti-bg-secondary: #1e293b;
  --dalti-bg-tertiary: #334155;
  --dalti-text-primary: #f8fafc;
  --dalti-text-secondary: #cbd5e1;
  --dalti-text-tertiary: #94a3b8;
  --dalti-border: #334155;
  --dalti-border-light: #475569;
}

/* Light Theme Variables */
[data-theme="light"] {
  --dalti-bg-primary: #ffffff;
  --dalti-bg-secondary: #f8fafc;
  --dalti-bg-tertiary: #f1f5f9;
  --dalti-text-primary: #0f172a;
  --dalti-text-secondary: #475569;
  --dalti-text-tertiary: #64748b;
  --dalti-border: #e2e8f0;
  --dalti-border-light: #f1f5f9;
}

/* Dalti Component Styles */
.dalti-card {
  background: var(--dalti-bg-primary);
  border: 1px solid var(--dalti-border);
  border-radius: var(--dalti-radius-lg);
  box-shadow: var(--dalti-shadow-sm);
  transition: var(--dalti-transition);
}

.dalti-card:hover {
  box-shadow: var(--dalti-shadow-md);
}

.dalti-button-primary {
  background: var(--dalti-primary-600);
  color: white;
  border: none;
  border-radius: var(--dalti-radius-md);
  padding: var(--dalti-spacing-sm) var(--dalti-spacing-md);
  font-weight: 500;
  transition: var(--dalti-transition);
  cursor: pointer;
}

.dalti-button-primary:hover {
  background: var(--dalti-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--dalti-shadow-md);
}

.dalti-button-secondary {
  background: var(--dalti-secondary-600);
  color: white;
  border: none;
  border-radius: var(--dalti-radius-md);
  padding: var(--dalti-spacing-sm) var(--dalti-spacing-md);
  font-weight: 500;
  transition: var(--dalti-transition);
  cursor: pointer;
}

.dalti-button-secondary:hover {
  background: var(--dalti-secondary-700);
  transform: translateY(-1px);
  box-shadow: var(--dalti-shadow-md);
}

.dalti-input {
  background: var(--dalti-bg-primary);
  border: 1px solid var(--dalti-border);
  border-radius: var(--dalti-radius-md);
  padding: var(--dalti-spacing-sm) var(--dalti-spacing-md);
  color: var(--dalti-text-primary);
  transition: var(--dalti-transition);
}

.dalti-input:focus {
  outline: none;
  border-color: var(--dalti-primary-500);
  box-shadow: 0 0 0 3px var(--dalti-primary-100);
}

.dalti-badge-success {
  background: var(--dalti-secondary-100);
  color: var(--dalti-secondary-800);
  padding: var(--dalti-spacing-xs) var(--dalti-spacing-sm);
  border-radius: var(--dalti-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.dalti-badge-warning {
  background: var(--dalti-accent-100);
  color: var(--dalti-accent-800);
  padding: var(--dalti-spacing-xs) var(--dalti-spacing-sm);
  border-radius: var(--dalti-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.dalti-badge-error {
  background: #fef2f2;
  color: #991b1b;
  padding: var(--dalti-spacing-xs) var(--dalti-spacing-sm);
  border-radius: var(--dalti-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.dalti-badge-info {
  background: var(--dalti-primary-100);
  color: var(--dalti-primary-800);
  padding: var(--dalti-spacing-xs) var(--dalti-spacing-sm);
  border-radius: var(--dalti-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

/* Dalti Gradient Backgrounds */
.dalti-gradient-primary {
  background: linear-gradient(135deg, var(--dalti-primary-600) 0%, var(--dalti-primary-700) 100%);
}

.dalti-gradient-secondary {
  background: linear-gradient(135deg, var(--dalti-secondary-600) 0%, var(--dalti-secondary-700) 100%);
}

.dalti-gradient-accent {
  background: linear-gradient(135deg, var(--dalti-accent-500) 0%, var(--dalti-accent-600) 100%);
}

/* Dalti Animation Classes */
.dalti-fade-in {
  animation: dalti-fadeIn 0.3s ease-in-out;
}

.dalti-slide-up {
  animation: dalti-slideUp 0.3s ease-out;
}

.dalti-scale-in {
  animation: dalti-scaleIn 0.2s ease-out;
}

@keyframes dalti-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes dalti-slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes dalti-scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Dalti Status Indicators */
.dalti-status-online {
  width: 8px;
  height: 8px;
  background: var(--dalti-secondary-500);
  border-radius: 50%;
  display: inline-block;
  animation: dalti-pulse 2s infinite;
}

.dalti-status-offline {
  width: 8px;
  height: 8px;
  background: var(--dalti-gray-400);
  border-radius: 50%;
  display: inline-block;
}

.dalti-status-error {
  width: 8px;
  height: 8px;
  background: var(--dalti-error);
  border-radius: 50%;
  display: inline-block;
  animation: dalti-pulse 1s infinite;
}

@keyframes dalti-pulse {
  0% {
    box-shadow: 0 0 0 0 currentColor;
    opacity: 1;
  }
  70% {
    box-shadow: 0 0 0 10px transparent;
    opacity: 0;
  }
  100% {
    box-shadow: 0 0 0 0 transparent;
    opacity: 0;
  }
}

/* Dalti Typography */
.dalti-heading-1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--dalti-text-primary);
  font-family: var(--dalti-font-family);
}

.dalti-heading-2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
  color: var(--dalti-text-primary);
  font-family: var(--dalti-font-family);
}

.dalti-heading-3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  color: var(--dalti-text-primary);
  font-family: var(--dalti-font-family);
}

.dalti-body-large {
  font-size: 1.125rem;
  line-height: 1.6;
  color: var(--dalti-text-secondary);
  font-family: var(--dalti-font-family);
}

.dalti-body {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--dalti-text-secondary);
  font-family: var(--dalti-font-family);
}

.dalti-body-small {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--dalti-text-tertiary);
  font-family: var(--dalti-font-family);
}

.dalti-caption {
  font-size: 0.75rem;
  line-height: 1.4;
  color: var(--dalti-text-tertiary);
  font-family: var(--dalti-font-family);
}

/* Dalti Layout Utilities */
.dalti-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--dalti-spacing-md);
}

.dalti-section {
  padding: var(--dalti-spacing-2xl) 0;
}

.dalti-grid {
  display: grid;
  gap: var(--dalti-spacing-lg);
}

.dalti-flex {
  display: flex;
  gap: var(--dalti-spacing-md);
}

.dalti-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dalti-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dalti-heading-1 {
    font-size: 1.875rem;
  }
  
  .dalti-heading-2 {
    font-size: 1.5rem;
  }
  
  .dalti-container {
    padding: 0 var(--dalti-spacing-sm);
  }
  
  .dalti-section {
    padding: var(--dalti-spacing-xl) 0;
  }
}

#!/bin/bash

# Deployment Verification Script for Dalti Admin Panel
# This script verifies that the deployment was successful

set -e

echo "🔍 Verifying Dalti Admin Panel Deployment..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed or not in PATH"
    exit 1
fi

# Check namespace
echo "📋 Checking namespace..."
if kubectl get namespace dalti &> /dev/null; then
    echo "✅ Namespace 'dalti' exists"
else
    echo "❌ Namespace 'dalti' does not exist"
    exit 1
fi

# Check ConfigMap
echo "📋 Checking ConfigMap..."
if kubectl get configmap dalti-frontend-nginx-conf -n dalti &> /dev/null; then
    echo "✅ ConfigMap 'dalti-frontend-nginx-conf' exists"
else
    echo "❌ ConfigMap 'dalti-frontend-nginx-conf' does not exist"
    exit 1
fi

# Check Deployment
echo "📋 Checking Deployment..."
if kubectl get deployment dalti-frontend -n dalti &> /dev/null; then
    echo "✅ Deployment 'dalti-frontend' exists"
    
    # Check if deployment is ready
    READY_REPLICAS=$(kubectl get deployment dalti-frontend -n dalti -o jsonpath='{.status.readyReplicas}')
    DESIRED_REPLICAS=$(kubectl get deployment dalti-frontend -n dalti -o jsonpath='{.spec.replicas}')
    
    if [ "$READY_REPLICAS" = "$DESIRED_REPLICAS" ]; then
        echo "✅ Deployment is ready ($READY_REPLICAS/$DESIRED_REPLICAS replicas)"
    else
        echo "⚠️  Deployment not fully ready ($READY_REPLICAS/$DESIRED_REPLICAS replicas)"
    fi
else
    echo "❌ Deployment 'dalti-frontend' does not exist"
    exit 1
fi

# Check Service
echo "📋 Checking Service..."
if kubectl get service dalti-frontend-service -n dalti &> /dev/null; then
    echo "✅ Service 'dalti-frontend-service' exists"
else
    echo "❌ Service 'dalti-frontend-service' does not exist"
    exit 1
fi

# Check Ingress
echo "📋 Checking Ingress..."
if kubectl get ingress dalti-frontend-ingress -n dalti &> /dev/null; then
    echo "✅ Ingress 'dalti-frontend-ingress' exists"
    
    # Check if ingress has an IP
    INGRESS_IP=$(kubectl get ingress dalti-frontend-ingress -n dalti -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [ -n "$INGRESS_IP" ]; then
        echo "✅ Ingress has IP: $INGRESS_IP"
    else
        echo "⚠️  Ingress does not have an IP assigned yet"
    fi
else
    echo "❌ Ingress 'dalti-frontend-ingress' does not exist"
    exit 1
fi

# Check Pods
echo "📋 Checking Pods..."
PODS=$(kubectl get pods -n dalti -l app=dalti-frontend --no-headers)
if [ -n "$PODS" ]; then
    echo "✅ Frontend pods found:"
    kubectl get pods -n dalti -l app=dalti-frontend
    
    # Check pod status
    RUNNING_PODS=$(kubectl get pods -n dalti -l app=dalti-frontend --field-selector=status.phase=Running --no-headers | wc -l)
    TOTAL_PODS=$(kubectl get pods -n dalti -l app=dalti-frontend --no-headers | wc -l)
    
    if [ "$RUNNING_PODS" = "$TOTAL_PODS" ]; then
        echo "✅ All pods are running ($RUNNING_PODS/$TOTAL_PODS)"
    else
        echo "⚠️  Not all pods are running ($RUNNING_PODS/$TOTAL_PODS)"
    fi
else
    echo "❌ No frontend pods found"
    exit 1
fi

# Test health endpoint
echo "📋 Testing health endpoint..."
if command -v curl &> /dev/null; then
    if curl -f -s https://dalti-admin.adscloud.org/health &> /dev/null; then
        echo "✅ Health endpoint is responding"
    else
        echo "⚠️  Health endpoint is not responding (this might be normal if DNS/TLS is not yet configured)"
    fi
else
    echo "⚠️  curl not available, skipping health check"
fi

echo ""
echo "🎉 Deployment verification completed!"
echo "📝 Summary:"
echo "   - Namespace: ✅"
echo "   - ConfigMap: ✅"
echo "   - Deployment: ✅"
echo "   - Service: ✅"
echo "   - Ingress: ✅"
echo "   - Pods: ✅"
echo ""
echo "🌐 Your application should be available at: https://dalti-admin.adscloud.org"
echo "🔧 To check logs: kubectl logs -n dalti -l app=dalti-frontend"
echo "🔍 To check events: kubectl get events -n dalti --sort-by='.lastTimestamp'"

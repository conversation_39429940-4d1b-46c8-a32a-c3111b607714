# Field Mapping Fixes for Provider Categories

This document outlines the fixes applied to resolve the field mapping mismatch between the API response and the component expectations.

## Issue

The API was returning categories with `title` field, but the components were expecting `name` field. This caused categories to display as empty or with missing names.

**API Response Structure:**
```json
{
  "id": 1,
  "title": "Healthcare",
  "parentId": null,
  "imageId": null,
  "_count": {
    "providers": 0,
    "children": 6
  }
}
```

**Component Expected Structure:**
```typescript
interface ProviderCategory {
  id: string;
  name: string; // ❌ Expected 'name' but API returns 'title'
  // ...
}
```

## Solution

Updated all components and types to use `title` field to match the actual API response structure.

## Files Modified

### 1. **Type Definitions** (`src/types/api.ts`)
- Updated `ProviderCategory` interface to use `title` instead of `name`
- Updated `CreateCategoryRequest` and `UpdateCategoryRequest` interfaces
- Added `_count` field to match API response structure
- Made fields optional where appropriate to match API response

### 2. **Components Updated**

#### **CategoryTreeView** (`src/components/categories/CategoryTreeView.tsx`)
- Updated display logic to use `category.title`
- Updated search filtering to use `category.title`
- Updated icon generation logic
- Updated provider count to use `category._count?.providers`
- Updated mock data to use correct field structure

#### **CategoryForm** (`src/components/categories/CategoryForm.tsx`)
- Updated form schema to use `title` field
- Updated form registration and validation
- Updated form submission data mapping
- Updated form labels and placeholders

#### **CategoriesPage** (`src/pages/Categories/CategoriesPage.tsx`)
- Updated category display to use `selectedCategory.title`
- Updated deletion confirmation dialog
- Updated provider count calculations

#### **CategoryValidation** (`src/components/categories/CategoryValidation.tsx`)
- Updated validation logic to use `cat.title`
- Updated error messages and validation rules

#### **CategoryDeletionDialog** (`src/components/categories/CategoryDeletionDialog.tsx`)
- Updated category name references to use `category.title`
- Updated archive functionality

### 3. **Services Updated**

#### **AdminAPI** (`src/services/adminApi.ts`)
- Updated provider transformation to use `title` field
- Maintained backward compatibility where possible

### 4. **Utilities Updated**

#### **CategorySafety** (`src/utils/categorySafety.ts`)
- Updated duplicate name detection to use `category.title`

#### **ProviderSearchFilters** (`src/components/providers/ProviderSearchFilters.tsx`)
- Updated category dropdown to display `category.title`
- Updated fallback mock data structure

## Key Changes Summary

### Field Mappings
- `category.name` → `category.title`
- `category.providerCount` → `category._count?.providers || category.providerCount`
- Added support for `category._count.children`

### Form Updates
- Form schema validation updated for `title` field
- Form registration changed from `register('name')` to `register('title')`
- Error handling updated for `errors.title`

### Display Updates
- All category name displays now use `category.title`
- Provider count displays now use `category._count?.providers` with fallback
- Search and filtering updated to use `title` field

### API Compatibility
- Maintained backward compatibility where possible
- Added fallback logic for missing fields
- Updated request/response type definitions

## Result

✅ **Categories now display correctly** with proper titles  
✅ **Provider counts show accurate data** from API response  
✅ **Search and filtering work properly** with title field  
✅ **Form submission uses correct field names** for API  
✅ **All TypeScript errors resolved**  
✅ **Backward compatibility maintained** where possible  

## Testing

The changes have been tested to ensure:
- Categories display with correct titles
- Provider counts show accurate numbers
- Search functionality works with title field
- Form creation/editing uses correct API fields
- No TypeScript compilation errors
- All components render without console errors

## API Response Structure Now Supported

```typescript
interface ProviderCategory {
  id: string;
  title: string;                    // ✅ Matches API response
  description?: string;
  parentId?: string | null;
  imageId?: string | null;
  image?: FileUploadResponse | null;
  _count?: {                        // ✅ Matches API response
    providers: number;
    children: number;
  };
  children?: ProviderCategory[];
  parent?: ProviderCategory | null;
  // ... other fields
}
```

The categories widget should now display correctly with proper category titles and accurate provider counts.

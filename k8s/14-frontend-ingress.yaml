apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dalti-frontend-ingress
  namespace: dalti
  annotations:
    # Use the same annotations as your API ingress if needed (e.g., rewrite, SSL)
    nginx.ingress.kubernetes.io/rewrite-target: / # Optional: Usually needed if service path differs
    # nginx.ingress.kubernetes.io/ssl-redirect: "true"
    # cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: nginx
  # tls: # Add TLS config if needed, matching the API ingress
  # - hosts:
  #   - dalti-admin.adscloud.org
  #   secretName: dalti-frontend-tls-secret # Use a separate or wildcard cert
  rules:
  - host: dalti-admin.adscloud.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: dalti-frontend-service # Points to the Nginx frontend Service
            port:
              number: 80 
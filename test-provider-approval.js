// Test script to verify provider approval API
// This script can be run in the browser console to test the API

async function testProviderApproval() {
  try {
    console.log('🧪 Testing Provider Approval API...');
    
    // Get the auth token from localStorage
    const token = localStorage.getItem('dalti_admin_token');
    if (!token) {
      console.error('❌ No auth token found. Please login first.');
      return;
    }

    // Test API base URL (adjust if needed)
    const API_BASE = 'https://dapi-test.adscloud.org:8443';
    
    // First, get a list of providers to find one to test with
    console.log('📋 Fetching providers list...');
    const providersResponse = await fetch(`${API_BASE}/api/auth/admin/providers?page=1&limit=5`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!providersResponse.ok) {
      throw new Error(`Failed to fetch providers: ${providersResponse.status} ${providersResponse.statusText}`);
    }

    const providersData = await providersResponse.json();
    console.log('✅ Providers fetched:', providersData);

    if (!providersData.providers || providersData.providers.length === 0) {
      console.log('⚠️ No providers found to test with');
      return;
    }

    // Get the first provider for testing
    const testProvider = providersData.providers[0];
    console.log(`🎯 Testing with provider: ${testProvider.title} (ID: ${testProvider.id})`);

    // Test the approval API endpoint
    console.log('🔄 Testing provider approval...');
    const approvalResponse = await fetch(`${API_BASE}/api/auth/admin/providers/${testProvider.id}/status`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        isVerified: true,
        reason: 'Test approval from admin panel - API verification'
      })
    });

    if (!approvalResponse.ok) {
      throw new Error(`Failed to approve provider: ${approvalResponse.status} ${approvalResponse.statusText}`);
    }

    const approvalData = await approvalResponse.json();
    console.log('✅ Provider approval successful:', approvalData);

    // Test rejection as well
    console.log('🔄 Testing provider rejection...');
    const rejectionResponse = await fetch(`${API_BASE}/api/auth/admin/providers/${testProvider.id}/status`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        isVerified: false,
        reason: 'Test rejection from admin panel - API verification'
      })
    });

    if (!rejectionResponse.ok) {
      throw new Error(`Failed to reject provider: ${rejectionResponse.status} ${rejectionResponse.statusText}`);
    }

    const rejectionData = await rejectionResponse.json();
    console.log('✅ Provider rejection successful:', rejectionData);

    console.log('🎉 All provider approval API tests passed!');
    
    return {
      success: true,
      approvalData,
      rejectionData
    };

  } catch (error) {
    console.error('❌ Provider approval API test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Export for use
if (typeof window !== 'undefined') {
  window.testProviderApproval = testProviderApproval;
  console.log('🔧 Test function loaded. Run testProviderApproval() to test the API.');
}

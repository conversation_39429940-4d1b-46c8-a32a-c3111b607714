// Test script to verify provider detail API
// This script can be run in the browser console to test the API

async function testProviderDetail() {
  try {
    console.log('🧪 Testing Provider Detail API...');
    
    // Get the auth token from localStorage
    const token = localStorage.getItem('dalti_admin_token');
    if (!token) {
      console.error('❌ No auth token found. Please login first.');
      return;
    }

    // Test API base URL (adjust if needed)
    const API_BASE = 'https://dapi-test.adscloud.org:8443';
    
    // Test with provider ID 1 (from your example)
    const providerId = '1';
    
    console.log(`🎯 Testing provider detail for ID: ${providerId}`);

    // Test the provider detail API endpoint
    console.log('🔄 Fetching provider details...');
    const response = await fetch(`${API_BASE}/api/auth/admin/providers/${providerId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch provider: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ Provider detail API response:', data);

    // Test the transformation logic
    if (data.success && data.data) {
      const rawProvider = data.data;
      console.log('📋 Raw provider data:', rawProvider);
      
      // Simulate the transformation that should happen in the frontend
      const transformedProvider = {
        id: rawProvider.id.toString(),
        name: rawProvider.title,
        email: rawProvider.user?.email || '',
        phone: rawProvider.phone,
        businessTitle: rawProvider.title,
        description: rawProvider.presentation,
        verified: rawProvider.isVerified,
        status: rawProvider.isVerified ? 'approved' : 'pending',
        locationCount: 1,
        serviceCount: rawProvider._count?.services || 0,
        customerCount: rawProvider._count?.customerFolders || 0,
        rating: rawProvider.averageRating || undefined,
        totalRatings: rawProvider.totalReviews || 0,
        createdAt: rawProvider.createdAt,
        updatedAt: rawProvider.updatedAt,
        category: rawProvider.category ? {
          id: rawProvider.category.id.toString(),
          name: rawProvider.category.title,
          description: '',
          isActive: true,
          createdAt: rawProvider.createdAt,
          updatedAt: rawProvider.updatedAt,
        } : undefined,
      };
      
      console.log('🔄 Transformed provider data:', transformedProvider);
      console.log('🎉 Provider detail API test passed!');
      
      return {
        success: true,
        rawData: data,
        transformedData: transformedProvider
      };
    } else {
      throw new Error('Invalid response structure');
    }

  } catch (error) {
    console.error('❌ Provider detail API test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Export for use
if (typeof window !== 'undefined') {
  window.testProviderDetail = testProviderDetail;
  console.log('🔧 Test function loaded. Run testProviderDetail() to test the API.');
}

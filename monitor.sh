#!/bin/bash

# Monitoring Script for Dalti Admin Panel
# This script provides real-time monitoring of the deployment

set -e

echo "📊 Dalti Admin Panel Monitoring Dashboard"
echo "=========================================="

# Function to get pod status
get_pod_status() {
    kubectl get pods -n dalti -l app=dalti-frontend --no-headers 2>/dev/null | awk '{print $3}' | head -1
}

# Function to get deployment status
get_deployment_status() {
    kubectl get deployment dalti-frontend -n dalti -o jsonpath='{.status.readyReplicas}/{.spec.replicas}' 2>/dev/null
}

# Function to check health endpoint
check_health() {
    if command -v curl &> /dev/null; then
        if curl -f -s -m 5 https://dalti-admin.adscloud.org/health &> /dev/null; then
            echo "✅ Healthy"
        else
            echo "❌ Unhealthy"
        fi
    else
        echo "⚠️  curl not available"
    fi
}

# Function to get ingress IP
get_ingress_ip() {
    kubectl get ingress dalti-frontend-ingress -n dalti -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null
}

# Function to get certificate status
get_cert_status() {
    if kubectl get certificate dalti-frontend-tls-secret -n dalti &> /dev/null; then
        kubectl get certificate dalti-frontend-tls-secret -n dalti -o jsonpath='{.status.conditions[0].type}' 2>/dev/null
    else
        echo "Not Found"
    fi
}

# Main monitoring loop
while true; do
    clear
    echo "📊 Dalti Admin Panel Monitoring Dashboard"
    echo "=========================================="
    echo "🕐 Last Updated: $(date)"
    echo ""
    
    echo "🚀 Deployment Status:"
    echo "   Replicas: $(get_deployment_status)"
    echo "   Pod Status: $(get_pod_status)"
    echo ""
    
    echo "🌐 Network Status:"
    echo "   Ingress IP: $(get_ingress_ip)"
    echo "   Health Check: $(check_health)"
    echo "   Certificate: $(get_cert_status)"
    echo ""
    
    echo "📈 Resource Usage:"
    kubectl top pods -n dalti -l app=dalti-frontend 2>/dev/null || echo "   Metrics not available"
    echo ""
    
    echo "📝 Recent Events:"
    kubectl get events -n dalti --field-selector involvedObject.name=dalti-frontend --sort-by='.lastTimestamp' --no-headers 2>/dev/null | tail -3 | while read line; do
        echo "   $line"
    done
    echo ""
    
    echo "🔄 Auto-refresh in 30 seconds... (Ctrl+C to exit)"
    sleep 30
done

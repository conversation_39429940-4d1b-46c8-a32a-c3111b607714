# Dalti Admin Panel Production Environment Configuration

# API Configuration
VITE_API_BASE_URL_PROD=https://dapi.adscloud.org
VITE_NODE_ENV=production

# Application Configuration
VITE_APP_NAME=Dalti Admin Panel
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Comprehensive admin panel for Dalti appointment booking platform

# Security Configuration
VITE_JWT_STORAGE_KEY=dalti_admin_token

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_AUDIT_LOGS=true
VITE_ENABLE_REAL_TIME_UPDATES=true

# Pagination Defaults
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100

# Cache Configuration
VITE_CACHE_DURATION=300000
VITE_ENABLE_CACHING=true

# Dalti Admin Panel Environment Configuration Template
# Copy this file to .env and update the values

# API Configuration
VITE_API_BASE_URL_DEV=https://dapi-test.adscloud.org:8443
VITE_API_BASE_URL_PROD=https://dapi.adscloud.org
VITE_NODE_ENV=development

# Admin Credentials (for development only)
VITE_ADMIN_EMAIL=<EMAIL>
VITE_ADMIN_PASSWORD=your_admin_password

# Application Configuration
VITE_APP_NAME=Dalti Admin Panel
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Comprehensive admin panel for Dal<PERSON> appointment booking platform

# Security Configuration
VITE_JWT_STORAGE_KEY=dalti_admin_token

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_AUDIT_LOGS=true
VITE_ENABLE_REAL_TIME_UPDATES=true

# Pagination Defaults
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100

# Cache Configuration
VITE_CACHE_DURATION=300000
VITE_ENABLE_CACHING=true

#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Default version
VERSION="beta-1.0.0"

# Parse command line options
while getopts v: flag
do
    case "${flag}" in
        v) VERSION=${OPTARG};;
    esac
done

echo "Deploying version: $VERSION"

# --- Frontend Deployment ---
echo "Setting up production environment..."
cp .env.production .env
echo "Production environment configured."

echo "Installing dependencies..."
npm ci
echo "Dependencies installed."

echo "Building frontend for production..."
NODE_ENV=production npm run build
echo "Frontend build complete."

echo "Building frontend Docker image..."
docker build -t kotoubm7/dalti-frontend:$VERSION -f Dockerfile.frontend .
echo "Frontend Docker image build complete."

echo "Pushing frontend Docker image..."
docker push kotoubm7/dalti-frontend:$VERSION
echo "Frontend Docker image push complete."

echo "Updating frontend deployment image tag..."
# Note: Assuming the image line looks like 'image: kotoubm7/dalti-frontend:...'
sed -i 's|image: kotoubm7/dalti-frontend:.*|image: kotoubm7/dalti-frontend:'"$VERSION"'|g' k8s/12-frontend-deployment.yaml
echo "Frontend deployment image tag updated."

echo "Deleting existing frontend Kubernetes resources (if any)..."
kubectl delete --ignore-not-found=true -f k8s/
echo "Existing frontend resources deleted (or none found)."

echo "Deploying frontend Kubernetes manifests..."
kubectl apply -f k8s/
echo "Frontend deployment complete."

echo "Deployment finished successfully for version $VERSION!" 